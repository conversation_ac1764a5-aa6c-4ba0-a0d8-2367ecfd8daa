// 验证手机号格式
export function checkMobile(str) {
  let re = /^1\d{10}$/
  if (re.test(str) && str != '') {
    return true;
  }
  return false;
}

// 验证邮箱格式
export function checkEmail(email) {
  var regEmail = /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
  if (email != '' && regEmail.test(email)) {
    return true;
  }
  return false;
}

// 是否为微信浏览器
export function isWeiXin() {
  var ua = window.navigator.userAgent.toLowerCase();
  if(ua.match(/MicroMessenger/i) == 'micromessenger' || ua.match(/Weixin/i) == 'weixin'){
    return true;
  } else{
    return false;
  }
}

// 是否为空
export function isEmpty(value) {
  if (value != "" && value != undefined && value != null) {
    return false;
  }

  return true;
}

// 保留两位小数
export function numFilter(value) {
  return parseFloat(value).toFixed(2);
}

// 小于等于0取0
export function retain(value) {
  let val = numFilter(value);
  return val > 0 ? val : 0;
}

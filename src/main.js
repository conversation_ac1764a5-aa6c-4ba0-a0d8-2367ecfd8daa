// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'
// import $ from 'jquery'
//animate
import animate from 'animate.css'
Vue.use(animate);

// 引用复制方法
import VueClipboard from 'vue-clipboard2'
Vue.use(VueClipboard)

//公共css
import "./assets/css/style.scss"

//视频组件
import "./assets/js/aliplayerComponents.js"


//bootstrap
import { BootstrapVue, IconsPlugin } from 'bootstrap-vue'
import 'bootstrap/dist/css/bootstrap.css'
import 'bootstrap-vue/dist/bootstrap-vue.css'
Vue.use(BootstrapVue)
Vue.use(IconsPlugin)

//element
import ElementUI from 'element-ui';    // （1）
import 'element-ui/lib/theme-chalk/index.css';    // （2）

//swiper
import VueAwesomeSwiper from "vue-awesome-swiper";
import 'swiper/dist/css/swiper.css'
Vue.use(VueAwesomeSwiper);

//less
import less from 'less'
Vue.use(less)

import store from "./store/index";

Vue.config.productionTip = false
Vue.use(ElementUI, { size: 'small', zIndex: 3000 })
// （3）在引入 Element 时，可以传入一个全局配置对象。该对象目前支持 sizenpm i element-ui -S 与 zIndex 字段。size 用于改变组件的默认尺寸，zIndex 设置弹框的初始 z-index（默认值：2000）

//axios
import axios from '../node_modules/axios'
axios.interceptors.request.use(
  config => {
    if (localStorage.getItem('Authorization')) {
      config.headers.Authorization = localStorage.getItem('Authorization');
    }

    return config;
  },
  error => {
    return Promise.reject(error);
  });
Vue.prototype.$axios = axios;


let browser = window.location.hostname;
if (browser == 'www.radirhino.com') {
    Vue.prototype.url = 'https://main.radirhino.com'
} else {
    // Vue.prototype.url = 'https://main.radirhino.com'
    Vue.prototype.url = 'http://127.0.0.1:8082'
}

/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  axios,
  store,
  components: { App },
  template: '<App/>'
})

<template>
  <div class="xg-ny xg-periphery-details">
    <div class="xg-box">
      <!-- 主体 -->
      <div class="main">
        <div class="product">
          <!-- 产品图 -->
          <div class="product-image">
            <swiper
              :options="swiperOption"
              class="image-swiper"
              :key="swiperKey"
            >
              <swiper-slide
                v-for="(image, index) in goodsInfo.images"
                :key="index"
              >
                <img :src="image" />
              </swiper-slide>
            </swiper>
            <!-- 前进后退 -->
            <div class="swiper-button-prev" slot="button-prev"></div>
            <div class="swiper-button-next" slot="button-next"></div>
            <!-- 分页器 -->
            <div class="swiper-pagination" slot="pagination"></div>
          </div>
          <!-- 产品信息 -->
          <div class="info">
            <div class="title">
              {{ goodsInfo.name }}
            </div>

            <div class="tag-box" v-if="goodsInfo.tag_list_info">
              <el-tag
                @click="go(item)"
                class="tag-item"
                v-for="(item, i) in goodsInfo.tag_list_info"
                :key="i"
                >{{ item.tag }}</el-tag
              >
            </div>
            <div class="item item1" v-if="goodsInfo.price > 0">
              <div class="text">价格</div>
              <div class="text2">
                <strong>{{ goodsInfo.price }} 光子</strong>
              </div>
            </div>
            <div class="item item2">
              <div class="text">运费</div>
              <div class="text2" v-if="goodsInfo.postage > 0">
                <strong>{{ goodsInfo.postage }} 光子</strong>
              </div>
              <div class="text2" v-else>包邮</div>
            </div>
            <div class="item item3">
              <div class="text">浏览量</div>
              <div class="text2">{{ goodsInfo.sales_num }}</div>
            </div>
            <div
              class="item item4 pointer"
              v-if="goodsInfo.suggest_user"
              @click="gofans(goodsInfo.suggest_user.id)"
            >
              <div class="text">推荐人</div>
              <div class="avatar2">
                <div class="img-box">
                  <img :src="goodsInfo.suggest_user.avatar" alt="" />
                </div>
                <i
                  class="el-icon-plus fansicon"
                  v-if="goodsInfo.nofans"
                  @click.stop="userGuanzhu()"
                ></i>
              </div>
              <div class="text2">{{ goodsInfo.suggest_user.name }}</div>
            </div>
            <div class="item item4" v-if="goodsInfo.material">
              <div class="protocol">
                <el-checkbox v-model="checked" @change="check"></el-checkbox>
                <span>同意犀光RadiRhino下载声明内容</span>
              </div>
            </div>
            <div style="display: flex" v-if="goodsInfo.material">
              <a
                :href="goodsInfo.material"
                class="xg-btn"
                style="background-color: green; width: 48%"
                v-if="checked"
                >下载素材</a
              >
              <a
                href="#"
                @click.prevent="handleDownloadClick"
                class="xg-btn"
                style="background-color: green; width: 48%"
                v-else
                >下载素材</a
              >
              <div
                class="xg-btn"
                @click="getUrl"
                style="margin-left: 4%; width: 48%"
              >
                立即购买
              </div>
            </div>
            <div class="xg-btn" @click="getUrl" v-else>立即购买</div>
          </div>
        </div>
        <!-- 选项卡 -->
        <el-tabs v-model="activeName" class="tabs">
          <el-tab-pane label="商品详情" name="0">
            <p v-html="goodsInfo.desc"></p>
          </el-tab-pane>
          <el-tab-pane label="评论" name="1">
            <!-- 评论 -->
            <div class="comments">
              <!-- 还未登录的样式 -->
              <div class="noinput" v-if="!user">
                <div class="text">登录后可评论</div>
                <div class="xg-btn" @click="goLogin()">登录</div>
              </div>
              <!-- 登录后的样式 -->
              <div @click="inputFocus" class="my-reply" v-else>
                <div
                  tabindex="0"
                  contenteditable="true"
                  id="replyInput"
                  spellcheck="false"
                  placeholder="输入评论..."
                  class="reply-input"
                  @input="onDivInput($event)"
                ></div>
                <div class="reply-btn-box">
                  <el-button
                    class="reply-btn"
                    size="medium"
                    @click="sendComment"
                    type="primary"
                    >发表</el-button
                  >
                </div>
              </div>
            </div>
            <!-- 评论列表 -->
            <div class="itembox">
              <div class="item" v-for="(item, index) in comments" :key="index">
                <div class="avatar">
                  <img :src="item.user.avatar" alt="" />
                </div>
                <div class="desc">
                  <div class="name">
                    {{ item.user.name || "匿名" }}
                  </div>
                  <div class="text">
                    {{ item.content }}
                  </div>
                  <div class="time">{{ item.created_at }}</div>
                </div>
              </div>
            </div>
            <!-- 分页 -->
            <el-pagination
              background
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page.sync="offset"
              :page-size="limit"
              layout="prev, pager, next"
              :total="total"
              class="pagination"
            >
            </el-pagination>
          </el-tab-pane>
        </el-tabs>
      </div>
      <!-- 推荐 -->
      <div class="recommended">
        <div class="title">相关推荐</div>
        <div class="itembox">
          <div class="item" v-for="(item, index) in recommends" :key="index">
            <div class="xg-image image" @click="jump(item.id)">
              <img :src="item.cover" alt="" />
            </div>
            <div class="bottom">
              <div class="xg-line-1 text">{{ item.name }}</div>
              <div class="price">￥{{ item.price }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog title="下载声明" :visible.sync="centerDialogVisible" center>
      <p v-html="downloadContent" style="height: 60%"></p>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="centerDialogVisible = false"
          >关 闭</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "xg-details",
  data() {
    return {
      activeName: "0",
      // 数量
      number: 1,
      goodsInfo: {},
      recommends: [],
      // 产品图片 swiper 配置
      swiperOption: {
        spaceBetween: 20, // swiper-solid 间距
        autoHeight: true, //高度随内容变化
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
        // allowTouchMove: false,// 禁止滑动
        preventClicks: false, //默认true
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
        },
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
      },
      swiperKey: "",
      // 用户数据
      user: {},

      // 评论
      comments: [],
      // 评论分页
      limit: 15,
      offset: 1,
      total: 0,

      centerDialogVisible: false,
      checked: false,
      downloadContent: "",
      downloadUrl: "",
    };
  },
  components: {},
  beforeMount() {
    this.getInfo();
    this.getComment();
    this.getDownloadContent();
    // 获取本地存储用户数据
    this.user = JSON.parse(localStorage.getItem("UserInfo"));
  },
  methods: {
    gofans(uid) {
      this.$router.push("/userfans/" + uid);
    },
    userGuanzhu(type = 1) {
      this.$axios
        .get(
          this.url + "/api/userGuanzhu/" + this.goodsInfo.suggest_user.id + "?type=" + type
        )
        .then((res) => {
          console.log(res, "res--------");
          if (res.data.code !== 200) {
            this.$message.error(res.data.msg);
          } else {
            this.goodsInfo.nofans = false;
            this.$message.success(res.data.msg);
          }
        })
        .catch((err) => {});
    },
    go(item) {
      this.$router.push("/search?tag=" + item.id + "&type=goods");
    },
    handleDownloadClick() {
      this.$message.warning("请先同意下载声明后操作");
    },

    handleSizeChange(val) {
      this.limit = val;
    },
    handleCurrentChange(val) {
      let page = val - 1;
      this.getComment(page);
    },

    //  跳转
    jump(id) {
      if (id && this.$route.params.id != id) {
        this.$router.push({ name: "peripheryDetails", params: { id: id } });
      }
    },

    getInfo() {
      var id = this.$route.params.id;
      if (id) {
        this.$axios
          .get(this.url + "/api/goods/" + id, {
            params: {},
          })
          .then((res) => {
            if (res.data.code === 200) {
              this.goodsInfo = res.data.data.goods;
              this.recommends = res.data.data.recommends;
              this.swiperKey = res.data.data.recommends[0].name;
            } else {
              this.$message.error({
                showClose: true,
                message: res.data.msg,
              });
              this.$router.go(-1);
            }
          })
          .catch((err) => {});
      }
    },
    // 登录方法
    goLogin() {
      localStorage.removeItem("UserInfo");
      localStorage.removeItem("Authorization");
      this.$router.push({ name: "login" });
    },
    // 设置输入框焦点
    inputFocus() {
      var replyInput = document.getElementById("replyInput");
      replyInput.focus();
    },
    // 发表评论
    sendComment() {
      let AtKey =
        new Date().getFullYear() +
        "-" +
        new Date().getMonth() +
        "-" +
        new Date().getDate();
      let count = Number(localStorage.getItem(AtKey));
      let val = count + 1;
      if (count >= 10) {
        this.$message({
          showClose: true,
          type: "warning",
          message: "评论太过频繁",
        });
        return;
      }

      var course_id = this.$route.params.id;
      let user = JSON.parse(localStorage.getItem("UserInfo"));
      if (user && course_id) {
        if (!this.replyComment) {
          this.$message({
            showClose: true,
            type: "warning",
            message: "评论不能为空",
          });
        } else {
          let input = document.getElementById("replyInput");
          let timeNow = new Date().getTime();
          let time = this.dateStr(timeNow);
          this.$axios
            .post(this.url + "/api/comments", {
              content: this.replyComment,
              target_id: course_id,
              target_type: "goods",
            })
            .then((res) => {
              if (res.data.code === 200) {
                localStorage.setItem(AtKey, val);

                this.comments.unshift({
                  user: {
                    name: user.name,
                    avatar: user.avatar,
                  },
                  content: this.replyComment,
                  created_at: time,
                });
                this.replyComment = "";
                input.innerHTML = "";
                this.$message.success({
                  showClose: true,
                  message: "评论成功",
                });
              } else if (res.data.code === 4001) {
                localStorage.removeItem("UserInfo");
                localStorage.removeItem("Authorization");
                this.$router.push({ name: "login" });
              } else {
                this.$message.warning({
                  showClose: true,
                  message: res.data.msg,
                });
              }
            })
            .catch((err) => {
              this.comments = [];
            });
        }
      } else {
        localStorage.removeItem("UserInfo");
        localStorage.removeItem("Authorization");
        this.$router.push({ name: "login" });
      }
    },
    onDivInput: function (e) {
      this.replyComment = e.target.innerHTML;
    },
    dateStr(date) {
      //获取js 时间戳
      var time = new Date().getTime();
      //去掉 js 时间戳后三位，与php 时间戳保持一致
      time = parseInt((time - date) / 1000);
      //存储转换值
      var s;
      if (time < 60 * 10) {
        //十分钟内
        return "刚刚";
      } else if (time < 60 * 60 && time >= 60 * 10) {
        //超过十分钟少于1小时
        s = Math.floor(time / 60);
        return s + "分钟前";
      } else if (time < 60 * 60 * 24 && time >= 60 * 60) {
        //超过1小时少于24小时
        s = Math.floor(time / 60 / 60);
        return s + "小时前";
      } else if (time < 60 * 60 * 24 * 30 && time >= 60 * 60 * 24) {
        //超过1天少于30天内
        s = Math.floor(time / 60 / 60 / 24);
        return s + "天前";
      } else {
        //超过30天ddd
        var date = new Date(parseInt(date));
        return (
          date.getFullYear() +
          "/" +
          (date.getMonth() + 1) +
          "/" +
          date.getDate()
        );
      }
    },

    //获取评论
    getComment(offset = 0) {
      var id = this.$route.params.id;
      if (id) {
        this.$axios
          .get(this.url + "/api/comments", {
            params: {
              target_id: id,
              target_type: "goods",
              limit: this.limit,
              offset: offset,
            },
          })
          .then((res) => {
            if (res.data.code === 200) {
              this.comments = res.data.data.comments;
              this.total = res.data.data.total;
            }
          })
          .catch((err) => {
            this.comments = [];
          });
      }
    },

    //跳转链接
    getUrl() {
      let goods_id = this.$route.params.id;
      if (goods_id) {
        // product_type：1-课程，2-插件，3素材
        this.$router.push({
          path: "/payment",
          query: { product_id: goods_id, product_type: 3 },
        });

        // this.$axios.post(this.url + "/api/create-order", {
        //     type: 3,
        //     id: goods_id,
        //     num:this.number
        //   })
        //   .then((res) => {
        //     if (res.data.code === 200) {
        //       this.$router.push({
        //         path: "/payment",
        //         query: { order_no: res.data.data.order_no },
        //       });
        //     } else if (res.data.code === 4001) {
        //       localStorage.removeItem("UserInfo");
        //       localStorage.removeItem("Authorization");
        //       this.$router.push({ name: "login" });
        //     } else {
        //       this.$message.warning({
        //         showClose: true,
        //         message: res.data.msg,
        //       });
        //     }
        //   })
        //   .catch((err) => {});
      }
    },

    // 获取下载声明
    getDownloadContent() {
      this.$axios
        .get(this.url + "/api/download_statement", {
          params: {},
        })
        .then((res) => {
          this.downloadContent = res.data.data;
        })
        .catch((err) => {});
    },

    check(event) {
      if (event) {
        this.centerDialogVisible = true;
      } else {
        this.centerDialogVisible = false;
      }
    },
  },
  watch: {
    $route: {
      handler(route) {
        this.getInfo();
        this.getComment();
      },
    },
  },
};
</script>

<style lang="scss" scoped>
.pointer{
  
    cursor: pointer;
}
.avatar2 {
  display: inline-block;
  width: 40px;
  height: 40px;
  position: relative;
  margin-right: 10px;

  .img-box {
    width: 40px;
    height: 40px;
    border-radius: 100%;
    overflow: hidden;
    img {
      width: 40px;
      height: auto;
    }
  }
  .fansicon {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 15px;
    height: 15px;
    border: #000;
    background-color: #0d6efd;
    text-align: center;
    line-height: 14px;
    color: #ffffff;
    font-weight: 700;
    border-radius: 35px;
  }
}
.tag-box {
  text-align: left;
  .tag-item {
    margin: 5px 20px 5px 0;
    cursor: pointer;
  }
}
.xg-periphery-details {
  .xg-box {
    display: flex;
    align-items: stretch;
  }
  .main {
    width: 74%;
    margin-right: 3%;
  }
  .product {
    display: flex;
    align-items: flex-start;
    margin-bottom: 25px;
    .product-image {
      width: 400px;
      flex-shrink: 0;
      margin-right: 30px;
    }

    .info {
      min-width: 0;
      flex-grow: 1;
      .title {
        font-size: 18px;
        color: #000;
        line-height: 1.6;
        font-weight: bold;
        margin-bottom: 20px;
      }
      .item {
        display: flex;
        align-items: center;
        height: 50px;
        line-height: 50px;
        font-size: 14px;
        border-bottom: 1px solid #c9c9c9;
        color: #818e9d;
        text-align: left;
        padding-left: 12px;
        padding-right: 12px;
        .text {
          width: 60px;
          flex-shrink: 0;
          margin-right: 10px;
        }
        .text2 {
          color: #4f5760;
          font-weight: bold;
          strong {
            font-size: 22px;
          }
        }
        &.item1 {
          .text2 {
            color: #ff0000;
          }
          background: #e9eaec;
          border-bottom: 0;
        }
        &.item2 {
        }
        &.item3 {
        }
        &.item4 {
          .text2 {
            color: #0d6efd;
          }
        }
      }
      .number {
        border-bottom: 0;
        .text {
        }
        .input {
          display: flex;
          align-items: center;
          /deep/ .el-input__inner {
            width: 40px;
            height: 31px;
            line-height: 31px;
            border-radius: 0;
            border: 1px solid #a7a6ab;
            margin-right: 3px;
            padding: 0 5px;
            background: none;
          }
        }
        .option {
          margin-right: 8px;
        }
        .add,
        .reduce {
          cursor: pointer;
          outline: none;
          width: 18px;
          height: 14px;
          line-height: 14px;
          border: 1px solid #a7a6ab;
          display: flex;
          align-items: center;
          justify-content: center;
          &:after {
            content: "";
            display: block;
            width: 3px;
            height: 3px;
            border-top: 1px solid #000;
          }
        }
        .add {
          margin-bottom: 3px;
          &::after {
            border-right: 1px solid #000;
            transform: rotate(-45deg);
          }
        }
        .reduce {
          &::after {
            border-left: 1px solid #000;
            transform: rotate(-135deg);
          }
        }
        .text2 {
          font-size: 12px;
          color: #4f5760;
        }
      }

      .xg-btn {
        background: #0084ff;
        font-size: 16px;
        color: #fff;
        width: 100%;
        height: 50px;
        line-height: 50px;
        text-align: center;
        margin-top: 20px;
        cursor: pointer;
      }
    }
  }

  // 选项卡
  .tabs /deep/ {
    .el-tabs__item {
      height: 50px;
      line-height: 50px;
      text-align: 0;
      padding: 0;
      margin-right: 0;
      min-width: 140px;
      &.is-active {
        color: #fff !important;
        background: #4886fd;
        font-size: 14px;
      }
    }
    .el-tabs__active-bar {
      display: none;
    }
    .el-tabs__nav-scroll {
      background: #e9eaec;
    }
    .el-tab-pane {
      background: #fff;
      padding: 45px 35px 50px;
    }
    .el-tabs__header {
      margin-bottom: 0;
    }
    .itembox {
    }
    .item {
      padding-top: 25px;
      padding-bottom: 25px;
      border-bottom: 1px solid #efefef;
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
      text-align: justify;
      .avatar {
        display: block;
        width: 60px;
        height: 60px;
        border-radius: 100%;
        overflow: hidden;
        margin-right: 14px;
        background: #fff;
        flex-shrink: 0;
        img {
          width: 100%;
        }
      }
      .desc {
      }
      .name {
        color: #959ea7;
        font-size: 14px;
        line-height: 1.2;
        margin-bottom: 10px;
      }
      .text {
        font-size: 15px;
        color: #000;
        line-height: 1.6;
        text-align: justify;
      }
      .time {
        font-size: 14px;
        color: #959ea7;
        margin-top: 5px;
      }
      .reply {
        font-size: 14px;
        color: #4886fd;
        line-height: 1.6;
        text-align: justify;
        margin-top: 10px;
      }
    }
    .pagination {
      justify-content: center;
      margin-top: 46px;
      .btn-prev,
      .btn-next,
      .el-pager li {
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        padding: 0;
        border: 1px solid #d1d4d7;
        font-size: 12px;
        color: #999;
        border-radius: 0;
        background: none;
        &:hover {
          color: #4886fd;
          background: #dfe2e6;
          border-color: #dfe2e6;
        }
        &.active {
          color: #4886fd;
          background: none !important;
        }
      }
    }
  }
  // 推荐
  .recommended {
    border-left: 1px solid #dadce1;
    width: 23%;
    padding-left: 30px;
    .title {
      font-size: 18px;
      color: #0084ff;
      line-height: 1.2;
      margin-bottom: 15px;
    }

    .itembox {
      .item {
        cursor: pointer;
        outline: none;
        margin-bottom: 20px;
        .image {
          width: 100%;
          margin-bottom: 10px;
        }

        .bottom {
          display: flex;
          align-items: center;
          .text {
            flex-grow: 1;
            min-width: 0;
            color: #000;
            font-size: 14px;
            line-height: 1.2;
            padding-right: 20px;
          }

          .price {
            font-size: 14px;
            color: #ff0000;
            font-weight: bold;
            line-height: 1.2;
          }
        }
      }
    }
  }

  .product-image /deep/ {
    position: relative;

    img {
      width: 100%;
    }
    .swiper-button-prev,
    .swiper-button-next {
      background-image: none;
      border-width: 1px;
      border-top-style: solid;
      border-color: #96a1ac;
      position: absolute;
      top: 50%;
      width: 30px;
      height: 30px;
    }
    .swiper-button-prev {
      border-left-style: solid;
      transform: rotate(-45deg) translateY(-50%);
      left: 35px;
    }
    .swiper-button-next {
      border-right-style: solid;
      transform: rotate(45deg) translateY(-50%);
      right: 35px;
    }
    .swiper-pagination {
      font-size: 0;
      bottom: 12px;
      position: absolute;
      left: 0;
      width: 100%;
      z-index: 99;
      display: flex;
      align-items: center;
      justify-content: center;
      .swiper-pagination-bullet {
        width: 10px;
        height: 10px;
        border-radius: 100%;
        background: #96a1ac;
        margin-left: 4px;
        margin-right: 4px;
        opacity: 1;
      }
      .swiper-pagination-bullet-active {
        background: #0d6efd;
      }
    }
  }

  // 评论框样式
  .my-reply {
    .header-img {
      display: inline-block;
      vertical-align: top;
    }
    .reply-input {
      text-align: left;
      min-height: 120px;
      padding: 10px;
      color: #999;
      background-color: #fff;
      border-radius: 5px;
      font-size: 14px;
      border: 1px solid #efefef;
      outline: none;
      &:focus,
      &:focus-visible {
        border: 1px solid #4886fd;
      }
      &:empty:before {
        content: attr(placeholder);
      }
      &:focus:before {
        content: none;
      }
    }
    .reply-btn-box {
      text-align: right;
      padding-top: 20px;
      .reply-btn {
        background-color: #4886fd !important;
        border: 0 !important;
        position: relative;
      }
    }
  }
  // 未登录的样式
  .noinput {
    margin-bottom: 50px;
    width: 100%;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #efefef;
    border-radius: 5px;
    .text {
      color: #4a5764;
      font-size: 14px;
      line-height: 1.2;
      margin-right: 14px;
    }
    .xg-btn {
      width: 100px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      color: #fff;
      font-size: 14px;
      background: #4886fd;
      border-radius: 2px;
      display: block;
      cursor: pointer;
    }
  }
}

@media screen and (min-width: 751px) {
  .xg-periphery-details {
    .tabs /deep/ {
      .pagination {
        .btn-prev,
        .btn-next {
          width: 90px;
          & > i {
            display: none;
          }
        }
        .btn-prev:after {
          content: "上一页";
        }
        .btn-next:after {
          content: "下一页";
        }
      }
    }
  }
}
@media screen and (max-width: 751px) {
  .xg-periphery-details .product-image /deep/ {
    padding: 30px 20px;
    .swiper-pagination {
      bottom: 10px;
      .swiper-pagination-bullet {
        width: 6px;
        height: 6px;
      }
    }
    .swiper-button-prev,
    .swiper-button-next {
      width: 15px;
      height: 15px;
      display: none;
    }
    .swiper-button-prev {
      left: 20px;
    }
    .swiper-button-next {
      right: 20px;
    }
  }
  .xg-periphery-details .xg-box {
    flex-wrap: wrap;
  }
  .xg-periphery-details .main {
    width: 100%;
    margin-right: 0;
  }
  .xg-periphery-details .recommended {
    width: 100%;
  }
  .xg-periphery-details .product {
    flex-wrap: wrap;
    margin-bottom: 20px;
  }
  .xg-periphery-details .product .product-image {
    width: 100%;
    margin-right: 0;
    padding: 0;
  }
  .xg-periphery-details .product .info {
    width: 100%;
  }
  .xg-periphery-details .image-swiper {
    width: 100%;
  }
  .xg-periphery-details .product .info .title {
    font-size: 16px;
    margin-top: 15px;
    margin-bottom: 15px;
  }
  .xg-periphery-details .product .info .item {
    height: 40px;
    line-height: 40px;
    font-size: 12px;
  }
  .xg-periphery-details .product .info .item .text2 strong {
    font-size: 20px;
  }
  .xg-periphery-details .product .info .number .add,
  .xg-periphery-details .product .info .number .reduce {
    display: none;
  }
  .xg-periphery-details .product .info .xg-btn {
    height: 40px;
    line-height: 40px;
    font-size: 12px;
    margin-top: 10px;
  }
  .xg-periphery-details .tabs /deep/ {
    .el-tabs__nav {
      width: 100%;
    }
    .el-tabs__item {
      min-width: inherit;
      width: 50%;
      height: 40px;
      line-height: 40px;
      font-size: 12px;
    }
    .el-tab-pane {
      padding: 20px;
    }
    .item {
      padding-top: 15px;
      padding-bottom: 15px;
      .avatar {
        width: 40px;
        height: 40px;
        margin-right: 10px;
      }
      .name {
        font-size: 13px;
        margin-bottom: 5px;
      }
      .text,
      .time,
      .reply {
        font-size: 12px;
      }
    }
    .pagination {
      margin-top: 20px;

      .btn-prev,
      .btn-next,
      .el-pager li {
        width: 20px;
        line-height: 30px;
        height: 30px;
        min-width: inherit;
        margin-left: 2px;
        margin-right: 2px;
      }
    }
  }
  .xg-periphery-details .recommended {
    border-left: 0;
    padding-left: 0;
    margin-top: 20px;
    .title {
      font-size: 16px;
    }
    .itembox {
      flex-wrap: wrap;
      display: flex;
      .item {
        width: 48%;
        margin-right: 4%;
        margin-bottom: 15px;
        &:nth-child(2n) {
          margin-right: 0;
        }
        .bottom {
          .text {
            font-size: 12px;
            padding-right: 10px;
          }
          .price {
            font-size: 12px;
          }
        }
      }
    }

    .my-reply,
    .noinput {
      margin-bottom: 20px;
    }
  }
}
</style>

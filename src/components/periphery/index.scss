
/* ==================== 内页 - 课程列表 start ==================== */
.xg-list-card {
  // 选项卡样式
  .el-tabs__header {
    margin-bottom: 30px;
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }
  .el-tabs__active-bar {
    display: none;
  }
  .el-tabs__item {
    margin-right: 20px;
    height: auto;
    line-height: inherit;
    padding: 20px !important;
    font-size: 14px;
    color: #000;
    border-radius: 4px;
    text-align: center;
    &:last-child {
      margin-right: 0;
    }
    &.is-active {
      background: #0d6efd;
      color: #fff !important;
    }
  }
  .el-tabs__nav-prev,
  .el-tabs__nav-next {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 23px;
    height: 23px;
    line-height: 23px;
    border-radius: 100%;
    background-color: #fff;
    bottom: auto;
    top: 50%;
    transform: translateY(-50%);
    box-shadow: -3px 4px 7px 0px rgba(0, 0, 0, 0.08);
    &:hover {
      background-color: #0d6efd;
      &:after {
        border-left-color: #fff;
      }
    }
    &:after {
      content: "";
      display: block;
      width: 0;
      height: 0;
      border-top: 4px solid transparent;
      border-left: 4px solid #0d6efd;
      border-bottom: 4px solid transparent;
    }
    & > i {
      display: none;
    }
  }
  .el-tabs__nav-prev {
    &:hover {
      &:after {
        border-right-color: #fff;
      }
    }
    &:after {
      border-left: 0;
      border-right: 4px solid #0d6efd;
    }
  }
  .el-tabs__nav-wrap {
    padding-left: 40px;
    padding-right: 40px;
  }
}
@media screen and (min-width: 751px) {
  .xg-list-card {
  }
}
@media screen and (max-width: 751px) {
  .xg-list-card .el-tabs__header {
    margin-bottom: 20px;
  }
  .xg-list-card .el-tabs__item {
    padding: 10px 15px !important;
    margin-right: 5px;
  }
  .xg-list-card .el-tabs__nav-wrap {
    padding-left: 30px;
    padding-right: 30px;
  }
}
/* ==================== 内页 - 课程列表 end ==================== */
/* ==================== 内页 - 周边列表 start ==================== */
.xg-ny-periphery {
    .item{cursor: pointer;outline: none;}
    .image{border-radius:6px;}
    .info{margin-top:15px;margin-bottom:10px;display:flex;align-items:center;justify-content:space-between;}
    .price{font-size:14px;color:#ff0000;line-height:1.2;
        span{font-weight:bold;font-size:18px;}
    }
    .count{flex-shrink:0;line-height:1.2;color:#818e9d;font-size:12px;}
    .name{font-size:16px;line-height:1.8;color:#000;text-align:justify;height:3.6em;}
}

@media screen and (min-width: 751px) {
  .xg-ny-periphery {
    .item:nth-child(4)~.item{margin-top:45px;}
  }
}

@media screen and (max-width: 751px) {
    .xg-ny-periphery .price span{font-size:16px;}
    .xg-ny-periphery .info{margin-top:10px;margin-bottom:5px;}
    .xg-ny-periphery .name{font-size:12px;line-height:1.6em;height:3.2em;}
}

/* ==================== 内页 - 周边列表 end ==================== */

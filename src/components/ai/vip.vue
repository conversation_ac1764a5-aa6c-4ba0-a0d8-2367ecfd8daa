<template>
  <div class="content" v-loading="loading">
    <div class="title">
      <div class="t-top">
        <span>{{info.title_pre}}</span> <span>{{info.title_suff}}</span>
      </div>
      <div class="t-bottom">
        <span>{{info.title_sub1}}</span>
        <span class="xg">/</span>
        <span>{{info.title_sub2}}</span>
        <span class="xg">/</span>
        <span>{{info.title_sub3}}</span>
      </div>
    </div>
    <div class="card-content">
      <div class="card" style="color:#4f5461;background:#f6f7f7;">
        <div class="card-bg" style="background: #dbdee0"><span></span></div>
        <!-- <div class="card-bgl">618特惠</div> -->
        <div class="card-head">
          <p class="title">月度VIP</p>
        </div>
        <div class="card-middle">
          <!-- <p class="original-price">原价：￥59.00元</p> -->
          <p><span >¥</span><span class="price">{{info.vip_price1}}光子</span><span style="color: #7d858f;">/月</span></p>
          <p class="title-limit">{{info.vip1_sub}}</p>
          <div class="btn" style="background:#4f5461" @click="buyComfirm(info.vip_price1,'月度')">购买</div>
        </div>
        <div class="card-bottom">
          <p class="bottom-text">
            <span class="text">{{info.vip1_suff1}}</span>
          </p>
          <p class="bottom-text">
            <span class="text">{{info.vip1_suff2}}</span>
          </p>
          <p class="bottom-text">
            <span class="text">{{info.vip1_suff3}}</span>
          </p>
          <p class="bottom-text">
            <span class="text">{{info.vip1_suff4}}</span>
          </p>
        </div>
      </div>
      <div class="card" style="color:#5380d9;background:#eff2fd;">
        <div class="card-bg" style="background: #c5d9f7"><span></span></div>
        <!-- <div class="card-bgl">618特惠</div> -->
        <div class="card-head">
          <p class="title">季度VIP</p>
        </div>
        <div class="card-middle">
          <!-- <p class="original-price">原价：￥59.00元</p> -->
          <p><span >¥</span><span class="price">{{info.vip_price2}}光子</span><span style="color: #7d858f;">/季</span></p>
          <p class="title-limit">{{info.vip2_sub}}</p>
          <div class="btn" style="background:#5380d9" @click="buyComfirm(info.vip_price2,'季度')">购买</div>
        </div>
        <div class="card-bottom">
          <p class="bottom-text">
            <span class="text">{{info.vip2_suff1}}</span>
          </p>
          <p class="bottom-text">
            <span class="text">{{info.vip2_suff2}}</span>
          </p>
          <p class="bottom-text">
            <span class="text">{{info.vip2_suff3}}</span>
          </p>
          <p class="bottom-text">
            <span class="text">{{info.vip2_suff4}}</span>
          </p>
        </div>
      </div>
      <div class="card" style="color:#56b73a;background:#f1fffa;">
        <div class="card-bg" style="background: #d7f4e7"><span></span></div>
        <!-- <div class="card-bgl">618特惠</div> -->
        <div class="card-head">
          <p class="title">年度VIP</p>
        </div>
        <div class="card-middle">
          <!-- <p class="original-price">原价：￥59.00元</p> -->
          <p><span >¥</span><span class="price">{{info.vip_price3}}光子</span><span style="color: #7d858f;">/年</span></p>
          <p class="title-limit">{{info.vip3_sub}}
          <div class="btn" style="background:#56b73a" @click="buyComfirm(info.vip_price3,'年度')">购买</div>
        </div>
        <div class="card-bottom">
          <p class="bottom-text">
            <span class="text">{{info.vip3_suff1}}</span>
          </p>
          <p class="bottom-text">
            <span class="text">{{info.vip3_suff2}}</span>
          </p>
          <p class="bottom-text">
            <span class="text">{{info.vip3_suff3}}</span>
          </p>
          <p class="bottom-text">
            <span class="text">{{info.vip3_suff4}}</span>
          </p>
        </div>
      </div>
      <div class="card" style="color:#3b434a;background: #1a202a;">
        <div class="card-bg" style="background: #303840"><span></span></div>
        <!-- <div class="card-bgl">618特惠</div> -->
        <div class="card-head">
          <p class="title" style="color: #fff;">永久VIP</p>
        </div>
        <div class="card-middle">
          <!-- <p class="original-price">原价：￥59.00元</p> -->
          <p style="color: #fff;"><span >¥</span><span class="price">{{info.vip_price4}}光子</span><span style="color: #7d858f;">/永久</span></p>
          <p class="title-limit">{{info.vip4_sub}}</p>
          <div class="btn" style="background:#3b434a" @click="buyComfirm(info.vip_price4, '永久')">购买</div>
        </div>
        <div class="card-bottom">
          <p class="bottom-text">
            <span class="text">{{info.vip4_suff1}}</span>
          </p>
          <p class="bottom-text">
            <span class="text">{{info.vip4_suff2}}</span>
          </p>
          <p class="bottom-text">
            <span class="text">{{info.vip4_suff3}}</span>
          </p>
          <p class="bottom-text">
            <span class="text">{{info.vip4_suff4}}</span>
          </p>
        </div>
      </div>
    </div>
    <div class="content-bottom">
      <div class="b-t">
        <span>{{info.bottom_title_pre}}</span> <span>{{info.bottom_title_suff}}</span>
      </div>
      <div class="b-list">
        <div class="hexagon">
          <div class="hexagon-text">{{info.title_tag1}}</div>
        </div>
        <div class="hexagon">
          <div class="hexagon-text">{{info.title_tag2}}</div>
        </div>
        <div class="hexagon">
          <div class="hexagon-text">{{info.title_tag3}}</div>
        </div>
        <div class="hexagon">
          <div class="hexagon-text">{{info.title_tag4}}</div>
        </div>
        <div class="hexagon">
          <div class="hexagon-text">{{info.title_tag5}}</div>
        </div>
        <div class="hexagon">
          <div class="hexagon-text">{{info.title_tag6}}</div>
        </div>
      </div>
    </div>
    <el-dialog
      title="温馨提示"
      :visible.sync="buyDialog"
      width="25%"
      :center="dialogText"
      top="30vh"
      @close="closeDialog"
      >
      <div style="text-align: center;font-size: 16px;">确定支付{{price}}光子开通{{vipText}}VIP吗</div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="buy">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  // 引入通用 选项卡 组件
  export default {
    name: "vip",
    data() {
      return {
        dialogText:true,
        buyDialog:false,
        price:0,
        vipText:'',
        info:{},
        loading:false
        // galleryData: [],
        // srcList: [],
        // active: 0,
        // activeName: 0,
      };
    },
    props: {

    },
    beforeMount() {
      this.getSetting();
    },
    methods: {
      getSetting() {
        this.loading = true
        this.$axios
          .get(this.url + "/api/ai/vip_setting/",{
            params: {},
          })
          .then((res) => {
            if (res.data.code === 200) {
              this.info = res.data.data;
            }
            this.loading = false
          })
          .catch((err) => {});
      },
      buyComfirm(price, text){
        this.price = price
        this.vipText = text
        this.buyDialog = true
      },
      closeDialog(){
        this.buyDialog = false;
      },
      buy() {
        this.$axios.post(this.url + '/api/ai/buy', {
          price: this.price
        }).then(res => {
          if (res.data.code === 200) {
            this.buyDialog = false;
            let user = JSON.parse(localStorage.getItem("UserInfo"));
            user.vip = res.data.data;
            localStorage.setItem("UserInfo", JSON.stringify(user));
            this.$message.success({
              showClose: true,
              message: res.data.msg,
            });
            setTimeout(() => {
              this.$router.go(0);
            },2000);
          }  else if (res.data.code === 4001) {
            localStorage.removeItem("UserInfo");
            localStorage.removeItem("Authorization");
            this.$message.warning({
              showClose: true,
              message: res.data.msg,
            });
            setTimeout(() => {
              this.$router.push({name:"login"})
            },1000);
          } else {
            this.$message.warning({
              showClose: true,
              message: res.data.msg,
            });
            this.closeDialog();
            if (res.data.msg == '光子不足，请充值') {
              setTimeout(() => {
                this.$router.push("/user/integral");
              },2000);
            }
          }
        }).catch(err => {})
      },
    },
    watch: {

    },
    components: {},
  };
</script>

<style lang="scss" scoped>
  /deep/ .el-loading-mask {
    background-color: unset;
  }
.content {
    padding: 20px;
    text-align: center;

    .title {
      padding: 20px;

      .t-top {
        color: #0d6efd;
        font-size: 42px;
        font-weight: bold;
        margin-bottom: 10px;

        span {
          margin: 0 10px;
        }
      }

      .t-bottom {
        font-size: 14px;
        color: #7d858f;

        .xg {
          margin: 0 10px;
        }
      }
    }
    .card-content {
      margin-top: 40px;
      // padding: 0 10%;
      display: flex;
      justify-content: center;
      .card{
        margin: 0 10px;
        position: relative;
        width: 200px;
        overflow: hidden;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 3px 6px rgba(0, 0, 0, .16);
        transition: all .3s;
        &:hover{
          transform: translateY(-20px)
        }
        .card-bg {
          position: absolute;
          top: -728px;
          left: calc(50% - 396px);
          z-index: 0;
          display: flex;
          align-items: flex-end;
          justify-content: center;
          width: 792px;
          height: 792px;
          // background: #ffb5b5;
          border-radius: 50%;
          opacity: .2;
        }
        .card-bgl{
          position: absolute;
          top: 8px;
          right: 8px;
          z-index: 2;
          width: 68px;
          height: 25px;
          font-size: 12px;
          font-weight: 400;
          line-height: 25px;
          color: #fff;
          text-align: center;
          background: #ff8282;
          border-radius: 4px;
        }
        .card-head{
          position: relative;
          // padding: 21px 40px 0;
          overflow: hidden;
          .title{
            position: relative;
            z-index: 1;
            font-size: 16px;
            font-weight: 700;
            line-height: 28px;
            // color: #53cfa1;
            text-align: center;
          }
        }
        .card-middle{
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          margin: 0 0 20px;
          .original-price{
            margin-bottom: 4px;
            font-size: 12px;
            font-weight: 400;
            color: #8d8d8d;
            text-decoration: line-through;
          }
          .price{
            margin-bottom: 0;
            font-size: 18px;
            font-weight: 700;
          }
          .title-limit{
            font-size: 10px;
            font-weight: 400;
            color: #8d8d8d;
          }
          .btn{
            display: flex;
            align-items: center;
            justify-content: center;
            width: 80%;
            height: 36px;
            margin: 0 auto;
            font-size: 16px;
            font-weight: 700;
            color: #fff;
            cursor: pointer;
            border-radius: 18px;
          }
        }
        .card-bottom{
          padding: 0 20px;
          text-align: left;
          margin-bottom: 20px;
          .bottom-text{
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            .text{
              margin-left: 12px;
              font-size: 10px;
              font-weight: 400;
              color: #8d8d8d;
            }
          }
        }
      }
    }

    .content-bottom {
      margin: 50px 0 30px;
      .b-t{
        font-size: 18px;
        color: #7d858f;
        margin-bottom: 30px;
      }
      .b-list{
        display: flex;
        justify-content: center;
        .hexagon {
          // cursor: pointer;
          width: 80px;
          height: 90px;
          margin: 0 20px;
          background-color: #3c444c;
          clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
          .hexagon-text{
            text-align: center;
            font-size: 11px;
            font-weight: 500;
            padding: 28px 16px;
            color: #919ba2;
          }
          &:hover{
            transform: translateY(-20px)
          }
        }
      }
    }

}
</style>

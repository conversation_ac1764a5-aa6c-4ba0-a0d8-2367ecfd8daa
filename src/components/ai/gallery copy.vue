<template>
  <!-- Ai作品集 -->
  <div class="content">
    <h2>作品集</h2>
    <div class="cate">
        <el-tabs v-model="activeName" @tab-click="selectCate" class="xg-tabs">
            <el-tab-pane
            :label="item.name"
            v-for="(item, index) in galleryData"
            :name="index + ''"
            :key="item.id"
            ></el-tab-pane>
        </el-tabs>
    </div>
    <div class="list" v-if="Object.keys(galleryData).length !== 0">
        <el-row>
            <el-col :span="6" v-for="(item, index) in galleryData[active].list" :key="index" class="list-col">
                <el-image :src="item.cover"  :preview-src-list="srcList" class="image" lazy>
                    <!-- 封面图 -->
                    <!-- <div class="image" :style="`background-image:url('${item.cover}')`"> -->
                    <div class="info">{{ item.name }}</div>
                    <!-- </div> -->
                </el-image>
            </el-col>
        </el-row>
    </div>
  </div>
</template>

<script>
// 引入通用 选项卡 组件
export default {
    name: "gallery",
    data () {
        return {
            galleryData: [],
            srcList: [],
            active : 0,
            activeName: 0,
        };
    },
    props: {

    },
    beforeMount () {
        this.getDatas();
    },
    methods: {
        selectCate(event){
            console.log(event.name)
            this.active = event.name
            this.activeName = event.name
            this.srcList = [];
            this.galleryData[event.name].list.forEach(item => {
                this.srcList.push(item.cover)
            })
            console.log(this.galleryData)
        },
        getDatas () {
            this.$axios.get(this.url + '/api/home/<USER>', {}).then(res => {
                if (res.data.code === 200) {
                    this.$nextTick(() => {
                        this.galleryData = res.data.data;
                        this.galleryData[0].list.forEach(item => {
                            this.srcList.push(item.cover)
                        })
                    });
                }
            }).catch(err => {
            })
        },
    },
    watch:{
        // 'active' : {
        //     handler: function (val, oldVal) {
        //         console.log(val)

        //     }
        // }
    },
    components: {
    },
};

</script>

<style lang="scss" scoped>
.content {
    text-align: center;
    h2 {
        margin-bottom: 20px;
    }
    .cate {
        padding: 20px;
        .el-tabs__header{
            border-bottom: none!important;
            &::after {
                display: none!important;
            }
        }
        .el-tabs{
            display: flex;
            justify-content: center;
            /deep/ .el-tabs__header {
                margin-bottom: 0;
            }
            /deep/ .el-tabs__item.is-active {
                color: #0d6efd!important;
                font-size: 16px;
                background: none;
                font-weight: bold;
            }
            /deep/ .el-tabs__item{
                color: #fff;
                padding: 0 10px;
            }

        }
    }
    .list {
        .image{
            height:240px;
            width: 100%;
            background-size:cover;
            background-position:center;
            border-radius: 6px;
            overflow: hidden;
            position: relative;
            .info {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background:rgba(0,0,0,.6);
                color: #fff;
                font-size: 14px;
                line-height: 1.6;
                padding: 20px;
                transition: all 0.4s;
                opacity: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                    cursor: pointer;
            }
        }
        .list-col {
            padding: 10px;
            &:hover {
                .info {
                    opacity: 1;
                }
            }
        }
    }
}
</style>

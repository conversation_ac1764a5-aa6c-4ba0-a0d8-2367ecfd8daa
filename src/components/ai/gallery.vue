<template>
  <div class="content" v-loading="loading">
    <div class="cate">
      <el-tabs v-model="activeName" @tab-click="selectCate" class="xg-tabs">
        <el-tab-pane :label="item.name" v-for="(item, index) in galleryData" :name="index + ''"
          :key="item.id"></el-tab-pane>
      </el-tabs>
      <!-- <el-divider></el-divider> -->
    </div>
    <div class="waterfall">
      <div v-for="(column, columnIndex) in columns" :key="columnIndex" class="column" style="height: auto">
        <div v-for="(img, imgIndex) in column.images" :key="imgIndex" class="image-wrapper"
          style="background-size:cover;margin-bottom: 20px;border-radius: 10px;min-height:120px;background-repeat: no-repeat;"
          >
          <el-image @click="scale(img)" :src="img.url" style="border-radius: 10px;"  lazy></el-image>
          <div
            style="width: 100%;height: 50px;line-height: 50px;display: flex;justify-content: space-between;align-items: stretch; ">
            <div style="flex:3;padding: 10px 0;display: flex;align-items: center;min-width: 0;">
              <el-avatar style="width: 30px;height: 30px;"
                :src="img.headPic"></el-avatar>
              <span style="height: 30px;text-align:left;line-height: 30px;margin-left: 8px;font-size: 12px;overflow:hidden;white-space: nowrap;text-overflow: ellipsis;">{{img.author}}</span>
            </div>
            <div style="flex:1;text-align: right;cursor: pointer;" @click="like(img)">
              <i :class="img.like ? 'el-icon-star-on' : 'el-icon-star-off'"></i>
            </div>
            <div style="flex: 1;line-height: 53px;text-align: center;">
              <span>{{img.likeCount}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="descShow">
      <image-viewer
          :url-list="srcList"
          :on-close="closeImageViewer"
          :hide-on-click-modal="true" />
          <el-tooltip v-model="descShow" class="item" effect="dark" :content="imgDesc" placement="left">
          </el-tooltip>
    </div>
    <!-- <el-dialog title="" :visible.sync="dialogTableVisible" top="12vh">
      <el-image  :src="pic" lazy></el-image>
    </el-dialog> -->
    <el-pagination
      background
      layout="prev, pager, next"
      :total="pagination.total"
      :page-size="pagination.limit"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page.sync="pagination.offset"
      style="text-align: center;"
      v-if="pagination.total > 0"
      >
    </el-pagination>
    <div class="nodata" v-else>
      暂无数据
    </div>
  </div>

</template>
<script>
  import ImageViewer from 'element-ui/packages/image/src/image-viewer';
  // 引入通用 选项卡 组件
  export default {
    name: "gallery",
    data() {
      return {
        pagination: {
          total: 0,
          offset: 0,
          limit: 10,
        },
        dialogTableVisible:false,
        pic:'',
        galleryData: [],
        srcList:[],
        active: 0,
        activeName: 0,
        columns: [], // 存放列的数组
        imagesData: [], // 存放图片数据的数组
        columnCount: 5, // 预设的列数
        // 假设的每张图片之间的间距
        MARGIN_BETWEEN_IMAGES: 10,
        // 假设的每张图片的初始高度（用于占位符）
        PLACEHOLDER_HEIGHT: 200,
        imageRefs: [],
        loading:false,
        descShow:false,
        imgDesc:'',
        notify:null,
      };
    },
    props: {

    },
    beforeMount() {
      // this.getDatas();
      this.initColumns();
      this.loadImages();
    },
    methods: {
      initColumns() {
        // 初始化列
        for (let i = 0; i < this.columnCount; i++) {
          this.columns.push({
            images: [],
            height: 0,
          });
        }
      },
      like(img){
        this.loading = true;
        this.$axios.post(this.url + '/api/ai/gallery/like', {
          id:img.id
        }).then(res => {
          if (res.data.code === 200) {
            img.like = !img.like;
            this.$message.success({
              showClose: true,
              message: res.data.msg,
            });
          }  else if (res.data.code === 4001) {
            localStorage.removeItem("UserInfo");
            localStorage.removeItem("Authorization");
            this.$message.warning({
              showClose: true,
              message: res.data.msg,
            });
            setTimeout(() => {
              this.$router.push({name:"login"})
            },1000);
          } else {
            this.$message.warning({
              showClose: true,
              message: res.data.msg,
            });
          }
          this.loading = false;
        }).catch(err => {
          this.loading = false;
        })
      },
      scale(img) {
        this.srcList = img.images
        this.descShow = true
        this.imgDesc = img.desc
        this.notify = this.$notify({
          title: '',
          message: img.desc,
          showClose:false,
          offset:100,
          duration: 0,

        });
      },
      closeImageViewer() {
        this.descShow = false
        this.notify.close()
      },
      loadImages(limit = 10, offset = 0) {
        this.loading = true
        this.$axios.post(this.url + '/api/ai/gallery', {
          limit: limit,
          offset: offset,
          cate_id: parseInt(this.active),
        }).then(res => {
          if (res.data.code === 200) {
            this.$nextTick(() => {
              this.galleryData = res.data.data.cates;
              this.pagination.total = res.data.data.total;
              res.data.data.list.forEach(item => {
                this.imagesData.push(item)
              })
              this.makeList()
              this.loading = false
            });
          }
        }).catch(err => {
            this.loading = false
        })

      },
      makeList(){
        let columnIndex = 0;
        for (let i = 0; i < this.imagesData.length; i++) {
          const imageData = this.imagesData[i];
          const imgObj = {
            id: imageData.id,
            desc: imageData.desc,
            url: imageData.cover,
            like: imageData.like,
            likeCount: imageData.like_count,
            author: imageData.author.name,
            headPic: imageData.author.avatar,
            images:imageData.images,
            loaded: false,
            height: this.PLACEHOLDER_HEIGHT, // 初始高度为占位符高度
          };
          // 尽量平均分配图片到每列
          this.columns[columnIndex].images.push(imgObj);
          this.srcList.push(imageData.cover)
          if (columnIndex >= this.columnCount - 1) {
            columnIndex = 0;
          } else {
            columnIndex = columnIndex + 1; // 切换到下一列
          }
        }
      },
      clickPic(url){
        this.dialogTableVisible = true
        this.pic = url
      },

      selectCate(event) {
        // console.log(333, this.galleryData[parseInt(event.name)].id)
        this.active = this.galleryData[parseInt(event.name)].id
        this.activeName = event.name
        this.srcList = [];
        this.imagesData = [];
        this.columns = [];

        this.initColumns();
        this.loadImages();
        this.makeList();
        // console.log(this.galleryData)
      },
      handleSizeChange(val) {
        this.pagination.limit = val;
      },
      // current page news info
      handleCurrentChange(val) {
        let page = val - 1;
        this.srcList = [];
        this.imagesData = [];
        this.columns = [];
        this.initColumns();
        this.loadImages(this.pagination.limit, page);
      }

    },
    watch: {
      // 'active' : {
      //     handler: function (val, oldVal) {
      //         console.log(val)

      //     }
      // }
    },
    components: {
      ImageViewer
    },
  };
</script>
<style>
  .el-notification.right {
    z-index: 4000!important;
    right: 90px!important;
  }
</style>
<style lang="scss" scoped>
  // /deep/ .el-divider--horizontal{
  //   margin:0;
  // }

  /deep/ .el-loading-mask {
    background-color: unset;
  }
  /deep/ .el-dialog__header {
    padding: 0;
  }
  /deep/ .el-dialog__body {
    background: rgba(204, 204, 204, 0.5);
    padding: 30px 0 10px;
  }

  /deep/ .el-dialog__headerbtn{
    top:5px;
    right:10px;
  }
  .waterfall {
    padding: 10px 30px;
    display: flex;
    justify-content: space-between;
    /* 列之间的间距 */
  }

  .column {
    width: calc(20% - 15px);
    /* 假设有5列，所以每列宽度为 20% - 间距 */
    box-sizing: border-box;
  }

  .image-wrapper {
    /* 设置图片的宽度、边距等样式 */
    width: 100%;
    background-size: cover;
    background-position: center;
  }

  .content {
    text-align: center;
    // position: relative;
    // top: -80px;
    h2 {
      margin-bottom: 20px;
    }

    .cate {
      padding: 20px;

      .el-tabs__header {
        // border-bottom: none !important;

        // &::after {
        //   display: none !important;
        // }
      }

      .el-tabs {
        display: flex;
        border-bottom: 1px solid #555;
        // justify-content: center;

        /deep/ .el-tabs__header {
          margin-bottom: 0;
        }

        /deep/ .el-tabs__nav-wrap {
          padding: 0px !important;
        }

        /deep/ .el-tabs__item.is-active {
          color: #0d6efd !important;
          font-size: 16px;
          background: none;
          font-weight: bold;
          border-bottom: 2px solid;
          border-radius: 0;
        }

        /deep/ .el-tabs__item {
          color: #fff;
          padding: 0 10px;
          &:hover{
            border-bottom: 2px solid;
            border-radius: 0;
          }
        }

      }
    }

    .list {
      .image {
        height: 240px;
        width: 100%;
        background-size: cover;
        background-position: center;
        border-radius: 6px;
        overflow: hidden;
        position: relative;

        .info {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, .6);
          color: #fff;
          font-size: 14px;
          line-height: 1.6;
          padding: 20px;
          transition: all 0.4s;
          opacity: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
        }
      }

      .list-col {
        padding: 10px;

        &:hover {
          .info {
            opacity: 1;
          }
        }
      }
    }
  }
</style>

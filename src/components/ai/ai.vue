<template>
    <el-container class="full-screen">
      <el-aside class="ai-left aside-content" :style="width">
          <div class="logo-container" v-if="!isCollapse" :style="logoPadding">
              <!-- logo -->
              <router-link to="/" class="xg-flex logo">
                  <img src="@/assets/img/logo.png" alt="Logo" />
              </router-link>
              <span class="company-name">犀光AI</span>
          </div>
          <div class="logo-container-m" :style="logoPadding" v-else>
            <router-link to="/" class="xg-flex logo">
              <img src="@/assets/img/ny-bg.png" alt="Logo" />
            </router-link>
          </div>
          <el-row class="tac">
            <el-col :span="24">
              <div class="openBtn" :class="[isCollapse?'el-icon-s-unfold':'el-icon-s-fold']" @click="collapse"></div>
              <el-menu
                :collapse="isCollapse"
                collapse-transition
                :default-active="menuActive"
                class="el-menu-vertical-demo"
                router
                background-color="#151422"
                text-color="#cbd3de"
                active-text-color="#0d6efd !important"
                @select="menuSelect"
              >
                <el-menu-item index="/ai/gallery">
                  <i class="el-icon-document"></i>
                  <span>作品集</span>
                </el-menu-item>
                <el-menu-item index="/ai/plug">
                  <i class="el-icon-menu"></i>
                  <span slot="title">插件介绍</span>
                </el-menu-item>
                <el-menu-item index="/ai/chat">
                  <i class="el-icon-chat-dot-round"></i>
                  <span slot="title">AI问答</span>
                </el-menu-item>
                <el-menu-item index="/ai/render">
                  <i class="el-icon-cpu"></i>
                  <span slot="title">AI智能渲染</span>
                </el-menu-item>
                <el-menu-item index="/ai/news">
                  <i class="el-icon-news"></i>
                  <span slot="title">其他资讯</span>
                </el-menu-item>
                <el-menu-item index="/ai/vip">
                  <i class="el-icon-medal-1"></i>
                  <span slot="title">开通VIP</span>
                </el-menu-item>
              </el-menu>
            </el-col>
          </el-row>
      </el-aside>
      <el-container class="ai-right">
          <el-header>
            <el-page-header v-if="newsBack" @back="goBack" title="" style="position: absolute;left: 40px;top:20px">
            </el-page-header>
            <el-popover
                placement="bottom"
                width="100"
                height="100"
                trigger="hover"
                >
                <div style="text-align: center;">
                  <p><img src="../../assets/img/wechat.jpg" alt="" title="" /></p>
                  <p>扫码咨询客服</p>
                </div>
                <div class="xg-flex kefu" slot="reference">
                  <i class="el-icon-service" style="font-size: 24px;margin: 0 auto;z-index: 10000;"></i>
                </div>
              </el-popover>

            <!-- 搜索 -->
            <div class="xg-flex search">
              <input type="text" placeholder="输入关键词" v-model="keywords" @keyup.enter="seachEnter" />
              <div class="icon" @click="search()">
                <img src="@/assets/img/search.png" alt="" title="" />
              </div>
            </div>
            <!-- 登录注册按钮 -->
            <div class="xg-flex login top">
              <!-- 已经登录的状态 -->
              <div class="" v-if="user.name">
                <!-- 头像 -->
                <div class="avatar" @click="goToInfo">
                  <img v-if="user.avatar" :src="user.avatar" :alt="user.name" :title="user.name" />
                </div>
                <!-- 下拉框 -->
                <div class="info">
                  <div>
                    <div class="list">
                      <div class="list-item" style="cursor: pointer;" @click="dialogpassword = true">修改密码</div>
                      <div class="list-item" style="cursor: pointer;" @click="logout()">注销登录</div>
                    </div>
                    <!-- <div class="xg-btn" @click="logout()"><span>退出</span></div> -->
                  </div>
                </div>
              </div>
              <!-- 还未登录的状态 -->
              <div class="xg-flex bottom" v-else>
                <!-- 登录 -->
                <router-link to="/login" class="login-btn">登录</router-link>
                <!-- 注册 -->
                <router-link to="/register" class="register-btn">注册</router-link>
              </div>
              <div class="name">
                <div @click="goToInfo">{{ user.name }}</div>
                <div v-if="!user.vip" style="color: #0e6dfe;margin-top: 5px;" @click="goVip">开通会员</div>
                <div v-else-if="user.vip && user.vip.permanent" style="color: #7d858f;margin-top: 5px;font-size: 12px;"><i style="font-size: 16px;" class="el-icon-connection"></i> 永久VIP</div>
                <div v-else style="color: #7d858f;margin-top: 5px;font-size: 11px;">
                  <template v-if="expire">
                    VIP已过期 <span style="margin-left:10px;color: #0e6dfe;font-size: 14px;" @click="goVip">开通会员</span>
                  </template>
                  <template v-else>
                    VIP到期时间：{{user.vip.endtime}}
                  </template>
                </div>
              </div>
            </div>

          </el-header>
          <el-main :style="galleryStyle">
            <!-- 主要内容 -->
            <router-view ></router-view>
          </el-main>
      </el-container>
      <!--登录密码-弹窗-->
      <el-dialog title="密码修改" :visible.sync="dialogpassword" class="dialog01">
        <el-form ref="ruleForm" :model="ruleForm" status-icon :rules="rules" class="demo-ruleForm">
          <el-form-item prop="oldpassword" label="当前密码">
            <el-input type="password" v-model="ruleForm.oldpassword" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item prop="pass" label="新密码">
            <el-input type="password" v-model="ruleForm.pass" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item prop="checkPass" label="确认密码">
            <el-input type="password" v-model="ruleForm.checkPass" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <div class="dialog-footer">
          <el-button type="primary" @click="passwordChange">确 定</el-button>
          <el-button @click="dialogpassword = false">取 消</el-button>
        </div>
      </el-dialog>

    </el-container>
</template>

<script>

export default {
  name: "ai",
  components: {

  },
  computed: {
  },
  data() {
    var validatePass = (rule, value, callback) => {
      if (this.ruleForm.checkPass !== '') {
        this.$refs.ruleForm.validateField('checkPass');
      }
      callback();
    };

    var validateCheckPass = (rule, value, callback) => {
      if (value !== this.ruleForm.pass) {
        callback(new Error('两次输入密码不一致!'));
      }
      callback();
    };
    return {
      newsBack:false,
      kefu:false,
      galleryStyle:'',
      width:'width:200px',
      logoPadding:'padding: 20px 30px;',
      keywords:'',
      // 表单验证
      rules: {
        oldpassword:[{required: true, message: '请填写密码', trigger: 'change'}],
        password:[{required: true, message: '请填写密码', trigger: 'change'}],
        pass: [{ required: true, message: '请填写新密码', trigger: 'change' }, { validator: validatePass, trigger: 'blur' }],
        checkPass: [{required:true, message:'请填写确认新密码', trigger:'change'}, { validator: validateCheckPass, trigger: 'blur' }],
      },
      // 密码修改
      ruleForm: {
        pass: '',
        checkPass: '',
        oldpassword: ''
      },
      isOpen: false,
      dialogpassword:false, //密码修改
      private: "",
      service: "",
      question: "",
      about: "",
      upload_statement: "",
      copyright_statement: "",
      activeName: "ai",
      menuActive: '/ai/gallery',
      // 用户数据
      user: {
        avatar: "https://placekitten.com/g/30/30",
      },
      isCollapse: false,
      windowWidth: document.documentElement.clientWidth, //实时屏幕宽度
      windowHeight: document.documentElement.clientHeight, //实时屏幕高度
      expire:false,
    };
  },
  beforeMount() {
    //  获取本地存储中user数据
    let user = JSON.parse(localStorage.getItem("UserInfo"));
    if (user) {
      this.state = true;
      this.user = user;
      if (this.user.vip && !this.user.vip.permanent) {
          this.isExpire(this.user.vip.endtime)
      }
    }

    this.getInfo();
    let type = this.$route.query.type;
    if(this.$route.path == '/ai/gallery' || this.$route.path == '/ai/news') {
      this.galleryStyle = 'position: relative;top: -80px;';
    } else {
      this.galleryStyle = ''
    }

    this.menuActive = this.$route.path;

    let regex = /\/ai\/news\/\d+/;
    if(regex.test(this.$route.path)) {
      this.newsBack = true;
      this.menuActive = '/ai/news'
    } else {
      this.newsBack = false;
    }

    if (type) {
      this.$nextTick(function () {
        this.activeName = type+'';
      });
    }
  },
  methods: {
    collapse(){
      this.isCollapse = !this.isCollapse
      if(this.isCollapse) {
        this.width = 'width:64px';
        this.logoPadding = 'padding: 30px 15px;';
      } else {
        this.width = 'width:200px';
        this.logoPadding = 'padding: 20px 30px;';
      }
    },
    isExpire(endtime){
      console.log(endtime)
      let expirationDate = new Date(endtime);

      // 获取当前日期
      let currentDate = new Date();

      // 比较当前日期和到期日期
      if (currentDate > expirationDate) {
          console.log('已过期')
          this.expire = true
      } else {
        console.log('未过期')
          this.expire = false
      }
    },
    menuSelect(index, path){
      // console.log(index)
      this.menuActive = index
    },
    goBack(){
      this.$router.push("/ai/news");
    },
    /**
     * 前往个人中心
     */
    goToInfo() {
      this.$router.push("/user/info");
    },
    goVip() {
      this.$router.push("/ai/vip");
    },
    setOpen() {
      this.isOpen = !this.isOpen;
    },
    /**
     * 关键词搜索
     */
    search() {
      if (!this.keywords) {
        return;
      }

      if (this.keywords != this.$route.query.keywords) {
        this.$router.push({
          name: "aiNews",
          query: { keywords: this.keywords },
        });
      }
    },
    /**
     * 回车搜索
     */
    seachEnter(e) {
      var keyCode = window.event ? e.keyCode : e.which;
      if (keyCode == 13) {
        this.search(); //搜索按钮的回调
      }
    },
    // 密码修改
    passwordChange(){
      let user = this.user
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.$axios.post(this.url + '/api/user/set-password',{
            old_password: this.ruleForm.oldpassword,
            password: this.ruleForm.pass
          }).then(res => {
            if (res.data.code !== 200) {
              this.$message.warning({
                showClose: true,
                message: res.data.msg
              });
            } else {
              user.is_pass = true;
              localStorage.setItem('UserInfo', JSON.stringify(user));
              this.dialogpassword = false
            }
          }).catch(err => {
          });
        } else {
          // 登录表单校验失败
          return false;
        }
      });
    },
    // 退出登录
    logout() {
      this.$axios
          .post(this.url + "/api/user/logout", {
            params: {},
          })
          .then((res) => {
            localStorage.removeItem("UserInfo");
            localStorage.removeItem("Authorization");
            localStorage.removeItem("IsLogin");
            if (this.$route.name != "homeindex") {
              this.$router.push("/");
            }
            this.$nextTick(() => {
              this.state = false;
              this.user = {};
            });
            this.reload();

            this.$message({
              message: "退出登录成功！",
              type: "success",
            });
          })
          .catch((err) => { });
    },
    getInfo() {
      this.$axios
        .get(this.url + "/api/config/info", {
          params: {},
        })
        .then((res) => {
          if (res.data.code === 200) {
            this.about = res.data.data.about;
            this.question = res.data.data.question;
            this.service = res.data.data.service;
            this.private = res.data.data.private;
            this.upload_statement = res.data.data.upload_statement;
            this.copyright_statement = res.data.data.copyright_statement;
          }
        })
        .catch((err) => {});
    },
  },
  watch: {
    $route: {
      handler(route, oldroute) {
        let user = JSON.parse(localStorage.getItem("UserInfo"));
        if (user) {
          this.state = true;
          this.user = user;
          if (this.user.vip && !this.user.vip.permanent) {
              this.isExpire(this.user.vip.endtime)
          }
        }
        // 监听路由变化为内页时，导航设置内页样式
        route.path != "/" ? (this.ny = true) : (this.ny = false);
        if(route.path == '/ai/gallery' || route.path == '/ai/news') {
          this.galleryStyle = 'position: relative;top: -80px;';
        } else {
          this.galleryStyle = ''
        }

        this.menuActive = route.path

        let regex = /\/ai\/news\/\d+/;
        if(regex.test(route.path)) {
          this.newsBack = true;
          this.menuActive = '/ai/news'
        } else {
          this.newsBack = false;
        }


        // 路由变化，关闭移动端菜单
        this.isOpen = false;

        let type = this.$route.query.type;

        this.$nextTick(function () {
          this.activeName = type+'';
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
  /deep/ .el-main {
    padding: 0!important;
  }
  /deep/ .el-menu-item [class^=el-icon-]{
      margin-right: 16px !important;
  }
  /deep/ .el-page-header__left .el-icon-back{
    font-size:30px;
  }
  /deep/ .el-page-header__left::after{
      content: none;
  }
.full-screen {
    height: 100vh; /* 视口高度 */
    width: 100vw; /* 视口宽度 */
    .aside-content {
        display: flex;
        align-items: center; /* 垂直对齐 */
        position: relative;
        .logo-container {
            // width: 100%;
            text-align: center;
            display: flex;
            align-items: center;
            // padding: 20px 30px;
            position: absolute;
            top: 0;
            &:hover {
                img {
                    margin-top: -40px;
                }
                color: #fff !important;
            }
        }
        .logo-container-m {
            // width: 100%;
            text-align: center;
            display: flex;
            align-items: center;
            // padding: 20px 30px;
            position: absolute;
            top: 0;

        }
        .company-name {
            color: #cbd3de;
            font-size: 22px;
            font-weight:bold;
            font-style: italic;
            margin-left: 5px;
            cursor: pointer;
        }
    }

    .el-aside {
        background-color: #151422;
        // width: 200px !important;
        .logo {
            width: 40px;
            height: 40px;
            overflow: hidden;
            display: block;
            img {
                // margin-top: -40px;
            }
        }
        .tac{
          .openBtn{
            display:block;width:100%;text-align:right;
            cursor: pointer;font-size: 20px;color:#909399;margin-bottom:10px;
            padding: 0 20px;
            &.el-icon-s-unfold{text-align:center;}
            &.el-icon-close{font-weight:bold;padding-right:18px;}
          }
          width: 100%;
          background: #151422;
          position: absolute;
          top: 100px;
          .el-menu {
            border:none;
          }
        }
    }
    .el-container {
        height: 100%;
    }
    .ai-right {
      background:rgb(42,51,60);
      color: white;
      .kefu {
        z-index: 10000;
        margin-right: 20px;width: 40px;height: 40px;background: #222;border-radius: 20px;cursor: pointer;
        &:hover{
          color: #0d6efd;
        }
      }
      .el-main{
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
      .el-main::-webkit-scrollbar{
        display: none;
      }
      .el-header{
        position: relative;
        display: flex;
        align-items: center;
        justify-content: right;
        padding: 0 20px;
        .search {
          z-index: 10000;
          flex-shrink: 0;
          margin-right: 30px;
          border: 1px solid #646e7a;
          border-radius: 100px;
          width: 220px;
          height: 40px;
          padding: 10px 14px;

          input {
            height: 100%;
            min-width: 0;
            flex-grow: 1;
            color: #fff;
            font-size: 12px;

            &::-webkit-input-placeholder {
              /* WebKit browsers */
              color: #46515d;
            }

            &:-moz-placeholder {
              /* Mozilla Firefox 4 to 18 */
              color: #46515d;
            }

            &::-moz-placeholder {
              /* Mozilla Firefox 19+ */
              color: #46515d;
            }

            &:-ms-input-placeholder {
              /* Internet Explorer 10+ */
              color: #46515d;
            }
          }

          .icon {
            width: 20px;
            height: 20px;
            overflow: hidden;
            margin-left: 10px;
            flex-shrink: 0;
            cursor: pointer;

            img {
              margin-top: 0;
            }
          }
        }

        // 登录注册
        .login {
          z-index: 10000;
          flex-shrink: 0;
          // 登录注册按钮
          .login-btn,
          .register-btn {
            display: block;
            font-size: 16px;
            color: #cbd3de;
            line-height: 1.2;

            &:hover {
              color: #0d6efd;
            }
          }

          .login-btn {
            margin-right: 20px;
            padding-right: 20px;
            position: relative;

            &::after {
              content: "";
              display: block;
              width: 1px;
              height: 100%;
              position: absolute;
              right: 0;
              top: 0;
              background: #fff;
            }
          }

        }
      }
    }

    // 已经登录的状态
    .top {
      // position: relative;
      flex-shrink: 0;
      z-index: 10000;
      &:hover {
        .info {
          opacity: 1;
          visibility: visible;
          transform: translateY(0px);
        }
      }
    }

    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 100%;
      overflow: hidden;
      cursor: pointer;
      outline: none;
      overflow: hidden;
      background: #fff;
      display: block;
      border: 1px solid #ddd;

      img {
        width: 100%;
      }
    }

    .info {
      padding-top: 10px;
      transform: translateY(20px);
      transition: all 0.4s;
      text-align: left;
      opacity: 0;
      visibility: hidden;
      position: absolute;
      top: 100%;
      width: 150px;
      right: 20px;
      z-index: 99;
      text-align: center;
    }

    .info>div {
      padding: 10px 15px;
      background: #505860;
      border-radius: 4px;
      border-radius: 4px;
      box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
    }

    .name {
      font-size: 14px;
      color: #dcdfe6;
      line-height: 1.2;
      margin: 0 15px;
      cursor: pointer;
    }

    .list {}

    .list-item {
      font-size: 12px;
      color: #aaa;//#5f6974;
      line-height: 1.2;
      padding-bottom: 10px;
      padding-top: 10px;
      // border-bottom: 1px solid #e6e6e6;
      display: block;

      &:hover {
        color: #0d6efd;
      }
    }

}
</style>

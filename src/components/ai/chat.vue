<template>
  <!-- Ai作品集 -->
  <div class="content">
      <div class="msg-list">
            <div class="scroll-view">
                <div class="scroll-view" style="overflow: hidden auto;" ref="scrollContainer">
                    <div class="scroll-view-content">
                        <div>
                            <div class="x-hot-question">
                                <div class="hot-title">热门提问</div>
                                <div class="hot-title-tips">Popular Questions</div>
                                <div class="hot-list">
                                    <div class="hot" v-for="(item, index) in hot" :key="index">
                                        <div class="hot-text" @click.stop="hotClick(item.title)">
                                            <div class="ellipsis-2">
                                                <span ref="hotText">{{item.title}}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="refresh-but" @click="refresh" v-if="show">
                                    <i class="el-icon-refresh refresh"></i>
                                    <span slot="title"> 换一批</span>
                                </div>
                            </div>
                        </div>
                        <template v-for="item in conversation">
                        <div class="content-box reverse" v-if="item.isQuestion">
                            <div class="msg-box">
                                <div class="avatar">
                                    <img v-if="user" :src="user.avatar">
                                    <img v-else src="data:image/jpg;base64,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" draggable="false">
                                </div>
                                <div class="msg-content">
                                    <div class="rich-text-box rich-text">
                                        <div style="position: relative;">
                                            <p>
                                                {{item.text}} <span class="cursor">|</span>
                                            </p>
                                        </div>
                                    </div>
                                    <div class="copy" @click="copyText(item.text)">
                                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAADwSURBVFiF7ZfdDYMwDITvKtYprNEdUNdoZ2jXQOzQNSgDpS9G4qchBw1KH/K9QCSTu8g2BiCTGKqBzrkHgCqCZkOyHRYnUbyOJA4A5XghGYjM5CApDEwoxLgeQLNx76sSJBfhHqx2FkZIXob7YvZACeAcQ5xkS7K1Pb0FPE/BM4Y4ADjnepLvUNyRRVjbddVE8i7IBpT3QIdAHg2p77ca6EjeQ5tYq+0iZKCyKXgYSgpiTcGvJC/C5AaUFNxCr1Tf0PEwmapKFygtuMZ4lC/mg9IFr1/UTdB7iOQ1kA0caUAq3rmBrR+ePrrxz0fmr/kAmq06liFXdIkAAAAASUVORK5CYII=" draggable="false">
                                    </div>
                                    <!-- <div class="star">
                                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAGVSURBVFiF7VfRdcMwCDz6Oo/myBCeI0N4Dg3RObQQ/aiwZQyKhNp89d7Lix1LcCA4HGABzLwx875igxac7wBSvS1E9IzY+Qg6T41zAEjMvL2NAABxVgBk9dvfElDRZyLKzbNpEpEMHNETUREiATvzBHT0xpJtNguzGbCiRz2GUBaGCdTIetELtpqpIVx0QKUvqW+B2/NKG4716roAgGSQGscjZ1fwU/nFelgjbzPVQyaiLAS+FNsLa89hD+oYbsIFAET0+HSYTTvUUDaO62Z2FOAsQjnTBGCfKaJRMHOqmbYLWRY0n18j0bOtu0AX0XP1OKrNdmRfbJrjWLXTRe8nnbfdZbavKUR1obCcltfqXLLpOncJGCQieOm8S0AhUpCWpoQJhMctXijsKwIrrTh0fC6BS692WtHTi3ZPT1MsKda4OW9eTLZ67w2pgnMOmEH0CJhn50xOkXBNRAi4GMlAdhxLYRac6ilEtHi5JNw/JmpEawM3dXTeBbKQJqJHlEDXsbHPfLnxCHQNNdMrIsVL+//xNnwDz2cNVhSWwHoAAAAASUVORK5CYII=" draggable="false">
                                    </div> -->
                                </div>
                            </div>
                        </div>
                        <div class="content-box" v-else>
                            <div class="msg-box">
                                <div class="avatar">
                                    <!-- <img src="@/assets/img/logo.png" style="margin-top: -38px;margin-left: 5px;"/> -->
                                    <img src="@/assets/img/ny-bg.png" />
                                </div>
                                <div class="msg-content">
                                    <div class="rich-text-box rich-text">
                                        <div style="position: relative;">
                                          <p>
                                              {{item.text}} <span class="cursor">|</span>
                                          </p>
                                        </div>
                                    </div>
                                    <div class="copy" @click="copyText(item.text)">
                                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAADwSURBVFiF7ZfdDYMwDITvKtYprNEdUNdoZ2jXQOzQNSgDpS9G4qchBw1KH/K9QCSTu8g2BiCTGKqBzrkHgCqCZkOyHRYnUbyOJA4A5XghGYjM5CApDEwoxLgeQLNx76sSJBfhHqx2FkZIXob7YvZACeAcQ5xkS7K1Pb0FPE/BM4Y4ADjnepLvUNyRRVjbddVE8i7IBpT3QIdAHg2p77ca6EjeQ5tYq+0iZKCyKXgYSgpiTcGvJC/C5AaUFNxCr1Tf0PEwmapKFygtuMZ4lC/mg9IFr1/UTdB7iOQ1kA0caUAq3rmBrR+ePrrxz0fmr/kAmq06liFXdIkAAAAASUVORK5CYII=" draggable="false">
                                    </div>
                                    <!-- <div class="star">
                                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAGVSURBVFiF7VfRdcMwCDz6Oo/myBCeI0N4Dg3RObQQ/aiwZQyKhNp89d7Lix1LcCA4HGABzLwx875igxac7wBSvS1E9IzY+Qg6T41zAEjMvL2NAABxVgBk9dvfElDRZyLKzbNpEpEMHNETUREiATvzBHT0xpJtNguzGbCiRz2GUBaGCdTIetELtpqpIVx0QKUvqW+B2/NKG4716roAgGSQGscjZ1fwU/nFelgjbzPVQyaiLAS+FNsLa89hD+oYbsIFAET0+HSYTTvUUDaO62Z2FOAsQjnTBGCfKaJRMHOqmbYLWRY0n18j0bOtu0AX0XP1OKrNdmRfbJrjWLXTRe8nnbfdZbavKUR1obCcltfqXLLpOncJGCQieOm8S0AhUpCWpoQJhMctXijsKwIrrTh0fC6BS692WtHTi3ZPT1MsKda4OW9eTLZ67w2pgnMOmEH0CJhn50xOkXBNRAi4GMlAdhxLYRac6ilEtHi5JNw/JmpEawM3dXTeBbKQJqJHlEDXsbHPfLnxCHQNNdMrIsVL+//xNnwDz2cNVhSWwHoAAAAASUVORK5CYII=" draggable="false">
                                    </div> -->
                                </div>
                            </div>
                        </div>
                        </template>
                        <div class="content-box" v-if="answerStatus">
                            <div class="msg-box">
                                <div class="avatar">
                                    <!-- <img src="@/assets/img/logo.png" style="margin-top: -35px;"/> -->
                                    <img src="@/assets/img/ny-bg.png" />
                                </div>
                                <div class="msg-content">
                                    <div class="rich-text-box rich-text">
                                        <div style="position: relative;">
                                          <p ref="answerText">
                                            <!-- 使用ref来引用DOM元素 -->
                                            <span v-html="typedAnswer"></span> <span class="cursor">|</span>
                                          </p>
                                        </div>
                                    </div>
                                    <!-- <div class="copy">
                                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAADwSURBVFiF7ZfdDYMwDITvKtYprNEdUNdoZ2jXQOzQNSgDpS9G4qchBw1KH/K9QCSTu8g2BiCTGKqBzrkHgCqCZkOyHRYnUbyOJA4A5XghGYjM5CApDEwoxLgeQLNx76sSJBfhHqx2FkZIXob7YvZACeAcQ5xkS7K1Pb0FPE/BM4Y4ADjnepLvUNyRRVjbddVE8i7IBpT3QIdAHg2p77ca6EjeQ5tYq+0iZKCyKXgYSgpiTcGvJC/C5AaUFNxCr1Tf0PEwmapKFygtuMZ4lC/mg9IFr1/UTdB7iOQ1kA0caUAq3rmBrR+ePrrxz0fmr/kAmq06liFXdIkAAAAASUVORK5CYII=" draggable="false">
                                    </div> -->
                                    <!-- <div class="star">
                                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAGVSURBVFiF7VfRdcMwCDz6Oo/myBCeI0N4Dg3RObQQ/aiwZQyKhNp89d7Lix1LcCA4HGABzLwx875igxac7wBSvS1E9IzY+Qg6T41zAEjMvL2NAABxVgBk9dvfElDRZyLKzbNpEpEMHNETUREiATvzBHT0xpJtNguzGbCiRz2GUBaGCdTIetELtpqpIVx0QKUvqW+B2/NKG4716roAgGSQGscjZ1fwU/nFelgjbzPVQyaiLAS+FNsLa89hD+oYbsIFAET0+HSYTTvUUDaO62Z2FOAsQjnTBGCfKaJRMHOqmbYLWRY0n18j0bOtu0AX0XP1OKrNdmRfbJrjWLXTRe8nnbfdZbavKUR1obCcltfqXLLpOncJGCQieOm8S0AhUpCWpoQJhMctXijsKwIrrTh0fC6BS692WtHTi3ZPT1MsKda4OW9eTLZ67w2pgnMOmEH0CJhn50xOkXBNRAi4GMlAdhxLYRac6ilEtHi5JNw/JmpEawM3dXTeBbKQJqJHlEDXsbHPfLnxCHQNNdMrIsVL+//xNnwDz2cNVhSWwHoAAAAASUVORK5CYII=" draggable="false">
                                    </div> -->
                                </div>
                            </div>
                        </div>
                        <div v-loading="loading" style="width: 100%;height: 30px;"></div>
                        <div class="clear-list" v-if="conversation.length > 0 && !answerStatus && !loading">
                            <div class="clear-text" @click="clear">清空问答记录</div>
                        </div>
                    </div>
                </div>
            </div>
      </div>
      <div class="foot-box">
          <div v-if="!isVip" style="color:#8d8d8d;font-size: 14px;margin: 10px auto;min-width: 386px;max-width: 840px;">免费次数：{{freeCount}} 次</div>
          <div class="foot-box-content" style="position: relative;">
                <div class="textarea-box">
                    <el-input type="textarea" placeholder="请输入你的问题..." class="textarea" v-model="question" :disabled="sendStatus"></el-input>
                </div>
                <div v-if="freeCount == 0 && showPrice && !isVip" style="color:#8d8d8d;font-size: 14px;width: 90px;line-height: 62px;">0.02光子/次</div>
                <div class="send-btn-box" @click.stop="beforeSend" :class="sendStatus ? 'noClick' : ''">
                    <div class="logo">
                        <img src="@/assets/img/logo.png" />
                    </div>
                </div>
                <div style="position: absolute;right: -100px;top: 20px;"><span style="color:#8d8d8d;">每周二维护</span></div>
          </div>

      </div>
      <el-dialog

        :visible.sync="chatComfirm"
        width="25%"
        :center="dialogText"
        top="30vh"
        @close="closeDialog"
        >
        <div slot="title" style="position: relative;">
            <el-link @click="goVip" style="position: absolute;left: 0;color:#0e6dfe">开通会员</el-link>
            <p style="color: #000;">温馨提示</p>
        </div>
        <span v-if="freeCount">将消耗免费一次进行回答</span>
        <span v-else>是否使用0.2光子回答一次</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="send">确 定</el-button>
        </span>
      </el-dialog>
  </div>
</template>

<script>
// 引入通用 选项卡 组件
export default {
  name: "chat",
  data () {
    return {
        user:{},
        question:'',
        answer:'',
        hot:[],
        page:0,
        limit:9,
        show:false,
        sendStatus: false,
        answerStatus: false,
        typedAnswer:'',
        typingIndex:0,
        // typingInterval:100,
        typingInterval: 5,
        conversation:[],
        textToCopy: '',
        copySuccess: false,
        freeCount:0,
        showPrice:true,
        isVip:true,
        loading:false,
        chatComfirm:false,
        dialogText:true,
    };
  },
  props: {

  },
  beforeMount () {
    let user = JSON.parse(localStorage.getItem("UserInfo"));
    this.user = user
    this.getHot();
    this.getVip();
  },
  methods: {
      getVip(){
        this.$axios
          .get(this.url + "/api/ai/vip", {})
          .then((res) => {
            if (res.data.code === 200) {
              this.$nextTick(() => {
                this.isVip = res.data.data.is_vip
                this.freeCount = res.data.data.free_chat;
              });
            }
          })
          .catch((err) => {});
      },
      goVip() {
        this.$router.push("/ai/vip");
      },
      beforeSend(){
        if(this.isVip) {
          this.send()
        } else {
          this.chatComfirm = true;
        }
      },
      closeDialog(){
        this.chatComfirm = false;
      },
      hotClick(title){
          this.question = title
      },
      getHot(){
        this.$axios.post(this.url + '/api/ai/hot_question', {
          offset: this.page,
          limit:this.limit
        }).then(res => {
            if (res.data.code === 200) {
                this.$nextTick(() => {
                    this.hot = res.data.data.list;
                    if (res.data.data.total > this.limit) {
                      this.show = true
                    }
                    if ((this.page + 1) * this.limit >= res.data.data.total) {
                      this.page = 0;
                    } else {
                      this.page += 1;
                    }
                });
            }
        }).catch(err => {
        })
      },
      refresh(){
        this.getHot()
      },
      startTyping() {

        this.typedAnswer = '';
        this.typingIndex = 0;
        this.typeChar();

      },
      typeChar() {
        if (this.typingIndex < this.answer.length) {
          this.typedAnswer += this.answer.charAt(this.typingIndex);
          this.typingIndex++;
          setTimeout(this.typeChar, this.typingInterval);
          // this.scrollToBottom();
        } else {
          this.sendStatus = false;
          this.processConversation()
        }

      },
      processConversation(){
        this.conversation.push({
          "text" : this.answer,
          "isQuestion" : false
        })
        this.answerStatus = false;
      },
      sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
      },
      send(e){
          this.chatComfirm = false;
          if(this.answerStatus){
            return false;
          }
          if (this.question.trim() !== ''){
            this.conversation.push({
              "text" : this.question,
              "isQuestion" : true
            })
            this.sendStatus = true;
            this.loading = true
            let question = this.question
            // await this.sleep(10000);
            this.$axios.post(this.url + '/api/ai/chat', {
              prompt: this.question,
            }).then(res => {

                this.answerStatus = true;
                if (res.data.code === 200) {
                    this.$nextTick(() => {
                        // console.log(res.data.data)
                        this.answer = res.data.data
                        this.startTyping();
                        if (this.freeCount > 0) {
                          this.freeCount -= 1
                        } else {
                          this.freeCount = 0
                        }

                    });
                } else {
                  this.answer = res.data.msg
                  this.startTyping();
                  if (res.data.msg == '光子不足，请充值') {
                    setTimeout(() => {
                      this.$router.push("/user/integral");
                    },2000);
                  }
                }
                this.loading = false
                this.sendStatus = false;
            }).catch(err => {
                this.answer = '链接错误'
                this.answerStatus = true;
                this.sendStatus = false;
                this.loading = false
            })
            this.question = '';
          }

      },
      scrollToBottom() {
        const container = this.$refs.scrollContainer;
        // console.log(container.scrollHeight)
        container.scrollTop = container.scrollHeight;
      },
      clear(){
        console.log('clear')
        this.conversation = [];
        this.answerStatus = false;
      },
      copyText(text){
        this.$copyText(text).then(
          (e) => {
            this.$message.success({
              showClose: true,
              message: '文本已复制到剪贴板！',
            });
          },(e) =>{
            this.$message.warning({
              showClose: true,
              message: '无法复制文本: 请手动选择要复制的文字' ,
            });
          }
        )
      }
      // async copyText(text) {
      //   try {
      //     this.textToCopy = text
      //     await navigator.clipboard.writeText(this.textToCopy);
      //     this.$message.success({
      //       showClose: true,
      //       message: '文本已复制到剪贴板！',
      //     });
      //   } catch (err) {
      //     this.$message.warning({
      //       showClose: true,
      //       message: '无法复制文本: 请用https访问或者手动选择要复制的文字' ,
      //     });
      //     console.error('无法复制文本: ', err);
      //   }
      // }
  },
  watch: {
      $route: {
        handler(route, oldroute) {
          let user = JSON.parse(localStorage.getItem("UserInfo"));
          if (user) {
            this.user = user;
          }
        }
      },
      typedAnswer() {
        this.scrollToBottom();
      },
      loading(newValue, oldValue){
        if (newValue === true && oldValue !== true) {
          // 如果 newValue 是 true 并且它之前不是 true，则执行函数
          this.scrollToBottom();
        }
      },
      question:{
        handler(newVal, oldVal) {
          // 检查 newVal 是否有值（不是 null、undefined、空字符串等）
          if (newVal !== null && newVal !== undefined && newVal !== '') {
              this.showPrice = false
          } else {
            this.showPrice = true
          }
        },
        // 立即执行一次 handler
        immediate: true
      }
  },
  components: {
  },
};

</script>

<style lang="scss" scoped>
/deep/ .el-loading-mask {
  background-color: unset;
}
.noClick {
  cursor: not-allowed!important;
  pointer-events: none;
}
.rich-text-box{
    max-width: 100%;
    div{

    }
}
.rich-text{
    // white-space: pre-wrap;
    word-break: break-word;
    // overflow-x: scroll;
    div{
        word-break: break-all;
        word-wrap: break-word;
        // // white-space: pre-wrap;
        max-width: 100%;
        overflow: auto;
    }
}
.cursor{
    display: none;
}

.content{
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    .msg-list{
        height: 0;
        flex: 1;
        width: 100%;
        .scroll-view::-webkit-scrollbar{
            display: none;
        }
        .scroll-view{
            width: 100%;
            height: 100%;
            position: relative;
            -ms-overflow-style: none;
            scrollbar-width: none;
            -webkit-overflow-scrolling: touch;
            max-height: inherit;
            .scroll-view-content{
                width: 100%;
                height: 100%;

                .x-hot-question{
                    width: 100%;
                    padding: 0 32px;
                    min-height: 258px;
                    .hot-title{
                        // margin-top: 50px;
                        text-align: center;
                        color: #fff;
                        font-size: 36px;
                        font-weight: 700;
                        line-height: 50px;
                    }
                    .hot-title-tips{
                        text-align: center;
                        color: hsla(0,0%,100%,.5);
                        font-size: 24px;
                        font-weight: 400;
                        line-height: 29px;
                        margin: 16px 0;
                    }
                    .hot-list{
                        display: flex;
                        flex-wrap: wrap;
                        min-width: 386px;
                        max-width: 840px;
                        margin: 40px auto 0;
                        .hot{
                            display: flex;
                            width: 33.3%;
                            height: 100%;
                            color: #fff;
                            font-size: 14px;
                            font-weight: 400;
                            line-height: 20px;
                            cursor: pointer;
                            margin-bottom: 16px;
                            .hot-text{
                                flex: 1;
                                height: 70px;
                                border-radius: 8px;
                                background: #36384d;
                                padding: 15px 10px;
                                margin-right: 13px;
                                .ellipsis-2{
                                    white-space: normal;
                                    display: -webkit-box;
                                    -webkit-box-orient: vertical;
                                    -webkit-line-clamp: 2;
                                    line-clamp: 2;
                                    overflow: hidden;
                                    text-overflow: -o-ellipsis-lastline;
                                    text-overflow: ellipsis;
                                    word-break: break-all;
                                }
                            }
                        }
                    }
                    .refresh-but{
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 114px;
                        height: 38px;
                        border-radius: 19px;
                        border: 1px solid #bebfc3;
                        color: hsla(0,0%,100%,.7);
                        margin: 20px auto;
                        cursor: pointer;
                        .refresh{
                            margin-right: 8px;
                        }
                    }
                }
                .content-box.reverse{
                    background-color: #2a333c;
                    display: flex;
                    .msg-box{
                        align-self: flex-end;
                        flex-direction: row-reverse;
                        .avatar {
                            box-shadow: -3px -3px 6px #a4acbd, 3px 3px 6px #000;
                        }
                        .msg-content{
                            color: #0d6efd;// #30ffc4;
                            img{
                                width: 16px;
                                height: 16px;
                            }
                            .copy{
                                position: absolute;
                                left: -26px;
                                top: calc(50% - 18px);
                            }
                            .star{
                                position: absolute;
                                left: -50px;
                                right: inherit;
                                top: calc(50% - 18px);
                                cursor: pointer;
                            }
                        }
                    }
                }
                .content-box{
                    padding: 32px;
                    background-color: #36384d;
                    .msg-box{
                        position: relative;
                        width: 100%;
                        display: flex;
                        margin: 0 auto;
                        min-width: 386px;
                        max-width: 840px;
                        .avatar{
                            flex-shrink: 0;
                            width: 36px;
                            height: 36px;
                            border-radius: 4px;
                            overflow: hidden;
                            box-shadow: -3px -3px 6px #a4acbd, 3px 3px 6px #000;
                        }
                        .msg-content{
                            padding: 9px 10px;
                            margin: 0 16px;
                            border-radius: 4px;
                            position: relative;
                            max-width: 588px;
                            display: inline-block;
                            word-break: break-all;
                            -webkit-user-select: text;
                            user-select: text;
                            cursor: text;
                            color: #fff;
                            font-weight: 400;
                            img{
                                width: 16px;
                                height: 16px;
                            }
                            .copy{
                                position: absolute;
                                right: -26px;
                                top: calc(50% - 18px);
                                cursor: pointer;
                            }
                            .star{
                                position: absolute;
                                right: -50px;
                                top: calc(50% - 18px);
                                cursor: pointer;
                            }
                        }
                    }
                }

                .clear-list{
                    width: 100%;
                    height: 60px;
                    padding-bottom: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #fff;
                    font-size: 12px;
                    text-align: center;
                    letter-spacing: 1px;
                    text-decoration: underline;
                    .clear-text{
                        cursor: pointer;
                    }
                }
            }
        }
    }
    .foot-box{
        width: 100%;
        // background-color: #262837;
        padding: 26px 32px;
        .foot-box-content{
            margin: 0 auto;
            min-width: 386px;
            max-width: 840px;
            border-radius: 8px;
            background: #36384d;
            display: flex;
            justify-content: space-around;
            .textarea-box{
                width: 100%;
                padding: 8px 10px;
                color: #fff;
                border-radius: 5px;
                /deep/ .el-textarea__inner::-webkit-scrollbar {
                    display: none;
                }
                /deep/ .el-textarea__inner{
                    color: white;
                    background-color: #36384d;
                    border:none;
                    height: 36px;
                    line-height: 30px;
                    resize: none;
                    scrollbar-width: none;
                    -ms-overflow-style: none;
                }
                .textarea {
                    resize: none;
                    background: none;
                    color: inherit;
                    opacity: 1;
                    font: inherit;
                    line-height: inherit;
                    letter-spacing: inherit;
                    text-align: inherit;
                    text-indent: inherit;
                    text-transform: inherit;
                    text-shadow: inherit;
                    max-height: 120px;
                    overflow: auto;
                    height: auto;
                }
            }
            .send-btn-box{
                color: #919396;
                margin-right: 8px;
                font-size: 12px;
                line-height: 28px;
                margin: 11px 24px;
                cursor: pointer;
                .logo {
                    width: 40px;
                    height: 40px;
                    overflow: hidden;
                    display: block;
                    img {
                        // margin-top: -40px;
                    }
                }
            }
        }
    }
}
</style>

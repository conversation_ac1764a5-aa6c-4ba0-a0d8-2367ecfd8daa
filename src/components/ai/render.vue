<template>
  <div class="content-right">
    <div class="creation-warp" v-loading="loading || styleDataLoad">
      <div class="btn-wrap">
        <div class="creation-but active">
          AI智能渲染
          <div class="active-line"></div>
        </div>
        <div class="edit-but">
          <!-- AI后期 -->
          <div class="active-line"></div>
        </div>
      </div>
      <div class="ai-creation-wrap">
        <div class="ai-creation">
          <div class="ai-creation" style="padding: 0px 25px;">
              <div class="module module-pc">
                  <div class="sub-title size-title" style="margin-top:5px">
                      <div class="margin-left-size">
                        选择风格
                        <el-popover
                          v-if="tips"
                          popper-class="ai-popover"
                          placement="right"                      
                          width="200"
                          trigger="hover"
                          :title="tips.xzfg.title"
                          :content="tips.xzfg.content">
                          <div slot="reference" class="creation-query-icon" style="margin-left: 6px;">
                            <img src="@/assets/img/query-icon.png">
                          </div>
                        </el-popover>                       
                      </div>
                  </div>
                  <div class="classify-box" v-if="styleData.length">
                      <div class="scroll-box">
                        <div class="top-item ellipsis"
                        v-for="(item, index) in styleData"
                        :key="index"
                        :class="{ 'top-item-active': topItemActive === index}"
                        @click="topItemSelect(index, item.name)"
                        >{{item.name}}</div>
                      </div>
                      <div class="style-box">
                        <div class="style-item"
                        v-for="(item, index) in styleData[key].children"
                        :class="{ 'activeStyle': activeStyle === index}"
                        @click="styleSelect(index,item.name,item.promt,item.fpromt)" v-if="index < maxVisibleItems">
                          <div class="style-img">
                            <img :src="item.cover">
                          </div>
                          <div class="name">
                            <div class="name-text" style="height: 32px;">{{item.name}}</div>
                          </div>
                        </div>
                      </div>
                      <div class="u-lora open-lora" v-if="styleData[key].children.length > 6 && more" @click="open">
                        <span>更多</span>
                      </div>
                      <div class="u-lora put-lora" v-if="styleData[key].children.length > 6 && !more" @click="put">
                        <span>收起</span>
                      </div>
                  </div>
              </div>
              <div class="module module-pc">
                  <div class="sub-title size-title" style="margin-top:5px">
                      <div class="margin-left-size">
                        灵感描述<span style="font-size: 12px; color: rgb(170, 170, 170);">（请优先输入英文）</span>
                        <el-popover
                          v-if="tips"
                          popper-class="ai-popover"
                          placement="right"
                          width="200"
                          trigger="hover"
                          :title="tips.yjms.title"
                          :content="tips.yjms.content">
                          <div slot="reference" class="creation-query-icon" style="margin-left: 6px;">
                            <img src="@/assets/img/query-icon.png">
                          </div>
                        </el-popover>
                      </div>
                  </div>
                  <div class="textarea-class">
                    <el-input type="textarea" placeholder="请输入关键词，使用英文逗号分隔"  v-model="promt" autosize @input="handlePromt">
                    </el-input>
                    <div class="textarea-bottom">
                      <div></div>
                      <div class="bottom-right">
                        <div><span>{{promt.length}}/{{maxCharacters}}</span></div>
                        <div class="u-line" style="margin: 9px; border-left: 1px solid rgb(172, 172, 198); height: 10px; transform: scaleX(0.5); border-top-color: rgb(172, 172, 198); border-right-color: rgb(172, 172, 198); border-bottom-color: rgb(172, 172, 198);"></div>
                        <div class="u-icon u-icon--right">
                          <div @click="clearPromt" class="u-icon__icon uicon-trash" style="font-size: 16px; line-height: 16px; font-weight: normal; top: 0px; color: rgb(172, 172, 198);">
                            <i class="el-icon-delete" style="font-size: 14px;"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
              </div>
              <div class="module module-pc">
                  <div class="sub-title size-title" style="margin-top:5px">
                      <div class="margin-left-size">
                        反向描述<span style="font-size: 12px; color: rgb(170, 170, 170);">（请优先输入英文）</span>
                        <el-popover
                          v-if="tips"
                          popper-class="ai-popover"
                          placement="right"
                          width="200"
                          trigger="hover"
                          :title="tips.fhms.title"
                          :content="tips.fhms.content">
                          <div slot="reference" class="creation-query-icon" style="margin-left: 6px;">
                            <img src="@/assets/img/query-icon.png">
                          </div>
                        </el-popover>
                      </div>
                  </div>
                  <div class="textarea-class">
                    <el-input type="textarea" placeholder="请输入反向描述"  v-model="fpromt" autosize @input="handleFpromt">
                    </el-input>
                    <div class="textarea-bottom">
                      <div></div>
                      <div class="bottom-right">
                        <div><span>{{promt.length}}/{{maxCharacters}}</span></div>
                        <div class="u-line" style="margin: 9px; border-left: 1px solid rgb(172, 172, 198); height: 10px; transform: scaleX(0.5); border-top-color: rgb(172, 172, 198); border-right-color: rgb(172, 172, 198); border-bottom-color: rgb(172, 172, 198);"></div>
                        <div class="u-icon u-icon--right">
                          <div @click="clearFpromt" class="u-icon__icon uicon-trash" style="font-size: 16px; line-height: 16px; font-weight: normal; top: 0px; color: rgb(172, 172, 198);">
                            <i class="el-icon-delete" style="font-size: 14px;"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
              </div>
              <div class="module module-pc">
                  <div class="sub-title size-title" style="margin-top:5px">
                      <div class="margin-left-size">
                        上传风格参考图
                        <el-popover
                          v-if="tips"
                          popper-class="ai-popover"
                          placement="right"
                          width="200"
                          trigger="hover"
                          :title="tips.fwms.title"
                          :content="tips.fwms.content">
                          <div slot="reference" class="creation-query-icon" style="margin-left: 6px;">
                            <img src="@/assets/img/query-icon.png">
                          </div>
                        </el-popover>
                      </div>
                  </div>
                  <div style="position: relative;">
                    <el-upload
                      class="upload-demo"
                      :action="uploadUrl"
                      :on-success="handleSuccess"
                      :on-preview="handlePreview"
                      :on-remove="handleRemove"
                      :file-list="fileList"
                      :limit="uploadLimit"
                      accept=".jpg,.jpeg,.png"
                      list-type="picture">
                      <div class="creation-upload" style="width: 100%;">
                        <div><i class="el-icon-circle-plus"></i> 点击上传参考图</div>
                      </div>
                    </el-upload>
                    <!-- <el-select style="width: 30%;position: absolute;top:0;right: 0;" v-model="styleType" placeholder="请选择" :popper-append-to-body="false" >
                      <el-option
                        v-for="item in styleTypeArr"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select> -->
                  </div>
              </div>

              <div class="module module-pc">
                  <div class="slider-box">
                    <div class="creation-limit">
                      <div class="limit-text">
                        <div class="slider-sub">
                          <div>
                            <span>参考强度</span>
                          </div>
                          <el-popover
                            v-if="tips"
                            popper-class="ai-popover"
                            placement="right"
                            width="200"
                            trigger="hover"
                            :title="tips.fwqd.title"
                            :content="tips.fwqd.content">
                            <div slot="reference" class="creation-query-icon" style="margin-left: 6px;">
                              <img src="@/assets/img/query-icon.png">
                            </div>
                          </el-popover>
                        </div>
                        <el-input readonly type="number" :max="2" :min="0" v-model="weight1" class="number num"></el-input>
                      </div>
                      <div style="padding: 0px 8px;">
                          <div class="slider-limit">
                            <div class="u-slider">
                              <el-slider v-model="weight1" :max="2" :step="0.5"></el-slider>
                            </div>
                          </div>
                      </div>
                    </div>
                  </div>
              </div>
              <div class="module module-pc">
                  <div class="sub-title size-title" style="margin-top:5px">
                      <div class="margin-left-size">
                        上传外形参考图
                        <el-popover
                          v-if="tips"
                          popper-class="ai-popover"
                          placement="right"
                          width="200"
                          trigger="hover"
                          :title="tips.xmms.title"
                          :content="tips.xmms.content">
                          <div slot="reference" class="creation-query-icon" style="margin-left: 6px;">
                            <img src="@/assets/img/query-icon.png">
                          </div>
                        </el-popover>
                      </div>
                  </div>
                  <div style="position: relative;">
                    <el-upload
                      class="upload-demo"
                      :action="uploadUrl"
                      :on-success="handleSuccess2"
                      :on-preview="handlePreview"
                      :on-remove="handleRemove"
                      :file-list="fileList2"
                      :limit="uploadLimit"
                      accept=".jpg,.jpeg,.png"
                      list-type="picture">
                      <div class="creation-upload" style="width: 65%;">
                        <div><i class="el-icon-circle-plus"></i> 点击上传参考图</div>
                      </div>
                    </el-upload>
                    <el-select style="width: 30%;position: absolute;top:0;right: 0;" v-model="modelType" placeholder="请选择" :popper-append-to-body="false" >
                      <el-option
                        v-for="item in styleTypeArr"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </div>
              </div>
              <div class="module module-pc">
                  <div class="slider-box">
                    <div class="creation-limit">
                      <div class="limit-text">
                        <div class="slider-sub">
                          <div>
                            <span>参考强度</span>
                          </div>
                          <el-popover
                            v-if="tips"
                            popper-class="ai-popover"
                            placement="right"
                            width="200"
                            trigger="hover"
                            :title="tips.fwqd.title"
                            :content="tips.fwqd.content">
                            <div slot="reference" class="creation-query-icon" style="margin-left: 6px;">
                              <img src="@/assets/img/query-icon.png">
                            </div>
                          </el-popover>
                        </div>
                        <el-input readonly type="number" :max="2" :min="0" v-model="weight2" class="number num"></el-input>
                      </div>
                      <div style="padding: 0px 8px;">
                          <div class="slider-limit">
                            <div class="u-slider">
                              <el-slider v-model="weight2" :max="2" :step="0.5"></el-slider>
                            </div>
                          </div>
                      </div>
                    </div>
                  </div>
              </div>
              <div class="module module-pc">
                  <div class="sub-title size-title" style="margin-top:5px">
                      <div class="margin-left-size">
                        图像尺寸
                        <el-popover
                          v-if="tips"
                          popper-class="ai-popover"
                          placement="right"
                          width="200"
                          trigger="hover"
                          :title="tips.sjxl.title"
                          :content="tips.sjxl.content">
                          <div slot="reference" class="creation-query-icon" style="margin-left: 6px;">
                            <img src="@/assets/img/query-icon.png">
                          </div>
                        </el-popover>
                      </div>
                  </div>
                  <div>
                    <div class="creation-size">
                      <div class="size-content">
                        <div class="size-item ellipsis"
                        :class="{ 'active': size === 1}"
                        @click="size = 1">
                          <div class="size">
                            <div class="shape-warp">
                              <div class="shape" style="width: 16px; height: 16px;"></div>
                            </div>
                            1:1
                          </div>
                        </div>
                        <div class="size-item ellipsis"
                        :class="{ 'active': size === 2}"
                        @click="size = 2">
                          <div class="size">
                            <div class="shape-warp">
                              <div class="shape" style="width: 15px; height: 20px;"></div>
                            </div>
                            3:4
                          </div>
                        </div>
                        <div class="size-item ellipsis"
                        :class="{ 'active': size === 3}"
                        @click="size = 3">
                          <div class="size">
                            <div class="shape-warp">
                              <div class="shape" style="width: 20px; height: 15px;"></div>
                            </div>
                            4:3
                          </div>
                        </div>
                        <div class="size-item ellipsis"
                        :class="{ 'active': size === 4}"
                        @click="size = 4">
                          <div class="size">
                            <div class="shape-warp">
                              <div class="shape" style="width: 12px; height: 20px;"></div>
                            </div>
                            9:16
                          </div>
                        </div>
                        <div class="size-item ellipsis"
                        :class="{ 'active': size === 5}"
                        @click="size = 5">
                          <div class="size">
                            <div class="shape-warp">
                              <div class="shape" style="width: 20px; height: 12px;"></div>
                            </div>
                            16:9
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
              </div>
              <div class="module module-pc">
                  <div class="sub-title size-title" style="margin-top:5px">
                      <div class="margin-left-size">
                        图像质量
                        <el-popover
                          v-if="tips"
                          popper-class="ai-popover"
                          placement="right"
                          width="200"
                          trigger="hover"
                          :title="tips.yjxl.title"
                          :content="tips.yjxl.content">
                          <div slot="reference" class="creation-query-icon" style="margin-left: 6px;">
                            <img src="@/assets/img/query-icon.png">
                          </div>
                        </el-popover>
                      </div>
                  </div>
                  <div>
                    <div class="creation-quality">
                      <div class="quality-content">
                        <div class="quality-item"
                        :class="{ 'quality-active': quality === 1, 'ban': isVip}"
                        @click="quality = 1; deduct_integral = 0.01">
                          <span>普通</span>
                        </div>
                        <div class="quality-item"
                        :class="{ 'quality-active': quality === 2, 'ban': freeCount === 1 || isVip}"
                        @click="quality = 2; deduct_integral = 0.02">
                          <span>1.5倍</span>
                        </div>
                        <div class="quality-item"
                        :class="{ 'quality-active': quality === 3, 'ban': freeCount === 1}"
                        @click="quality = 3; deduct_integral = 0.03">
                          <span>2倍</span>
                        </div>
                      </div>
                    </div>
                  </div>
              </div>
              <div class="module module-pc" style="display: none;">
                  <div class="sub-title size-title" style="margin-top:5px">
                      <div class="margin-left-size">
                        高级参数
                      </div>
                      <el-switch v-model="gradeSwitch">
                      </el-switch>
                  </div>
              </div>
              <template>
                <div v-show="gradeSwitch">
                  <div class="module module-pc">
                      <div class="slider-box">
                        <div class="creation-limit">
                          <div class="limit-text">
                            <div class="slider-sub">
                              <div>
                                <span>描述相关性</span>
                              </div>
                              <div class="creation-query-icon" style="margin-left: 6px;">
                                <img src="@/assets/img/query-icon.png">
                              </div>
                            </div>
                            <el-input type="number" :max="30" :min="0" v-model="descNum" class="number num"></el-input>
                          </div>
                          <div style="padding: 0px 8px;">
                              <div class="slider-limit">
                                <div class="u-slider">
                                  <el-slider v-model="descNum" :max="30" :step="1"></el-slider>
                                </div>
                              </div>
                          </div>
                        </div>
                      </div>
                  </div>
                  <div class="module module-pc">
                      <div class="slider-box">
                        <div class="creation-limit">
                          <div class="limit-text">
                            <div class="slider-sub">
                              <div>
                                <span>采样方式</span>
                              </div>
                              <div class="creation-query-icon" style="margin-left: 6px;">
                                <img src="@/assets/img/query-icon.png">
                              </div>
                            </div>
                          </div>
                          <div style="margin-top:12px;position:relative">
                              <el-select v-model="takeType" placeholder="请选择" :popper-append-to-body="false" >
                                <el-option
                                  v-for="item in takeTypeArr"
                                  :key="item.value"
                                  :label="item.label"
                                  :value="item.value">
                                </el-option>
                              </el-select>
                          </div>
                        </div>
                      </div>
                  </div>
                  <div class="module module-pc">
                      <div class="slider-box">
                        <div class="creation-limit">
                          <div class="limit-text">
                            <div class="slider-sub">
                              <div>
                                <span>采样步数</span>
                              </div>
                              <div class="creation-query-icon" style="margin-left: 6px;">
                                <img src="@/assets/img/query-icon.png">
                              </div>
                            </div>
                            <el-input type="number" :max="30" :min="0" v-model="descNum" class="number num"></el-input>
                          </div>
                          <div style="padding: 0px 8px;">
                              <div class="slider-limit">
                                <div class="u-slider">
                                  <el-slider v-model="descNum" :max="30" :step="1"></el-slider>
                                </div>
                              </div>
                          </div>
                        </div>
                      </div>
                  </div>
                  <div class="module module-pc">
                      <div class="slider-box">
                        <div class="creation-limit">
                          <div class="limit-text">
                            <div class="slider-sub">
                              <div>
                                <span>VAE模型选择</span>
                              </div>
                              <div class="creation-query-icon" style="margin-left: 6px;">
                                <img src="@/assets/img/query-icon.png">
                              </div>
                            </div>
                          </div>
                          <div style="margin-top:12px;position:relative">
                              <el-select v-model="vae" placeholder="请选择" :popper-append-to-body="false">
                                <el-option
                                  v-for="item in vaeModel"
                                  :key="item.value"
                                  :label="item.label"
                                  :value="item.value">
                                </el-option>
                              </el-select>
                          </div>
                        </div>
                      </div>
                  </div>
                </div>
              </template>
              <div class="module module-pc" style="display: none;">
                  <div class="sub-title size-title" style="margin-top:5px">
                      <div class="margin-left-size">
                        生成数量
                      </div>
                      <div class="creation-query-icon" style="margin-left: 6px;">
                          <img src="@/assets/img/query-icon.png">
                        </div>
                  </div>
                  <div class="creation-limit">
                    <div class="limit-text">
                      <div class="name">
                        <span>数量</span>
                      </div>
                      <el-input class="number num" v-model="genCount" :max="4" :min="0" type="number"></el-input>
                    </div>
                    <div style="padding:0 8px">
                      <div class="slider-limit">
                        <div class="u-slider">
                          <el-slider v-model="genCount" :max="4" :step="1" show-stops></el-slider>
                        </div>
                      </div>
                    </div>
                  </div>
              </div>
              <div style="height: 94px;"></div>
          </div>
          <div class="creation-bottom">
            <div class="text-tips" style="display: flex;justify-content: space-between;">
              <div v-if="isVip">本次消耗0光子（共{{balance}}光子）</div>
              <div v-else>本次消耗{{deduct_integral}}光子（共{{balance}}光子）</div>
              <div style="text-align: right;" v-if="!isVip">免费使用{{freeCount}}次</div>
              <div style="text-align: right;" v-else>每周二维护</div>
            </div>
            <div class="creation-but" @click="genComfirm = true">
              <div>立即发光</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      title="温馨提示"
      :visible.sync="genComfirm"
      width="25%"
      :center="dialogText"
      top="30vh"
      @close="closeDialog"
      >
      <span v-if="isVip || freeCount">本次消耗0光子，生成1张图片</span>
      <span v-else>确定支付{{deduct_integral}}光子生成1张图片吗</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="startInterval">确 定</el-button>
      </span>
    </el-dialog>
    <div class="creation-center">
      <template v-if="genImg && !loading">
        <el-button @click="downloadImage" style="position: absolute;bottom: 4%;right: 10%;color:#fff" type="primary" plain >保存原图</el-button>
      </template>
      <div class="creation-box">
        <div class="gen-box" v-if="genImg && !loading" style="width: 90%; height: 90%; display: flex; justify-content: center;">
          <el-image
              :src="genImg"
              :preview-src-list="[genImg]">
            </el-image>
        </div>
        <div class="creation-warp2" v-else>
          <div v-if="!loading">
            <div class="creation-add">
              <img src="@/assets/img/ai-result-icon.png">
            </div>
            <div class="creation-text">
              <div>生成的图片将会显示在这里</div>
              <div class="tips">在左侧输入描述，创建你的作品吧</div>
            </div>
          </div>
          <div v-else>
            <div class="creation-text">
              <div style="margin-bottom: 10px;">AI创作中，请勿离开页面</div>
              <div class="tips" style="margin-bottom: 10px;">前方对列任务{{taskCount}}个，请耐心等待...</div>
              <el-progress :percentage="percentage" :show-text="false" style="width: 80%;margin: 15px auto;"></el-progress>
              <button @click="stopProgress" style="color: #fff;width: 100px;height: 40px;border-radius: 20px;border: 1px solid #fff;margin-top: 10px;margin-right:10px">取消制作</button>
              <button v-if="!isVip" @click="goVip" style="color: #fff;background: #0e6dfe;;width: 100px;height: 40px;border-radius: 20px;border: 1px solid #0e6dfe;margin-top: 10px;">立即加速</button>
              <span  style="color:#0e6dfe;margin-left: 20px;" v-else>VIP加速中</span>
            </div>
          </div>
          <!-- <input id="file" style="display: none;"> -->
        </div>
      </div>
    </div>
  </div>

</template>

<script>
// 引入通用 选项卡 组件
export default {
  name: "render",
  data() {
    return {
      galleryRoute: "ai",
      freeCount:0,
      isDisabled:true,
      dialogText:true,
      styleData: [],
      styleDataLoad:false,
      maxCharacters:600,
      maxVisibleItems: 6,
      topItemActive:0,
      activeStyle:null,
      key: 0,
      more:true,
      style:"",
      promt: "",
      fpromt: "",
      quality:1,
      size:1,
      gradeSwitch:false,
      genCount: 1,
      descNum: 7,
      weight1:0.5,
      weight2:0.5,
      takeStep: 20,
      takeType: 1,
      takeTypeArr:[{
          value: 1,
          label: '线稿控制'
        }, {
          value: 2,
          label: '深度控制'
        }, {
          value: 3,
          label: 'seg分割'
        }
      ],
      styleType: 1,
      modelType: 1,
      styleTypeArr:[{
          value: 1,
          label: '线稿控制'
        }, {
          value: 2,
          label: '深度控制'
        }, {
          value: 3,
          label: 'seg分割'
        }
      ],
      uploadLimit:2,
      uploadUrl:this.url + "/api/upload",
      vae: 1,
      vaeModel:[{
        value: 1,
        label: 'model1'
      },{
        value: 2,
        label: 'model2'
      },{
        value: 3,
        label: 'model3'
      }],
      genImg:'',
      loading:false,
      fileList:[],
      fileList2:[],
      deduct_integral:0.01,
      balance:0,
      image:'',
      image2:'',
      genComfirm:false,
      requestCount: 0, // 请求计数器
      maxRequests: 10, // 最大请求次数
      taskCount:60,
      maxCount:60,
      percentage:0,
      isRunning: false, // 是否正在运行进度
      intervalId: null, // 进度定时器ID
      isVip:true,
      requestIntervalId: null, //请求定时ID
      tips: null, // 提示信息
    };
  },
  props: {},
  beforeMount() {
    this.getBalance();
    this.getVip();
    this.getDatas();
    this.startProgress()
    this.getTips()
  },
  beforeDestroy() {
      // 在组件销毁前清除定时器
      this.clearInterval();
      clearInterval(this.intervalId);
  },
  methods: {
    // 获取static目录下tip.json文信息，模拟接口
    getTips() {
      this.$axios
        .get("/static/tip.json")
        .then((res) => {
          this.tips = res.data;
        })
        .catch((err) => {});
    },
    startProgress() {
      if (this.isRunning) return; // 如果已经在运行，则不执行

      this.isRunning = true;
      this.intervalId = setInterval(() => {
        if (this.percentage < 100) {
          this.percentage++; // 每次增加1%
        } else {
          // clearInterval(this.intervalId); // 达到100%后清除定时器
          // this.isRunning = false;
          this.percentage = 0;
        }
      }, 100); // 每100毫秒更新一次进度
    },

    // 停止进度条动画
    stopProgress() {
      clearInterval(this.intervalId); // 清除定时器
      this.clearInterval();
      this.isRunning = false;
      this.loading = false;
    },
    goVip() {
      clearInterval(this.intervalId); // 清除定时器
      this.clearInterval();
      this.$router.push("/ai/vip");
    },
    getBalance(){
      this.$axios
        .get(this.url + "/api/balance", {})
        .then((res) => {
          if (res.data.code === 200) {
            this.$nextTick(() => {
              this.balance = res.data.data
              // this.balance = res.data.data.balance;
              // this.freeCount = res.data.data.free_count;
            });
          }
        })
        .catch((err) => {});
    },
    getVip(){
      this.$axios
        .get(this.url + "/api/ai/vip", {})
        .then((res) => {
          if (res.data.code === 200) {
            this.$nextTick(() => {
              this.isVip = res.data.data.is_vip
              this.freeCount = res.data.data.free_img;
              if (this.isVip) {
                this.quality = 3
              }
            });
          }
        })
        .catch((err) => {});
    },
    getDatas() {
      this.styleDataLoad = true;
      this.$axios
        .get(this.url + "/api/ai/render_style", {})
        .then((res) => {
          if (res.data.code === 200) {
            this.$nextTick(() => {
              this.styleData = res.data.data;
              this.style = this.styleData[0].name;
              // if (this.styleData[0].children.length > 0) {
              //   this.style =  this.styleData[0].children[0].name;
              //   this.promt = this.styleData[0].children[0].promt;
              //   this.fpromt = this.styleData[0].children[0].fpromt;
              // }
            });
          }else {
            this.$message.warning({
              showClose: true,
              message: res.data.msg,
            });
          }
          this.styleDataLoad = false
        })
        .catch((err) => {
          this.$message.warning({
            showClose: true,
            message: '网络错误',
          });
        });
    },
    open() {
      this.more = false;
      this.maxVisibleItems = this.styleData[this.key].children.length;
    },
    put(){
      this.more = true;
      this.maxVisibleItems = 6;
    },
    topItemSelect(index, name){
      this.key = index;
      this.style = name;
      this.topItemActive = index;
      this.activeStyle = null;
    },
    styleSelect(index,name,promt,fpromt){
      this.activeStyle = index;
      // this.style = name;
      this.promt = promt;
      this.fpromt = fpromt;
    },
    closeDialog(){
      this.genComfirm = false;
    },
    handlePromt(){
      if(this.promt.length > this.maxCharacters) {
        this.promt = this.promt.slice(0, this.maxCharacters);
      }
    },
    clearPromt(){
      this.promt = ''
    },
    handleFpromt(){
      if(this.fpromt.length > this.maxCharacters) {
        this.fpromt = this.fpromt.slice(0, this.maxCharacters);
      }
    },
    clearFpromt(){
      this.fpromt = ''
    },
    // 启动定时器
    startInterval() {
      this.loading = true;
      this.genComfirm = false;
      this.generate()
      if (this.loading) {
        this.requestIntervalId = setInterval(() => this.generate(), 20000); // 20秒 = 20000毫秒
      }
    },
    // 清除定时器
    clearInterval() {
      this.requestCount = 0;
      this.taskCount = 60;
      // this.isRunning = false;
      clearInterval(this.requestIntervalId);
    },
    generate(){

        if (this.requestCount >= this.maxRequests) {
            this.loading = false
            this.clearInterval();
            this.$alert('生成失败，请稍后再试~', '', {});
            return false;
        }
        this.$axios
          .post(this.url + "/api/ai/generate", {
              style:this.style,
              promt:this.promt,
              fpromt:this.fpromt,
              quality:this.quality,
              size:this.size,
              image1:this.image,
              image2:this.image2,
              weight1:this.weight1,
              weight2:this.weight2,
              type1: this.styleType,
              type2: this.modelType
          })
          .then((res) => {
            if (res.data.code === 200) {
              // console.log(2222,res.data.data)
              if(res.data.data.img != '') {
                this.$nextTick(() => {
                  this.genImg = res.data.data.img;
                });
                this.getBalance();
                this.getVip();
                this.clearInterval();
                this.loading = false
              } else {
                this.requestCount++;
                if(this.isVip) {
                    this.taskCount = res.data.data.tasks;
                } else {
                    this.taskCount = this.maxCount - this.requestCount * 5 + res.data.data.tasks;
                }
                // console.log('reqc',this.requestCount)
              }
            } else if (res.data.code === 4001) {
              this.clearInterval();
              this.loading = false
              localStorage.removeItem("UserInfo");
              localStorage.removeItem("Authorization");
              this.$message.warning({
                showClose: true,
                message: res.data.msg,
              });
              setTimeout(() => {
                this.$router.push({name:"login"})
              },1000);
            } else {
              this.clearInterval();
              this.loading = false
              this.$message.warning({
                showClose: true,
                message: res.data.msg,
              });

              if (res.data.msg == '光子不足，请充值') {
                setTimeout(() => {
                  this.$router.push("/user/integral");
                },2000);
              }
            }

          })
          .catch((err) => {
            this.loading = false
            this.clearInterval();
            console.log(err)
            this.$alert('生成失败，请稍后再试哦~', '', {});
          });

    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePreview(file) {
      console.log(file);
    },
    handleSuccess(response, file, fileList){
      if (fileList.length > 1) {
        this.fileList = [fileList[fileList.length - 1]];
      }
      if(response.code == 200 && response.msg == "success") {
        this.image = response.data.filename
      }
    },
    handleSuccess2(response, file, fileList){
      if (fileList.length > 1) {
        this.fileList2 = [fileList[fileList.length - 1]];
      }
      if(response.code == 200 && response.msg == "success") {
        this.image2 = response.data.filename
      }
    },
    downloadImage() {
          // 将 base64 转换为 Blob 对象
          function dataURItoBlob(dataURI) {
            // 将 base64 解码
            var byteString;
            if (dataURI.split(',')[0].indexOf('base64') >= 0)
              byteString = atob(dataURI.split(',')[1]);
            else
              byteString = unescape(dataURI.split(',')[1]);

            // 写入数组缓冲区
            var mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];
            var ia = new Uint8Array(byteString.length);
            for (var i = 0; i < byteString.length; i++) {
              ia[i] = byteString.charCodeAt(i);
            }

            return new Blob([ia], {type:mimeString});
          }

          function getMimeTypeFromBase64(base64Data) {
            const base64Regex = /^data:(.+?);base64,/;
            const matches = base64Data.match(base64Regex);
            return matches ? matches[1].split('/')[1] : 'png';
          }
          // 创建一个 Blob 对象
          var blob = dataURItoBlob(this.genImg);

          // 创建一个包含 Blob 对象的 URL
          var url = window.URL.createObjectURL(blob);

          // 创建一个下载链接
          var link = document.createElement('a');
          var suffix = getMimeTypeFromBase64(this.genImg);
          link.href = url;
          link.download = '犀光AI_'+ Date.now() +'.' + suffix; // 指定下载文件的名称
          document.body.appendChild(link); // 将链接添加到 body 中（这是必须的）
          link.click(); // 模拟点击以触发下载

          // 清理工作（可选）
          document.body.removeChild(link); // 从 body 中移除链接
          window.URL.revokeObjectURL(url); // 释放创建的 URL 对象
      }

  },
  components: {},
};
</script>

<style lang="scss" scoped>

/* 改变遮罩层的背景色 */
/deep/ .el-loading-mask {
  background-color: rgba(0, 0, 0, 0)!important; /* 半透明黑色 */
}
/deep/ .el-upload {
  width: 100%;
}
/deep/ .el-dialog--center .el-dialog__body{
  text-align: center;
}
/deep/ .el-input--small .el-input__inner {
    height: 40px;
}
/deep/ .el-progress-bar__inner{
  /* 假设 Element UI 使用这个类来表示进度条的填充部分 */
  background: linear-gradient(to right, #4084fb, #53edd7); /* 从左到右的红色到黄色渐变 */
  /* 其他样式，如宽度、高度等，可以按需设置 */
}
.ban {
  pointer-events: none;
  opacity: 0.5;
}
.content-right {
  flex: 1;
  display: flex;
  height: 100%;
  .creation-warp{
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
    width: calc(100% - 32px);
    max-width: 372px;
    min-width: 335px;
    height: calc(100% - 32px);
    margin: 16px;
    border-radius: 24px;
    color: #fff;
    .btn-wrap{
        position: absolute;
        width: 100%;
        height: 56px;
        display: flex;
        justify-content: space-around;
        border-radius: 26px 26px 0 0;
        .creation-but{
          width: 50%;
          cursor: pointer;
          text-align: center;
          padding: 16px 0;
          .active-line{
              position: absolute;
              bottom: 0;
              left: calc(25% - 16px);
              width: 32px;
              height: 3px;
              border-radius: 2px;
              background: #0d6efd;
          }
        }
        .creation-but.active {
            color: #0d6efd;
            background-color: #151422;
            border-radius: 26px 30px 0 0;
            border: 2px solid #57596d;
            border-right: none;
            border-bottom: none;
            z-index: 9;
            &::after {
                content: "";
                position: absolute;
                left: calc(50% - 26px);
                top: 0;
                width: 52px;
                height: 53px;
                background-size: cover;
                background-color: #2a333c;
                background-image: url(data:image/png;base64,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);
            }
        }
        .edit-but {
            width: 50%;
            cursor: pointer;
            text-align: center;
            padding: 16px 0;
            .active-line {
                position: absolute;
                bottom: 0;
                left: calc(75% - 16px);
                width: 32px;
                height: 3px;
                border-radius: 2px;
                background: #0d6efd;
            }
        }
        .edit-but.active {
            color: #0d6efd;
            background-color: #151422;
            border-radius: 0 30px 0 0;
            border: 2px solid #57596d;
            border-left: none;
            border-bottom: none;
            z-index: 9;
            &::before {
              content: "";
              position: absolute;
              left: calc(50% - 28px);
              top: 0;
              background-size: cover;
              width: 56px;
              height: 52px;
              background-color: #2a333c;
              background-image: url(data:image/png;base64,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);
            }
        }
    }
    .ai-creation-wrap {
        position: relative;
        height: calc(100% - 50px);
        padding: 25px 0;
        background-color: #151422;
        margin-top: 50px;
        border: 2px solid #57596d;
        border-radius: 0 26px 26px 26px;
        z-index: 8;
        .ai-creation {
            position: relative;
            height: calc(100%);
            overflow-y: auto;
            .creation-bottom{
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                position: absolute;
                left: 0;
                right: 0;
                bottom: 0;
                z-index: 1009;
                height: 100px;
                border-radius: 25px;
                padding: 0 25px;
                padding-top: 20px;
                background: #151422;
                .text-tips {
                    font-size: 13px;
                    font-weight: 400;
                    line-height: 19px;
                    color: hsla(0,0%,100%,.72);
                }
                .creation-but {
                    height: 48px;
                    border-radius: 8px;
                    background: linear-gradient(140.27deg,#b721ff,#0d6efd);
                    font-size: 16px;
                    font-weight: 600;
                    line-height: 48px;
                    text-align: center;
                    color: #fff;
                    cursor: pointer;
                }
            }
            .module.module-pc{
                margin-bottom: 22px;
                .sub-title.size-title {
                    display: flex;
                    justify-content: space-between;
                    .margin-left-size{
                        display: flex;
                        align-items: center;
                    }
                }
                .sub-title{
                  display: flex;
                  align-items: center;
                  margin: 0 0 15px;
                  padding-left: 0;
                  font-size: 14px;
                  font-weight: 500;
                  line-height: 21px;
                  color: hsla(0,0%,100%,.9);
                  .creation-query-icon {
                      width: 14px;
                      // height: 14px;
                      margin-left: 6px;
                      cursor: pointer;
                  }
                }
                .classify-box{
                  .scroll-box {
                      padding: 0;
                      position: relative;
                      display: grid;
                      gap: 8px;
                      grid-template-columns: 1fr 1fr 1fr 1fr;
                      .top-item {
                          height: 36px;
                          padding: 0 5px;
                          border-radius: 8px;
                          background: #323347;
                          border: 1px solid #323347;
                          font-size: 12px;
                          font-weight: 400;
                          line-height: 36px;
                          text-align: center;
                          color: #fff;
                          cursor: pointer;
                      }
                      .top-item.top-item-active {
                          color: #0d6efd;
                          border: 1px solid #0d6efd;
                          background: #323347;
                      }
                  }
                  .style-box {
                      margin-top: 16px;
                      padding: 0;
                      display: grid;
                      gap: 8px;
                      grid-template-columns: 1fr 1fr 1fr;
                      .style-item {
                          position: relative;
                          padding-bottom: 90%;
                          border-radius: 8px;
                          overflow: hidden;
                          cursor: pointer;
                          .name {
                              height: 26px;
                              line-height: 26px;
                          }
                          &::after {
                              height: 26px;
                          }
                      }
                  }
                  .u-lora.open-lora {
                      margin: 16px auto 0;
                  }
                  .u-lora.put-lora {
                      margin: 16px auto 0;
                  }
                  .u-lora {
                      border-radius: 8px;
                  }
                }
                .textarea-class {
                    padding: 1px;
                    margin: 0 0 8px;
                    border-radius: 12px;
                    background: #262837;

                }
                .creation-upload {
                    margin: 0 0 12px;
                    height: 40px;
                    padding: 0 16px;
                    border-radius: 8px;
                }
                .creation-size {
                    position: relative;
                    .size-content {
                        position: relative;
                        display: grid;
                        gap: 8px;
                        grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
                        .size-item {
                            position: relative;
                            padding-bottom: 100%;
                            border-radius: 8px;
                            overflow: hidden;
                            background: #323347;
                            cursor: pointer;
                            .size{
                                display: flex;
                                flex-direction: column;
                                align-items: center;
                                justify-content: center;
                                position: absolute;
                                top: 0;
                                left: 0;
                                right: 0;
                                bottom: 0;
                                font-size: 13px;
                                font-weight: 400;
                                line-height: 19px;
                                color: #fff;
                                .shape-warp {
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    width: 20px;
                                    height: 20px;
                                    .shape {
                                        margin-bottom: 2px;
                                        border-radius: 2px;
                                        border: 1px solid hsla(0,0%,100%,.72);
                                    }

                                }
                            }
                        }
                        .size-item.active {
                            border: 1px solid #0d6efd;
                            .size {
                                color: #0d6efd;
                                .shape-warp {
                                  .shape {
                                        border: 1px solid #0d6efd;
                                    }
                                }
                            }
                        }
                    }
                }
                .creation-quality{
                  margin: 0;
                }
                .creation-limit {
                    .limit-text {
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                      .name {
                          font-size: 10px;
                          line-height: 15px;
                          color: hsla(0,0%,100%,.8);
                      }
                      .num {
                        width: 56px;
                      }
                      /deep/ .el-input__inner {
                          font-size: 13px;
                          font-weight: 500;
                          line-height: 13px;
                          color: hsla(0,0%,100%,.8);
                          display: flex;
                          align-items: center;
                          width: 100%;
                          height: 26px;
                          padding: 0 8px;
                          border-radius: 4px;
                          border: none;
                          background: #323347;
                          color: #fff;
                          cursor: pointer;
                      }
                    }
                }
                .slider-box {
                    margin: 0;
                    .creation-limit {
                      .limit-text {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        .slider-sub {
                            white-space: nowrap;
                            display: flex;
                            align-items: center;
                            font-size: 12px;
                            font-weight: 400;
                            line-height: 18px;
                            color: hsla(0,0%,100%,.8);
                            .creation-query-icon {
                                width: 14px;
                                // height: 14px;
                                cursor: pointer;
                            }
                        }
                      }
                    }
                }
                .sampling-content {
                    // display: flex;
                    // align-items: center;
                    // justify-content: space-between;
                    // height: 40px;
                    // padding: 0 8px 0 16px;
                    // border-radius: 8px;
                    // background: #323347;
                    // font-size: 14px;
                    // font-weight: 400;
                    // line-height: 21px;
                    // color: #fff;
                }
            }

            .module{
                position: relative;
                .sub-title {
                    position: relative;
                    margin: 24px 16px 16px 16px;
                    padding-left: 14px;
                    font-size: 16px;
                    font-weight: 600;
                    line-height: 24px;
                    color: #fff;
                    display: flex;
                    align-items: center;
                }
                .classify-box{
                  .scroll-box {
                      padding: 0 16px;
                      display: grid;
                      gap: 8px;
                      grid-template-columns: 1fr 1fr 1fr 1fr;
                      .top-item {
                        height: 32px;
                        padding: 4px;
                        border-radius: 16px;
                        border: 1px solid #747484;
                        font-size: 13px;
                        font-weight: 600;
                        line-height: 24px;
                        text-align: center;
                        color: #747484;
                      }
                      .top-item.top-item-active{
                          color: #171723;
                          border: 0;
                          background: linear-gradient(90deg,#baffd1,#7af0ff);
                      }
                  }
                  .style-box {
                      padding: 16px 16px 12px;
                      display: grid;
                      gap: 16px;
                      grid-template-columns: 1fr 1fr 1fr;
                      .style-item{
                          position: relative;
                          padding-bottom: 90%;
                          border-radius: 8px;
                          overflow: hidden;
                          cursor: pointer;
                          .style-img{
                              width: 100%;
                              height: 100%;
                              position: absolute;
                              img{
                                width:100%;
                              }
                          }
                          .name{
                              position: absolute;
                              left: 0;
                              right: 0;
                              bottom: 0;
                              height: 31px;
                              color: #fff;
                              font-size: 12px;
                              font-weight: 400;
                              line-height: 31px;
                              text-align: center;
                              color: #fff;
                              border-radius: 0 8px 8px 0;
                              z-index: 6;
                          }
                          &::after {
                              content: "";
                              position: absolute;
                              left: 0;
                              right: 0;
                              bottom: 0;
                              height: 32px;
                              background: hsla(0,0%,100%,.16);
                              -webkit-backdrop-filter: blur(3px);
                              backdrop-filter: blur(3px);
                              border-bottom-right-radius: 8px;
                              border-bottom-left-radius: 8px;
                              z-index: 2;
                          }
                      }
                      .style-item.activeStyle::before {
                          content: "";
                          position: absolute;
                          top: 0;
                          left: 0;
                          right: 0;
                          bottom: 0;
                          border-radius: 8px;
                          background: rgba(0,0,0,.64);
                          border: 1px solid#0d6efd;
                          z-index: 4;
                      }
                  }
                  .open-lora{
                      margin: 0 16px;
                  }
                  .put-lora{
                      width: 84px;
                      margin: 0 auto;
                  }
                  .u-lora {
                      display: flex;
                      justify-content: center;
                      align-items: center;
                      font-size: 13px;
                      font-weight: 500;
                      line-height: 19px;
                      color: #b2b1c1;
                      height: 32px;
                      border-radius: 16px;
                      background: #323347;
                  }
                }
                .textarea-class {
                    padding: 1px;
                    margin: 0 16px 6px;
                    border-radius: 8px;
                    background: #323347;
                    color: #fff;
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 21px;
                    .textarea-bottom {
                        height: 40px;
                        padding: 0 15px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        font-size: 11px;
                        font-weight: 400;
                        line-height: 16px;
                        color: #acacc6;
                        .bottom-right {
                            display: flex;
                            align-items: center;
                        }
                    }
                     /deep/ .el-textarea__inner {
                        min-height: 124px !important;
                        padding: 10px 15px;
                        border: none;
                        resize: none;
                        background: none;
                        color: inherit;
                        opacity: 1;
                        font: inherit;
                        line-height: inherit;
                        letter-spacing: inherit;
                        text-align: inherit;
                        text-indent: inherit;
                        text-transform: inherit;
                        text-shadow: inherit;
                        overflow-y: hidden;
                    }
                }
                .creation-upload{
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 16px;
                    height: 32px;
                    border-radius: 16px;
                    background: #323347;
                    font-size: 13px;
                    font-weight: 400;
                    line-height: 38px;
                    color: #b2b1c1;
                    cursor: pointer;
                }
                .creation-quality {
                  margin: 0 16px;
                  .quality-content {
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                      .quality-item {
                          width: 30%;
                          height: 36px;
                          display: flex;
                          align-items: center;
                          justify-content: center;
                          border-radius: 8px;
                          background: #323347;
                          border: 1px solid #323347;
                          font-size: 14px;
                          font-weight: 400;
                          letter-spacing: 0;
                          line-height: 20.27px;
                          color: hsla(0,0%,100%,.8);
                          cursor: pointer;
                      }
                      .quality-item.quality-active {
                          color: #0d6efd;
                          border: 1px solid #0d6efd;
                      }
                  }
                }
                .slider-box {
                    margin: 0 16px;
                }
            }
        }
    }
  }
  .creation-center{
    position: relative;
    flex: 3;
    display: flex;
    align-items: center;
    justify-content: center;
    .creation-box {
        position: relative;
        height: 80%;
        padding-right: 80%;
        border-radius: 16px;
        border: 2px dashed hsla(0,0%,100%,.16);
        overflow: hidden;
        .gen-box{
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
        .creation-warp2 {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
        }
        .creation-add {
            width: 126px;
            height: 88px;
            // margin-bottom: 16px;
            margin: 0 auto 16px;
        }
        .creation-text{
            text-align: center;
            font-size: 14px;
            font-weight: 400;
            line-height: 21px;
            margin-bottom: 6px;
            color: #fff;
            .tips {
                font-size: 12px;
                font-weight: 400;
                line-height: 18px;
                color: #a6a6a6;
            }
        }
    }
  }
}
.ellipsis {
    white-space: normal;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    overflow: hidden;
    text-overflow: -o-ellipsis-lastline;
    text-overflow: ellipsis;
    word-break: break-all;
    word-break: break-all;
}
.u-line {
    vertical-align: middle;
}
.u-icon--right {
    flex-direction: row;
    align-items: center;
}
.slider-limit{
    position: relative;
}
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}
/deep/ .el-select{
  display: block;
}
/deep/ .el-select-dropdown {
  white-space: nowrap;
  left: 0 !important;
  width: auto !important;
  max-width: none !important;
}
</style>
<style lang="scss">
.ai-popover{
  font-size: 12px;
  padding: 10px;
  .el-popover--plain {
     padding: 16px 20px;
  }
  .el-popover__title{
    font-size: 14px !important;
    margin-bottom: 8px;
  }
}
</style>
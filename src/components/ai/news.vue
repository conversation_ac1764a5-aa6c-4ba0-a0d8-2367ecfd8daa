<template>
  <div style="padding:0 30px;">
    <el-tabs v-model="activeIndex" @tab-click="handleClick" class="xg-tabs">
      <div style="font-size: 15px;color: #565f6e;" v-if="serach">犀光共为你找到{{pagination.total}}条结果</div>
      <el-tab-pane :label="tab.label" :name="tab.id" style="padding:60px 20%" v-for="tab in tabs" :key="tab.id">
        <el-card class="box-card" v-for="item in items" :key="item.id">
          <div class="card-container" @click.stop="details(item.id)">
            <el-row :gutter="20">
              <el-col :span="7">
                <!-- 图片部分 -->
                <img :src="item.images"  class="card-image">
              </el-col>
              <el-col :span="17">
                <!-- 标题和简介部分 -->
                <div class="card-content">
                  <p class="card-title">{{ item.title }}</p>
                  <p class="card-description" style="margin-bottom: 0;">{{ item.content }}</p>
                  <div class="card-bottom">
                    <p class="card-time">
                      {{ item.author }}
                      <el-divider direction="vertical"></el-divider>
                      {{item.time}}
                    </p>
                  </div>
                </div>

              </el-col>
            </el-row>
          </div>
        </el-card>
        <el-pagination
          background
          layout="prev, pager, next"
          :total="pagination.total"
          :page-size="pagination.limit"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="pagination.offset"
          style="text-align: center;"
          v-if="pagination.total > 0"
          >
        </el-pagination>
        <div class="nodata" v-else>
          暂无数据
        </div>
      </el-tab-pane>
      <!-- <el-tab-pane label="知识" name="1">知识</el-tab-pane>
      <el-tab-pane label="资讯" name="2">资讯</el-tab-pane> -->
    </el-tabs>

  </div>
</template>
<script>
export default {
  name: "news",
  components: {},
  data() {
    return {
      activeIndex: 0,
      serach:false,
      tabs:[
        {label:'综合',id:'0'},
        {label:'知识',id:'1'},
        {label:'资讯',id:'2'},
      ],
      pagination: {
        total: 0,
        offset: 0,
        limit: 4,
      },
      loading: false,
      // 选中的选项
      current: 0,
      //新闻列表
      items: [],
      keywords:'',

    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    handleClick(tab, event) {
        // console.log(tab, event);
        console.log(this.activeIndex)
        this.getData();
    },
    handleSizeChange(val) {
      this.pagination.limit = val;
    },
    // current page news info
    handleCurrentChange(val) {
      let page = val - 1;
      this.getData(this.pagination.limit, page);
    },

    details(id) {
      console.log(id)
      this.$router.push({ name: "aiNewsDetail", params: { id: id } });
    },

    // get news info list
    getData(limit = 4, offset = 0) {
      let keywords = this.$route.query.keywords;
      if (keywords) {
        this.serach = true
        this.$nextTick(function () {
          this.keywords = this.$route.query.keywords;
        });
      } else {
        keywords = ''
        this.serach = false
      }
      console.log(4444,keywords)
      this.$axios
        .post(this.url + "/api/ai/news", {
            limit: limit,
            offset: offset,
            cate: parseInt(this.activeIndex),
            keywords:keywords
        })
        .then((res) => {
          if (res.data.code === 200) {
            this.items = res.data.data.list;
            this.pagination.total = res.data.data.total;
          } else {
          }
        })
        .catch((err) => {});
    },
  },
  watch: {
    "$route": {
      handler (route) {
        this.getData()
      }
    }
  }
};
</script>

<style scoped lang="scss">
.el-tabs {
    // display: flex;
    // border-bottom: 1px solid #555;
    // justify-content: center;

    /deep/ .el-tabs__header {
      margin: 20px 0;
      border-bottom: 1px solid #555;
    }

    /deep/ .el-tabs__nav-wrap {
      padding: 0px !important;
    }

    /deep/ .el-tabs__item.is-active {
      color: #0d6efd !important;
      font-size: 16px;
      background: none;
      font-weight: bold;
      border-bottom: 2px solid;
      border-radius: 0;
    }

    /deep/ .el-tabs__item {
      color: #fff;
      padding: 0 10px;
      &:hover{
        border-bottom: 2px solid;
        border-radius: 0;
      }
    }

  }
.box-card {
  margin:0 20px;
  // border-radius: 5px;
  border: none;
  box-shadow:none;
  background-color: #2a333c;
  overflow: hidden;
  margin-bottom: 20px;
  // background: #151422;
  color: white;
  border: none;
  cursor: pointer;
  &:hover{
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transition: 0.3s;
  }
}
.card-container {
  height: 100px;
  // margin-bottom: 20px; /* 为每张卡片添加底部外边距 */
}

.card-image {
  width: 100%;
  height: 100px;
  border-radius: 8px;
  object-fit: cover; /* 确保图片填充整个容器，同时保持宽高比 */
}

.card-content {
  display: flex;
  flex-direction: column;
  justify-content: center; /* 垂直居中 */
}

.card-title {
  margin: 0; /* 移除标题的默认外边距 */
}

.card-description {
  margin-top: 10px; /* 为简介添加上边距 */
  color: #737881;
  font-size: 14px;
  position: relative;
}
.card-bottom{
  position: absolute;
  bottom: 0;
  .card-time{
    font-size: 12px;
    color: #565f6e;
    .el-divider{
      background-color: #565f6e;
    }
  }
}
.nodata{
  text-align: center;
}
</style>

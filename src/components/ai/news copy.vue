<template>
  <div>
    <el-card class="box-card" v-for="item in items" :key="item.id">
      <div class="card-container" @click.stop="details(item.id)">
        <el-row :gutter="20">
          <el-col :span="3">
            <!-- 图片部分 -->
            <img :src="item.images"  class="card-image">
          </el-col>
          <el-col :span="21">
            <!-- 标题和简介部分 -->
            <div class="card-content">
              <h5 class="card-title">{{ item.title }}</h5>
              <p class="card-description">{{ item.content }}</p>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
    <el-pagination
      background
      layout="prev, pager, next"
      :total="pagination.total"
      :page-size="pagination.limit"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page.sync="pagination.offset"
      style="text-align: center;"
      v-if="pagination.total > 0"
      >
    </el-pagination>
    <div class="nodata" v-else>
      暂无数据
    </div>
  </div>
</template>
<script>
export default {
  name: "news",
  components: {},
  data() {
    return {
      pagination: {
        total: 0,
        offset: 0,
        limit: 4,
      },
      loading: false,
      // 选中的选项
      current: 0,
      //新闻列表
      items: [],

    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    handleSizeChange(val) {
      this.pagination.limit = val;
    },
    // current page news info
    handleCurrentChange(val) {
      let page = val - 1;
      this.getData(this.pagination.limit, page);
    },

    details(id) {
      console.log(id)
      this.$router.push({ name: "aiNewsDetail", params: { id: id } });
    },

    // get news info list
    getData(limit = 4, offset = 0) {
      this.$axios
        .post(this.url + "/api/ai/news", {
            limit: limit,
            offset: offset,
        })
        .then((res) => {
          if (res.data.code === 200) {
            this.items = res.data.data.list;
            this.pagination.total = res.data.data.total;
          } else {
          }
        })
        .catch((err) => {});
    },
  }
};
</script>

<style scoped lang="scss">
.box-card {
  margin:0 20px;
  border-radius: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: 0.3s;
  background-color: #fff;
  overflow: hidden;
  margin-bottom: 20px;
  background: #151422;
  color: white;
  border: none;
  cursor: pointer;
}
.card-container {
  // margin-bottom: 20px; /* 为每张卡片添加底部外边距 */
}

.card-image {
  width: 100%;
  height: auto;
  object-fit: cover; /* 确保图片填充整个容器，同时保持宽高比 */
}

.card-content {
  display: flex;
  flex-direction: column;
  justify-content: center; /* 垂直居中 */
}

.card-title {
  margin: 0; /* 移除标题的默认外边距 */
}

.card-description {
  margin-top: 10px; /* 为简介添加上边距 */
}
.nodata{
  text-align: center;
}
</style>

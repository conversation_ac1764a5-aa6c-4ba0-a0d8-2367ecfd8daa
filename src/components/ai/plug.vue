<template>
  <!-- Ai插件 -->
  <div class="content">
    <!-- <h2>插件</h2> -->
    <div id="video-el-col-l" class="main-video">
        <div>
            <div id="video" class="player" v-if="Object.keys(info).length !== 0">
                <ali-player
                    ref="player"
                    :source="source"
                    :cover="cover"
                    width="100%"
                    height="100%"
                    :preload="true"
                    :useH5Prism="true"
                    class="video"
                >
                </ali-player>
            </div>
        </div>
    </div>
    <div class="list">
        <div class="xg-btn-group">
            <div class="xg-btn" @click="dialogTableVisible = true">插件下载</div>
            <div class="xg-btn" @click="dialogmanual = true">安装说明</div>
        </div>
    </div>
    <!-- 下载 弹框 -->
    <div class="plug-open download-open" @click="dialogTableVisible = false" :class="{ active: dialogTableVisible }">
      <div class="plug-box" @click.stop>
        <div class="close el-icon-close" @click="dialogTableVisible = false"></div>
        <div class="open-title">插件下载</div>
        <el-table :data="gridData" class="list" height="280">
          <el-table-column prop="version_code" label="版本" width="140">
          </el-table-column>
          <el-table-column prop="platform" label="平台" width="120">
          </el-table-column>
          <el-table-column prop="time" label="时间" width="90"> </el-table-column>
          <el-table-column prop="url" label="下载" width="50">
            <template slot-scope="scope">
              <a
                  class="el-icon-download"
                  @click="downloadCount('download_package')"
                  :href="scope.row.download_link"
                  target="_blank"

              ></a>
              <!-- <a @click.prevent="tips" target="_blank" v-else-if="info.download" class="el-icon-download"
              ></a
              >
              <a @click.prevent="needLogin" target="_blank" v-else class="el-icon-download"></a> -->
            </template>
          </el-table-column>
        </el-table>
        <!-- 累计数量 -->
        <div class="download-count" v-if="info.download_package>0">
          累计下载数：<strong>{{ info.download_package || 0 }}</strong>
        </div>
      </div>
    </div>

    <!-- 使用手册 弹框 -->
    <div class="plug-open manual-open" @click="dialogmanual = false" :class="{ active: dialogmanual }">
      <div class="plug-box" @click.stop>
        <div class="close el-icon-close" @click="dialogmanual = false"></div>
        <div class="open-title">安装说明</div>
        <el-table :data="manualData" class="list" height="280">
          <el-table-column prop="version_code" label="版本"> </el-table-column>
          <el-table-column prop="time" label="时间"> </el-table-column>
          <el-table-column prop="url" label="下载" width="50">
            <template slot-scope="scope">
              <a
                  :href="scope.row.download_link"
                  @click="downloadCount('download_manual')"
                  target="_blank"
                  class="el-icon-download"
              ></a>
              <!-- <a @click.prevent="tips" target="_blank" v-else-if="info.download" class="el-icon-download c1"
              ></a
              >
              <a @click.prevent="needLogin" target="_blank" v-else class="el-icon-download c2"></a> -->
            </template>
          </el-table-column>
        </el-table>
        <!-- 累计数量 -->
        <div class="download-count" v-if="info.download_manual>0">
          累计下载数：<strong>{{ info.download_manual || 0 }}</strong>
        </div>
      </div>
    </div>
  </div>

</template>

<script>
// 引入通用 选项卡 组件
import Aliplayer from "../../components/curriculum/Aliplayer.vue";
export default {
  name: "plug",
  data () {
    return {
        // info
      info: {},
        //下载
      gridData: [],
      //使用手册
      manualData: [],
      source: "https://radirhino.oss-cn-beijing.aliyuncs.com/uploads/1.1课程相关的软件、插件介绍与版本要求.mp4",
      cover:"https://radirhino.oss-cn-beijing.aliyuncs.com/uploads/3c0750fbe2f62a281916e096722f795.jpg",
      dialogTableVisible: false,
      dialogmanual:false
    };
  },
  props: {

  },
  created() {


  },
  beforeMount () {
    this.getData();
  },
  mounted() {},
  methods: {
    needLogin() {
      localStorage.removeItem("UserInfo");
      localStorage.removeItem("Authorization");
      this.$router.push({ name: "login" });
    },
    tips() {
      this.$message.warning({
        showClose: true,
        message: "暂无权限",
      });
    },
    getData() {
      this.loading = true;
      this.$axios
        .get(this.url + "/api/ai/plug", {
          params: {
          },
        })
        .then((res) => {
          if (res.data.code === 200) {
            // console.log(1111,res.data.data)
            this.info = res.data.data
            if (res.data.data.images) {
              this.cover = res.data.data.images
            }
            if (res.data.data.link) {
              this.source = res.data.data.link
            }
            if(this.info.detail.length > 0) {
              this.info.detail.forEach((item, key)=>{
                if(item.type == 'package'){
                  this.gridData.push(item)
                }else if(item.type == 'manual'){
                  this.manualData.push(item)
                }
              });
              console.log(333,this.manualData)
            }
          }
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
        });
    }

  },
  components: {
      "ali-player": Aliplayer,
  },
};

</script>

<style lang="scss" scoped>

.content {
    text-align: center;
    height: 100%;
    position: relative;
    padding: 20px 0;
    h2 {
        margin-bottom: 20px;
    }
    .main-video {
        width: 50%;
        // height: 500px;
        flex-shrink: 0;
        margin: 0 auto;
        .player{
            height: 400px;
        }
    }
    .list{

    }

    .xg-btn-group {
        width: 50%;
        margin: 100px auto;
      .xg-btn {
        position: relative;
        color: #fff;
        font-size: 14px;
        border: 2px solid #c9c9c9;
        border-radius: 6px;
        display: block;
        margin-bottom: 15px;
        text-align: left;
        cursor: pointer;
        outline: none;
        padding-left: 20px;
        padding-right: 20px;
        height: 50px;
        line-height: 50px;
        &::before,
        &::after {
          content: "";
          position: absolute;
          right: 20px;
          top: 50%;
          transform: translateY(-50%);
          display: block;
        }
        &::before {
          width: 14px;
          height: 14px;
          border-top: 2px solid #c9c9c9;
          border-right: 2px solid #c9c9c9;
          margin-top: -2px;
          transform: rotate(45deg) translateY(-50%);
        }
        &::after {
          width: 20px;
          height: 2px;
          background: #c9c9c9;
          transition: all 0.4s;
          margin-right: -6px;
        }
        &:hover {
          color: #fff;
          background: #0084ff;
          border-color: #0084ff;
          &:before {
            border-color: #fff;
          }
          &:after {
            background: #fff;
            width: 26px;
          }
        }
      }
    }
    // 案例下载弹窗
  .plug-open{
    position: absolute;
    top:0;
    left:0;
    width:100%;
    height:100%;
    display:flex;
    align-items:center;
    justify-content:center;
    z-index:1001;
    transition: all 0.4s cubic-bezier(0.18, 0.89, 0.32, 1.28);
    transform: scale(0.8);
    visibility: hidden;
    opacity: 0;

    &.active {
      transform: scale(1.3);
      opacity: 1;
      visibility: visible;
    }
  }
  .plug-box {
    position:relative;
    text-align: left;
    width:100%;
    max-width: 460px;
    // height: 380px;
    background: #fff;
    box-shadow: 0px 0px 30px 0px rgba(4, 0, 0, 0.3);
    border-radius: 6px;
    padding: 30px 25px;
    .close {
      font-size: 30px;
      color: #96a1ac;
      position: absolute;
      top: 5px;
      right: 10px;
      cursor: pointer;
    }
    .open-title {
      color: #0084ff;
      line-height: 1.2;
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 15px;
    }
    /deep/ .el-table {
      &::before{display:none;}
      .cell {
        text-align: left !important;
        color: #909399;
        font-size: 12px;
      }
      th .cell {
        color: #4f5760;
        font-weight: bold;
      }
      .el-icon-download {
        font-size: 20px;
        color: #0084ff;
        text-align: center;
      }
      .el-table__cell{padding-top:10px;padding-bottom:10px;}
      th.el-table__cell{padding-bottom: 15px;}
      .el-table__body-wrapper{
        &::-webkit-scrollbar {
          /*滚动条整体样式*/
          width: 4px; /*高宽分别对应横竖滚动条的尺寸*/
          height: 1px;
        }
        &::-webkit-scrollbar-thumb {
          /*滚动条里面小方块*/
          border-radius: 10px;
          background-color: #b8b8b8;
        }
        &::-webkit-scrollbar-track {
          /*滚动条里面轨道*/
          box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
          background: #f4f4f4;
          border-radius: 10px;
        }
      }
    }

    .head {
      color: #4f5760;
      font-size: 12px;
      line-height: 1.2;
      display: flex;
      align-items: center;
      padding-bottom: 20px;
      font-weight: bold;
      & > div {
        flex-grow: 1;
      }
      .icon {
        flex-shrink: 0;
        width: 40px;
      }
    }
  }
}
</style>

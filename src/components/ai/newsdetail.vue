<template>
  <div class="xg-ny xg-news-detail">
    <div class="xg-box">
      <div class="newsPre" :class="{'unclickable' : !info.prev}" @click="jump(info.prev)"><i class="el-icon-arrow-left" ></i></div>
      <div class="newsNext" :class="{'unclickable' : !info.next}" @click="jump(info.next)"><i class="el-icon-arrow-right"></i></div>
      <div class="xg-head">
        <div class="title">{{ info.title }}</div>
        <div class="info">
          <!-- 作者 -->
          <div class="author">来源：{{ info.author }}</div>
          <el-divider direction="vertical"></el-divider>
          <!-- 时间 -->
          <div class="xg-time">{{ info.time }}</div>
        </div>
      </div>
      <!-- 正文 -->
      <div class="xg-body" v-html="info.content"></div>
      <!-- 切换文章 -->
      <div class="xg-btn-group">
        <!-- <div class="xg-line-1 xg-btn" v-if="info.prev" @click="jump(info.prev.id)">上一篇：{{ info.prev.title }}</div> -->
        <!-- <div class="xg-line-1 xg-btn" v-if="info.next" @click="jump(info.next.id)">下一篇：{{ info.next.title }}</div> -->
        <div style="font-size: 15px;color: #fff;margin-bottom: 15px;">相关文章</div>
        <ol>
          <li v-for="item in info.cateList"><el-link @click="jump(item)">{{item.title}}</el-link></li>
        </ol>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "newsdetail",
  components: {},
  data() {
    return {
      info:{},
    };
  },
  mounted() {
    this.getNewsInfo();
  },
  methods: {
    //  跳转
    jump(obj){
      if(!obj) {
        this.$message.warning({
          showClose: true,
          message: '已经没有更多了',
        });

        return
      }
      if (this.$route.params.id!=obj.id){
        this.$router.push({ name: 'aiNewsDetail', params: {id:obj.id}})
      }
    },

    getNewsInfo() {
      var id = this.$route.params.id;
      if (id) {
        this.$axios
          .get(this.url + "/api/ai/news/" + id, {
            params: {},
          })
          .then((res) => {
            if (res.data.code === 200) {
              this.info = res.data.data;
            }
          })
          .catch((err) => {});
      }
    },
  },
  watch: {
    $route: {
      handler(route) {
        this.getNewsInfo();
      },
    },
  },
};
</script>

<style scoped lang="scss">
.unclickable {
  // pointer-events: none;
}
.newsdetail {
  margin: 80px 20%;
  background: white;
  min-height: 600px;
  padding: 40px 20px;
  .top {
    margin-top: 50px;
  }
  .clearfix {
    content: "";
    display: block;
    overflow: hidden;
    clear: both;
  }
}
.newstext {
  img {
    max-width: 100% !important;
  }
  .mainp {
    text-align: left;
    text-indent: 2em !important;
  }
}
.navbar-expand-lg .navbar-collapse {
  display: flex;
  justify-content: space-between;
}
.navbar {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  width: 100%;
  /*position: relative;*/
  /*top: 0;*/
  z-index: 99;
  position: absolute;
  top: 0;
  left: 0;
}
button {
  outline: none;
}
.bg-info {
  /*background-color: rgba(0,0,0,0) !important;*/
  background-color: rgb(17, 19, 42) !important;
}
.navbar-dark .navbar-brand,
.navbar-dark .navbar-nav .nav-link {
  color: white;
}
.btn-success {
  color: white !important;
  background: none !important;
  border: none !important;
  margin-top: 5px;
}

@media only screen and (max-width: 900px) {
  .newsdetail {
    margin: 40px 5%;
    background: white;
    min-height: 600px;
    padding: 20px 10px;
  }
  .navbar-expand-lg .navbar-collapse {
    display: block;
  }
  .navbar-dark .navbar-toggler {
    color: rgba(0, 0, 0, 0.5);
    border-color: rgba(0, 0, 0, 0.5);
  }
  /deep/.navbar-dark .navbar-toggler-icon {
    background-image: url("../../assets/img/nav.png");
  }
  .newstext {
    margin: 20px;
  }
}

/* ==================== 内页 - 新闻详情 start ==================== */
.xg-news-detail{
  background: #2a333c;
  border-top: 1px solid #555;
  margin: 10px 40px;
  &>.xg-box{
    width:66%;
    background-color: #39414c;
    box-shadow: 0px 0px 20px 0px rgba(4, 0, 0, 0.2);
    border-radius: 20px;
    padding:35px 40px;
    position: relative;
    .newsPre{
      position: absolute;
      cursor: pointer;
      top:50%;
      left: -60px;
      i {
        font-size: 30px;
        color: #555;
        font-weight: bold;
      }
      &:hover{
        i {
          color: #0d6efd;
        }
      }
    }
    .newsNext{
      position: absolute;
      cursor: pointer;
      top:50%;
      right: -60px;
      i {
        font-size: 30px;
        color: #555;
        font-weight: bold;
      }
      &:hover{
        i {
          color: #0d6efd;
        }
      }
    }
  }
  .xg-head{
    text-align:left;
    padding-bottom:20px;
    border-bottom:1px solid #565f6e;
    width:100%;
    margin-bottom:20px;
    background:url(../../assets/img/ny-bg.png) no-repeat right top;
    background-size:40px;
  }
  .title{
    font-size:18px;
    color:#fff;
    line-height:1.6;
    margin-bottom: 10px;
  }
  .info{
    display:flex;
    align-items: center;
    font-size: 12px;
    color: #565f6e;
    .el-divider{
      background-color: #565f6e;
    }
  }
  .xg-body{font-size:13px;color:#989ea4;line-height:1.8;}
  .xg-btn-group{
    // display:flex;
    // align-items:center;
    // justify-content:space-between;
    color:#989ea4;
    margin-top:45px;
    border-top:1px solid #565f6e;
    padding-top:30px;
    ol,li{
      list-style: disc;
    }
    li{
      height: 30px;
      line-height: 30px;
      .el-link.el-link--default {
        color:#989ea4;
      }
      &:hover{
        .el-link.el-link--default {
          color:#0d6efd;
        }
      }
    }
    li a{
      height: 20px; /* 设置列表项的高度 */
      line-height: 20px;
      margin-bottom: 10px;
      &:hover{
        text-decoration: underline!important;

      }
    }
  }
  .xg-btn{
    width:430px;
    height:40px;
    line-height:40px;
    border-radius:100px;
    border:1px solid #dbdddf;
    color:#88929e;
    transition:all .4s;
    font-size:13px;
    text-align: left;
    padding-left:20px;
    padding-right:20px;
    cursor:pointer;
    &:hover{color:#0d6efd;border-color:#0d6efd;}
  }
}
@media screen and (max-width: 1300px) {
    .xg-news-detail{
      &>.xg-box{width:80%;}
    }
}
@media screen and (max-width: 751px) {
    .xg-news-detail{
      padding:30px 20px;
      &>.xg-box{width:80%;box-shadow: 0px 0px 10px 0px rgba(4, 0, 0, 0.2);padding:30px 25px;}
    }
    .xg-news-detail .title{font-size:16px;line-height:1.6;font-weight:bold;padding-bottom:15px;}
    .xg-news-detail .info{font-size:13px;}
    .xg-news-detail .xg-head{margin-bottom:15px;padding-bottom:20px;}
    .xg-news-detail .xg-btn-group{margin-top:20px;padding-top:10px;flex-wrap:wrap;}
    .xg-news-detail .xg-btn-group .xg-btn{width:100%;height:30px;line-height:30px;font-size:13px;margin-top:15px;}
}
/* ==================== 内页 - 新闻详情 end ==================== */
</style>

<template>
  <div class="user-course">
    <!-- 我的课程 -->
    <div class="user-head">
      <!-- 标题 -->
      <div class="title">
        <div class="icon"></div>
        <div class="text">我的课程</div>
      </div>
    </div>
    <div class="main">
      <el-tabs v-model="activeName" class="user-tabs" @tab-click="handleClick">
        <!-- 我的学习 -->
        <el-tab-pane label="我的学习" name="learn">
          <div class="list">
            <div class="list-item" v-for="(item, index) in items" :key="index">
              <div class="xg-image image" v-if="item.course">
                <img :src="item.course.images_src" alt="" />
              </div>
              <div class="info">
                <!-- 标题 -->
                <div class="xg-line-2 title" v-if="item.course">{{ item.course.name }}</div>
                <!-- 进度 -->
                <div class="progress-rate">
                  <!--  -->
                  <div class="xg-flex text">
                    <div class="text1">当前进度</div>
                    <div class="text2">{{ item.progress }}%</div>
                  </div>
                  <!-- 进度条 -->
                  <el-progress
                    class="xg-progress"
                    :percentage="item.progress"
                    :show-text="false"
                  ></el-progress>
                  <!-- 按钮 -->
                  <div class="btn-group"  v-if="item.course">
                    <el-button type="primary" @click="jump(item.course.id)"
                      >继续学习</el-button
                    >
                    <el-button>取消学习</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 分页 -->
          <el-pagination
            background
            class="xg-pagination"
            layout="prev, pager, next,jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-size="pagesize"
            :total="total"
          >
          </el-pagination>
        </el-tab-pane>
        <!-- 我的收藏 -->
        <el-tab-pane label="我的收藏" name="collect" class="tab-pane2">
          <div class="list">
            <div class="list-item" v-for="(item, index) in items" :key="index">
              <div class="xg-image image" v-if="item&&item.course">
                <img v-if="item.course" :src="item.course.images_src" alt="" />
              </div>
              <div class="info" v-if="item&&item.course">
                <div class="title">{{ item.course.name }}</div>
                <el-button @click="cancelCollect(item.id)">取消收藏</el-button>
              </div>
            </div>
          </div>
          <!-- 分页 -->
          <el-pagination
            background
            class="xg-pagination"
            layout="prev, pager, next,jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-size="pagesize"
            :total="total"
          >
          </el-pagination>
        </el-tab-pane>


        <!-- 我的素材 -->
        <el-tab-pane label="我的素材" name="material" class="tab-pane2">
          <div class="list mygoods">
            <div class="list-item" v-for="(item, index) in items" :key="index">
              <div class="xg-image image" >
                <img v-if="item.cover" :src="domain +item.cover" alt="" />
              </div>
              <div class="info" >
                <div class="title">{{ item.name }}</div>
                <el-button @click="showGoods(item.id)">查看详情</el-button>
              </div>
            </div>
          </div>
          <!-- 分页 -->
          <el-pagination
            background
            class="xg-pagination"
            layout="prev, pager, next,jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-size="pagesize"
            :total="total"
          >
          </el-pagination>
        </el-tab-pane>

        <el-tab-pane
          label="我的上传"
          name="upload"
          v-if="user && (user.plug || user.course || user.goods)"
        >
          <div class="upload-box">
            <el-tabs v-model="activeName2"  @tab-click="handleClick2">
              <el-tab-pane label="首页" name="home">
                <div class="upload-home">

                  <div class="upload-body">
                    <div class="upload-body-content">
                      <div class="upload-wrp">
                        <div class="bcc-upload upload">
                          <div class="bcc-upload-wrapper">
                            <div v-if="config.desc" v-html="config.desc">
                              
                            </div>
                           
                          </div>
                        </div>
                      </div>
                   
                      <div class="banner-entry" data-reporter-id="27" v-if="config.url">
                        <!-- <svg
                          
                          class="banner-entry-icon icon-sprite icon-sprite-bcut_icon"
                        >
                          <use
                            
                            xlink:href="@/assets/img/"
                          ></use>
                        </svg> -->
                        <img :src="domain + config.image" alt="" class="banner-entry-icon icon-sprite icon-sprite-bcut_icon">
                        <div class="banner-entry-text">
                          <p class="main-text">{{config.title}}</p>
                          <div class="sub-text" style="cursor: unset">
                            <span >{{config.itemdesc}}</span
                            >
                          </div>
                        </div>
                        <div class="banner-entry-right">
                          <a
                            :href="config.url"
                            target="_blank"
                            ><span class="enter-btn" data-reporter-id="26"
                              >立即下载</span
                            ></a
                          >
                        </div>
                        
                    
                      </div>
                      <br/>
                      <br/>
                      <div class="footer-item">
                      <div class="list">
                          <router-link
                            :to="{ name: 'help',query:{type:'service'} }"
                            class="item"
                            >服务条款</router-link>
                          <router-link
                            :to="{ name: 'help',query:{type:'private'} }"
                            class="item"
                            >隐私协议</router-link>
                          <router-link
                              :to="{ name: 'help',query:{type:'upload_statement'} }"
                              class="item"
                          >上传声明</router-link>
                          <router-link
                              :to="{ name: 'help',query:{type:'copyright_statement'} }"
                              class="item"
                          >版权声明</router-link>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="视频投稿" name="video">
                  <course :user="user" />
              </el-tab-pane>
              <el-tab-pane label="插件投稿" name="plug"><videos :user="user" /></el-tab-pane>
              <el-tab-pane label="素材投稿" name="goods"><goods :user="user" /></el-tab-pane>
           
            </el-tabs>
          </div>
        </el-tab-pane>
        <el-tab-pane  name="disableview"
          v-if="user && !user.plug && !user.course && !user.goods"
        >

          <span slot="label" class="disable-upload"> 我的上传</span>
          <div class="disable-upload-box">
            <div class="disable-upload-wrp">
              请联系管理员开通权限，微：18511861278
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="投稿管理" name="putlist">

        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import goods from './uploads/goods'
import videos from './uploads/videos'
import course from './uploads/course'

export default {
  components: {videos, goods, course},
  data() {
    return {
      pagesize: 10,
      page: 1,//投稿
      total: 0,
      user: {},
      // activeName: "learn",
      activeName: "learn",
      // activeName: "disableview",
      activeName2: "home",
      items: [],
      domain:'',
      footerhtml:'<div data-v-4db96594="" class="footer-item"><span data-v-4db96594="" class="i-list i-1"><a data-v-4db96594="" target="_blank" class="title">禁止发布的视频内容</a></span><span data-v-4db96594="" class="i-list i-1"><span data-v-4db96594="" class="title">视频大小</span><span data-v-4db96594="" class="title-block"><span data-v-4db96594="">网页端上传的文件大小上限为8G</span><br data-v-4db96594=""><span data-v-4db96594="">视频内容时长最大10小时</span><br data-v-4db96594=""><span data-v-4db96594="">（提升电磁力，即可体验32G超大文件上限哦！前往创作实验室了解更多）</span><br data-v-4db96594=""></span></span><span data-v-4db96594="" class="i-list i-1"><span data-v-4db96594="" class="title">视频格式</span><span data-v-4db96594="" class="title-block"><span data-v-4db96594="">网页端、桌面客户端推荐上传的格式为：mp4,flv</span><br data-v-4db96594=""><span data-v-4db96594="">（推荐上传的格式在转码过程更有优势，审核过程更快哦！）</span><br data-v-4db96594=""><span data-v-4db96594="">其他允许上传的格式：mp4,flv,avi,wmv,mov,webm,mpeg4,ts,mpg,rm,rmvb,mkv,m4v</span><br data-v-4db96594=""></span></span><span data-v-4db96594="" class="i-list i-1"><span data-v-4db96594="" class="title">视频码率</span><span data-v-4db96594="" class="title-block"><span data-v-4db96594="">分辨率最大支持 8192*4320</span><br data-v-4db96594=""><span data-v-4db96594="">推荐视频分辨率：1920*1080 或者 3840*2160</span><br data-v-4db96594=""><span data-v-4db96594="">推荐视频码率：1080p大于6000kbps；4k大于20000kbps；8k大于40000kbps</span><br data-v-4db96594=""><span data-v-4db96594="">音频采样率48000Hz</span><br data-v-4db96594=""><span data-v-4db96594="">推荐音频码率：320kbps</span><br data-v-4db96594=""><span data-v-4db96594="">逐行扫描</span><br data-v-4db96594=""><span data-v-4db96594="">智能识别输出HDR</span><br data-v-4db96594=""><span data-v-4db96594="">智能识别全景视频</span><br data-v-4db96594=""></span></span></div>',
      config:{

      }
    };
  },
  beforeMount() {
    console.log(123, this.$route.query.type);;
    if(this.$route.query.type){
      this.activeName = this.$route.query.type;
    }
    if(this.$route.query)
    this.getData();
    this.getUserInfo();
    this.getOss();
    this.getUserPutConfig();
  },
  methods: {
    showGoods(id){
      this.$router.push('/periphery/'+id);
    },
    getUserPutConfig(){
        this.$axios
        .get(this.url + "/api/getUserPutConfig", {})
        .then((res) => {
          console.log(res);
          if (res.data.code == 200) {
            this.config = res.data.data;
          }
        })
        .catch((err) => {});
    },
    getOss() {
      this.$axios
        .get(this.url + "/api/oss", {})
        .then((res) => {
          console.log(res);
          if (res.data.code == 200) {
            this.domain = res.data.data.oss.DoMain;
            localStorage.setItem('oss', JSON.stringify(res.data.data.oss));
          }
        })
        .catch((err) => {});
    },

    getUserInfo() {
      this.$axios
        .get(this.url + "/api/user", {})
        .then((res) => {
          if (res.data.code != 200) {
            localStorage.removeItem("UserInfo");
            localStorage.removeItem("Authorization");
            this.$router.push({ name: "login" });
          } else {
            this.user = res.data.data;
          }
        })
        .catch((err) => {});
    },
    handleSizeChange(val) {
      this.pagesize = val;
    },
    handleCurrentChange(val) {
      this.page = val;
      this.getData();
    },
    getData(type = "learn") {
      this.items = [];
      let user = JSON.parse(localStorage.getItem("UserInfo"));
      if (user && Object.keys(user).length !== 0) {
        let data = {
          offset: this.page - 1,
          limit: this.pagesize,
          type: type,
        };

        this.$axios
          .get(this.url + "/api/user/courses", {
            params: data,
          })
          .then((res) => {
            if (res.data.code === 200) {
              this.items = res.data.data.lists;
              this.total = res.data.data.total;
            } else if (res.data.code === 4001) {
              localStorage.removeItem("UserInfo");
              localStorage.removeItem("Authorization");
              this.$router.push({ name: "login" });
            }
          })
          .catch((err) => {});
      }
    },

    //  跳转
    jump(id) {
      this.$router.push({ name: "coursepage", params: { id: id } });
    },

    // 取消收藏
    cancelCollect(userCourseId) {
      if (userCourseId) {
        this.$axios
          .post(this.url + "/api/courses/collect/cancel", {
            user_course_id: userCourseId,
          })
          .then((res) => {
            if (res.data.code === 200) {
              this.$message.success({
                showClose: true,
                message: res.data.msg,
              });

              let newCollectData = [];
              this.items.forEach((item, index) => {
                if (item.id !== userCourseId) {
                  newCollectData.push(item);
                }
              });
              this.$nextTick(() => {
                this.items = newCollectData;
              });
            } else if (res.data.code === 4001) {
              localStorage.removeItem("UserInfo");
              localStorage.removeItem("Authorization");
              this.$router.push({ name: "login" });
            } else {
              this.$message.warning({
                showClose: true,
                message: res.data.msg,
              });
            }
          })
          .catch((err) => {});
      } else {
        this.$message.warning({
          showClose: true,
          message: "取消好像出了点小问题，先缓一缓吧",
        });
        return false;
      }
    },

    handleClick() {
      if(this.activeName=='putlist'){
        this.$router.push('contribute')
      }
      if(this.activeName!="upload"){
        this.getData(this.activeName);
      }
      

    },
    handleClick2() {
      
      console.log(this.activeName2);
      // this.getData(this.activeName);
    },
  },
};
</script>

<style lang="scss" scoped>
.disable-upload {
  color: #999999;
}
.video-entrance * {
    box-sizing: border-box;
}
>>>.disable-upload-wrp{
    margin-top: 10px;
    position: relative;
    border: 2px dashed #ccc;
    padding: 60px 0;
    text-align: center;
    color: #999;
    font-size: 14px;
    text-align: center;
}
>>>.upload-body{
    padding: 8px 0 16px;
    height: calc(100% - 64px);
    overflow: auto;
    >div{
        max-width: 830px;
        margin: 0 auto;
        .upload-wrp{
            margin-top: 10px;
            position: relative;
            border: 2px dashed #ccc;
            .upload{
                margin-top: 62px;
                margin-bottom: 62px;
                display: flex;
                justify-content: center;
                position: relative;
                color: #999;
                font-size: 14px;
                text-align: center;
            }
        }
        .banner-entry {
            margin-top: 20px;
            height: 88px;
            background-color: rgba(0,161,214,.05);
            position: relative;
            display: flex;
            align-items: center;
            .banner-entry-icon {
                color: #00a1d6;
                width: 50px;
                height: 50px;
                margin: 0 15px 0 20px;
            }
            .banner-entry-text{
                flex: 1;
                text-align: left;
                .main-text {
                    font-size: 16px;
                    padding-bottom: 4px;
                    color: #212121;
                    font-weight: 700;
                }
                .sub-text {
                    padding-top: 4px;
                    font-size: 12px;
                    color: #505050;
                    position: relative;
                    cursor: pointer;
                    span{
                        vertical-align: middle;
                    }
                }
            }
            .banner-entry-right {
                width: 220px;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: flex-end;
                padding-right: 40px;
                z-index: 2;
                .enter-btn {
                    display: inline-block;
                    width: 90px;
                    height: 30px;
                    line-height: 30px;
                    border: 1px solid #00aeec;
                    background-color: #fff;
                    color: #00aeec;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 13px;
                    text-align: center;
                    margin-left: 5px;
                }
            }
            .banner-entry-icon-wrapper {
                height: 83px;
                width: 83px;
                position: absolute;
                bottom: 0;
                right: 0;
            }
        }
        
    }
}


>>>.footer-item{
  .list{
    a, .item{
      color: #00a1d6;
      font-size: 12px;
    }
  }
}  
.mygoods{
  .image{
    max-width: 200px;
    max-height: 200px;
  }
}  
.upload-box {
  >>> .el-tabs__nav-scroll {
    background-color: #fff;
  }
  >>> .upload-home {
  }
}

.user-course {
  padding-bottom: 50px;
  .user-head {
    .icon {
      background-position: -35px -49px;
    }
  }
  .main {
    padding: 25px;
  }
  /deep/ .user-tabs .el-tabs__header {
    margin-bottom: 27px;
  }
  .user-tabs {
  }
  .list {
  }
  .list-item {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    &:last-child {
      margin-bottom: 0;
    }
    .image {
      width: 245px;
      border-radius: 5px;
      overflow: hidden;
      flex-shrink: 0;
      margin-right: 20px;
    }
    .info {
      min-width: 0;
      flex-grow: 1;
    }
    .title {
      font-size: 16px;
      color: #000;
      line-height: 1.6;
      height: 3.2em;
      margin-bottom: 15px;
    }
    .progress-rate {
    }
    .text {
      justify-content: space-between;
      width: 100%;
      margin-bottom: 10px;
      font-size: 14px;
      line-height: 1.2;
      .text1 {
        color: #687d93;
      }
      .text2 {
        color: #67c23a;
      }
    }
  }
  .xg-progress {
    margin-bottom: 15px;
  }
  .btn-group {
    display: flex;
    align-items: center;
  }
  .el-button {
    display: block;
    width: 100px;
    height: 36px;
    border-radius: 6px;
    padding: 0;
    line-height: 36px;
    &.el-button--primary:hover {
      background-color: #67c23a !important;
      border-color: #67c23a !important;
    }
    &.el-button--default {
      background-color: #fafafa;
      border-color: #dbdddf;
      color: #a3acb6;
    }
  }
  .xg-pagination {
    margin-top: 64px;
  }
  // 我的收藏
  .tab-pane2 {
    .list-item {
      align-items: stretch;
      padding-bottom: 25px;
      margin-bottom: 25px;
      border-bottom: 1px dashed #d3d3d3;
      &:last-child {
        margin-bottom: 0;
      }
    }
    .info {
      display: flex;
      flex-flow: column;
      align-items: flex-start;
      justify-content: space-between;
    }
    .title {
    }
    .el-pagination {
      margin-top: 40px;
    }
  }
}

@media screen and (max-width: 751px) {
  .user-course {
    padding: 15px 20px;
    .main {
      padding: 0;
    }
    .list-item {
      flex-wrap: wrap;
      .image {
        width: 100%;
        margin-right: 0;
        margin-bottom: 15px;
      }
      .info {
        width: 100%;
      }
      .title {
        font-size: 14px;
        margin-bottom: 10px;
      }
      .text {
        font-size: 12px;
      }
    }
    .xg-progress {
      margin-bottom: 10px;
    }
    .el-button {
      width: 48%;
      margin-left: 0;
      &.el-button--primary {
        margin-right: 4%;
      }
    }
    .xg-pagination {
      margin-top: 30px;
    }
  }
}
</style>

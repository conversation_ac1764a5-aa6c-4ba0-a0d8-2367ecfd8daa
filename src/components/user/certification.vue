<template>
  <div>
    <el-alert
      title="警告提示的文案"
      type="warning"
      :closable="false">
    </el-alert>

    <el-form :model="ruleForm" label-width="100px" class="demo-ruleForm">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-form-item label="真实姓名" prop="name" required>
          <el-input v-model="ruleForm.name"></el-input>
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-form-item label="身份证号" prop="name" required>
          <el-input v-model="ruleForm.date1"></el-input>
        </el-form-item>
      </el-col>

      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-upload
          class="avatar-uploader"
          action="https://jsonplaceholder.typicode.com/posts/"
          list-type="picture-card"
          :on-preview="handlePictureCardPreview"
          :on-remove="handleRemove"
          :http-request="httpRequest"
          :class="{'demo-httpRequestImg':httpRequestImg}"
        >
          <i class="el-icon-plus"></i>
          <div slot="tip" class="uploadTip">身份证正面</div>
        </el-upload>
        <el-dialog :visible.sync="dialogVisibleImg" append-to-body class="ImgClass">
          <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
      </el-col>

      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-upload
          class="avatar-uploader"
          action="https://jsonplaceholder.typicode.com/posts/"
          list-type="picture-card"
          :on-preview="handlePictureCardPreview1"
          :on-remove="handleRemove1"
          :http-request="httpRequest1"
          :class="{'demo-httpRequestImg':httpRequestImg1}"
        >
          <i class="el-icon-plus"></i>
          <div slot="tip" class="uploadTip">身份证反面</div>
        </el-upload>
        <el-dialog :visible.sync="dialogVisibleImg1" append-to-body class="ImgClass">
          <img width="100%" :src="dialogImageUrl1" alt="">
        </el-dialog>
      </el-col>


      <el-col :span="24" style="margin-top: 20px;">
          <el-button type="primary" @click="submitForm('ruleForm')">完成</el-button>
      </el-col>
    </el-form>
    <div class="tips" style="text-align: left; color: #999">
      <span>需知</span>
      <p>1.sdfdgdga</p>
    </div>
  </div>
</template>

<script>
export default {
  name:'certification',
  components:{
  },
  data() {
    return {
      ruleForm: {
        name: '',
        date1: '',
      },
      dialogImageUrl: '',//预览url
      dialogVisibleImg:false,
      httpRequestImg:false,//展示单个图片

      dialogImageUrl1: '',//预览url
      dialogVisibleImg1:false,
      httpRequestImg1:false,//展示单个图片
    };
  },
  created() {

  },
  methods: {
    httpRequest(a) { //上传成功
      this.httpRequestImg = true;
    },
    handlePictureCardPreview(file) { //预览
      this.dialogImageUrl = file.url;
      this.dialogVisibleImg = true;
    },
    handleRemove(file, fileList) { //删除
      this.httpRequestImg = false;
      // console.log(file, fileList);
    },

    httpRequest1(a) { //上传成功
      this.httpRequestImg1 = true;
    },
    handlePictureCardPreview1(file) { //预览
      this.dialogImageUrl1 = file.url;
      this.dialogVisibleImg1 = true;
    },
    handleRemove1(file, fileList) { //删除
      this.httpRequestImg1 = false;
      // console.log(file, fileList);
    },
  }
}
</script>

<style scoped lang="scss">
.demo-ruleForm{
  overflow: hidden;
  margin-top: 20px;
  /deep/ .el-upload--picture-card{
    width: 90%;
    height: 160px;
  }
  /deep/ .el-upload{
    width: 90%;
    height: 160px;
  }
  /deep/ .el-upload-list--picture-card .el-upload-list__item{
    width: 90%;
    height: 160px;
  }
  /deep/ .el-upload-list--picture-card .el-upload-list__item-thumbnail{
    width: 100%;
    height: 160px;
  }
  /deep/ .avatar{
    width: 100%;
    height: 160px;
  }
}

.demo-httpRequestImg{
  /deep/ .el-upload--picture-card{
    display: none;
  }
}

.tips{
  span{
    font-size: 13px;
  }
  p{
    font-size: 12px;
  }
}
</style>

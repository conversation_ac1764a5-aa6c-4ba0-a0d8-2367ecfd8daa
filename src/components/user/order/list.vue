<template>
  <div>
    <div class="search">
      <el-input placeholder="请输入搜索内容" v-model="keywords" class="input-with-select">
        <el-button slot="append" icon="el-icon-search" @click="search"></el-button>
      </el-input>
    </div>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="product_name" label="商品名称" width="300">
      </el-table-column>
      <el-table-column prop="order_no" label="订单号">
      </el-table-column>
      <el-table-column prop="order_amount" label="订单价格(光子)">
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间">
      </el-table-column>
      <el-table-column prop="status" label="订单状态">
        <template slot-scope="scope">
             <span v-if="scope.row.status== 0">待支付</span>
             <span v-if="scope.row.status== 1">已支付</span>
             <span v-if="scope.row.status== 2">已关闭</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            style="background-color: #e6a23c;color: white !important;width: 70px"
            v-if="scope.row.status == 0"
            @click="goPay(scope.row)">去支付</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[10, 15, 20]"
      :page-size="pagesize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total">
    </el-pagination>
  </div>
</template>
<script>
export default {
  data() {
    return {
      tableData: [],
      // 默认显示第一条
      currentPage: 1,
      total: 0,
      pagesize: 10,
      keywords: '',
    }
  },
  mounted() {
    this.getOrderList()
  },
  methods: {
    handleSizeChange(val) {
      this.pagesize = val;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getOrderList()
    },
    getOrderList(keywords = '') {
      let data = {
        page: this.currentPage,
        limit: this.pagesize
      };
      if (keywords) {
        data.keywords = keywords;
      }

      this.$axios.get(this.url + '/api/user/order-list', {
        params: data
      }).then(res => {
        if (res.data.code === 200) {
          this.tableData = res.data.data.data;
          this.total = res.data.data.total;
        } else if (res.data.code === 4001) {
          localStorage.removeItem('UserInfo');
          localStorage.removeItem('Authorization');
          this.$router.push({name:"login"})
        }
      }).catch(err => {
      })
    },
    // 搜索
    search(){
      if (this.keywords) {
        this.getOrderList(this.keywords);
      }
    },
    // 去支付
    goPay(orderInfo){
      this.$router.push({path:"/payment", query:{product_id:orderInfo.id, product_type:orderInfo.product_type}})
    }
  }
}

</script>
<style lang="scss">
.search {
  width: 40%;
}

</style>

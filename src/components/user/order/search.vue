<template>
  <div>
    <div class="search">
      <el-input placeholder="请输入搜索内容" v-model="input3" class="input-with-select">
        <el-button slot="append" icon="el-icon-search"></el-button>
      </el-input>
    </div>
    <el-table
      :data="tableData"
      style="width: 100%">
      <el-table-column
        prop="date"
        label="商品名称"
        width="300">
      </el-table-column>
      <el-table-column
        prop="name"
        label="订单号"
      >
      </el-table-column>
      <el-table-column
        prop="Price"
        label="订单价格"
      >
      </el-table-column>
      <el-table-column
        prop="time"
        label="创建时间"
      >
      </el-table-column>
      <el-table-column
        prop="status"
        label="订单状态"
      >
      </el-table-column>
      <el-table-column
        label="详情"
        width="50">
        <template slot-scope="scope">
          <el-button type="text" size="small">详情</el-button>
        </template>
      </el-table-column>
    </el-table>


    <el-pagination
      background
      layout="prev, pager, next"
      :total="tableData.length">
    </el-pagination>
  </div>
</template>
<script>
export default {
  data() {
    return {
      tableData: [],
      input3: '',
    }
  },
  mounted() {
  },
  methods:{
  }
}
</script>
<style scoped lang="scss">
.search{
  width: 40%;
}
 /deep/.el-pager li{
  background: rgba(0,0,0,0.1);
}
</style>

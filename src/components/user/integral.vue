<template>
  <div class="user-integral">
    <!-- 光子账户 -->
    <div class="user-head">
      <!-- 标题 -->
      <div class="title">
        <div class="icon"></div>
        <div class="text">光子账户</div>
      </div>
    </div>


    <div class="main">
      <div class="user-guangzi">
        <div class="balance-info">
          <div class="balance-amount">
            <span style="font-size: 16px">账户余额</span>
<!--            <span class="currency-symbol">¥</span>-->
            <span class="amount">{{ balance }}</span>
            <span style="font-size: 16px">光子</span>
          </div>
          <div class="balance-buttons">
            <button class="btn-deposit" @click="recharge()">充值</button>
          </div>
        </div>
        <div class="transaction-info">
          <div class="info-block">
            <div class="info-title">收入 </div>
            <div class="info-amount">{{ income }}<span style="font-size: 14px;color: red"> 光子</span></div>
          </div>
          <div class="info-block">
            <div class="info-title">支出</div>
            <div class="info-amount">{{ expend }}<span style="font-size: 14px;color: red"> 光子</span></div>
          </div>
        </div>
      </div>
      <!-- 数据 -->
      <el-table :data="tableData">
        <el-table-column label="标题" width="360" align="left">
          <template slot-scope="scope">
            <!-- 默认是灰色 -->
            <!-- text2 样式是“蓝色”，表示是消费 -->
            <div class="text">
              {{ scope.row.title }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="类型" align="left" width="130">
          <template slot-scope="scope">
            <!-- 默认是灰色 -->
            <!-- text2 样式是“蓝色”，表示是消费 -->
            <div class="text" :class="{ text2: scope.row.action == 2 }">
              {{ scope.row.action == 1 ? '收入' : '支出' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="交易金额" width="130">
          <template slot-scope="scope">
            <!-- text1 样式是“绿色”，表示是收入 -->
            <!-- text2 样式是“蓝色”，表示是支出 -->
            <!--            <div class="text" :class="[scope.row.action == 1 ? 'text1' : 'text2']">-->
            <div class="text">
              {{ scope.row.amount }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="光子余额" width="130" align="center">
          <template slot-scope="scope">
            <!-- text3 样式是“红色” -->
            <div class="text text3">
              {{ scope.row.extend ? scope.row.extend['balance'] : '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
            prop="created_at"
            label="创建时间"
            min-width="140"
            width="190"
        ></el-table-column>
      </el-table>
      <!-- 分页 -->
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          class="xg-pagination"
          layout="prev, pager, next,jumper"
          :current-page="offset"
          :page-size="pagesize"
          :total="total"
      >
      </el-pagination>
    </div>


    <!-- 充值弹窗 -->
    <div class="dialog-open manual-open" @click="closeDialog" :class="{ active: rechargeDialog }">
      <div class="dialog-box" @click.stop>
        <div class="close el-icon-close" @click="closeDialog"></div>
        <div class="open-title">{{ dialogText }}</div>
        <div class="body">
          <el-form ref="form" :model="rechargeForm" :rules="rechargeRules" class="editForm" label-width="80px">
            <el-form-item label="充值数量" prop="name">
              <el-input-number
                  :min="1"
                  v-model="rechargeForm.num"
                  precision="2"
                  placeholder="10人民币可充值1光子" style="width: 70%"></el-input-number>
              <span style="color: #0c63e4">&nbsp;光子</span>
              <div>
                <span style="color: red;font-size: 12px">10人民币可充值1光子</span>
              </div>
            </el-form-item>
            <el-form-item label="支付方式" prop="name">
              <div class="left">
                <div class="pay">
                  <div class="wechat" :class="{active:rechargeForm.pay_method===1}"><img
                      src="../../assets/img/wechat5.jpg" alt="" @click="rechargeForm.pay_method = 1"></div>
                  <div class="aliplay" :class="{active:rechargeForm.pay_method===2}"><img
                      src="../../assets/img/ailpay.jpg" alt="" @click="rechargeForm.pay_method = 2"></div>
                </div>
              </div>
              <!--              <el-radio v-model="rechargeForm.pay_method"-->
              <!--                        :label="option.value"-->
              <!--                        v-for="option in rechargeOptions"-->
              <!--                        :key="option.value">{{ option.label }}-->
              <!--              </el-radio>-->
            </el-form-item>
            <el-form-item class="btn-group">
              <el-button @click="closeDialog">取消</el-button>
              <el-button type="primary" @click="rechargeSubmit()">充值</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 支付弹窗 -->
    <div class="dialog-open manual-open qr-dialog" @click="payDialog = false" :class="{ active: payDialog }">
      <div class="dialog-box" style="height: 480px;" @click.stop>
        <div class="close el-icon-close" @click="payDialog = false"></div>
        <div class="open-title">微信支付</div>
        <div class="body" align="center">
          <div class="qr">
            <img :src="this.payQrcode" alt="">
          </div>
          <div class="text">
            支付金额：<span style="font-size: 24px;color: red">￥<strong>{{ currentPrice }}</strong></span>
          </div>
        </div>
      </div>
    </div>

    <div v-html="payInfo" ref="pay"></div>
  </div>
</template>

<script>
import {retain} from "../../utils/common";

export default {
  components: {},
  data() {
    return {
      tableData: [],
      // 默认显示第一条
      offset: 1,
      total: 10,
      pagesize: 15,

      // 用户资产
      balance: '0.00',
      income: '0.00',
      expend: '0.00',

      // 充值
      rechargeForm: {
        num: 1,
        pay_method: 1
      },
      rechargeOptions: [
        {label: '微信', value: 1},
        {label: '支付宝', value: 2},
      ],
      rechargeRules: {
        num: [{required: true, message: '请填写光子充值数量', trigger: 'blur'}],
      },
      rechargeDialog: false,
      dialogText: '',

      // 支付
      payDialog: false,
      payQrcode: "",
      payInfo: "",
      currentPrice: "0.00",

      timer: 0,
    };
  },
  beforeMount() {
    this.getData()
  },
  mounted() {
    // 支付监听
    // this.timer = setInterval(() => {
    //   this.getRechargeOrder();
    // }, 3000);
  },
  beforeDestroy() {
    //页面关闭前关闭定时器
    clearInterval(this.timer);
  },
  methods: {
    handleSizeChange(val) {
      this.pagesize = val;
    },
    handleCurrentChange(val) {
      this.offset = val;
      this.getData()
    },
    getData() {
      let data = {
        offset: this.offset - 1,
        limit: this.pagesize
      };

      this.$axios.get(this.url + "/api/user/wallet_logs", {params: data})
          .then((res) => {
            if (res.data.code === 200) {
              this.tableData = res.data.data.lists;
              this.total = res.data.data.total;
              this.income = res.data.data.income;
              this.balance = res.data.data.balance;
              this.expend = res.data.data.expend;
            } else if (res.data.code === 4001) {
              localStorage.removeItem('UserInfo');
              localStorage.removeItem('Authorization');
              this.$router.push({name: "login"})
            }
          })
          .catch((err) => {
          });
    },

    closeDialog() {
      this.rechargeDialog = false;
    },

    getRechargeOrder(orderNo) {
      // if (this.orderNo == '') {
      //   return;
      // }
      console.log(orderNo)
      this.$axios.post(this.url + "/api/integral/order", {
        order_no: orderNo,
      }).then((res) => {
        if (res.data.code === 200) {
          let orderInfo = res.data.data;
          if (orderInfo.status === 1) {
            clearInterval(this.timer);
            this.payDialog = false;
            this.$router.push({name: "payment-result", query: {order_no: res.data.data.order_no}});
          }
        } else if (res.data.code === 4001) {
          localStorage.removeItem("UserInfo");
          localStorage.removeItem("Authorization");
          this.$router.push({name: "login"})
          clearInterval(this.timer);
        } else {
          clearInterval(this.timer);
        }
      }).catch((err) => {
        clearInterval(this.timer);
      });
    },

    // 光子充值
    recharge() {
      this.rechargeDialog = true;
      this.dialogText = '光子充值';
    },
    rechargeSubmit() {
      this.currentPrice = this.rechargeForm.num * 10;
      this.$axios.post(this.url + "/api/integral/recharge", this.rechargeForm).then((res) => {
        if (res.data.code === 200) {
          if (res.data.msg == "支付码生成成功") {
            this.payQrcode = res.data.data.payCodeImg;
            this.orderNo = res.data.data.orderNo;
            this.payDialog = true;

            // 支付监听
            this.timer = setInterval(() => {
              this.getRechargeOrder(res.data.data.orderNo);
            }, 3000);
          } else {
            this.payInfo = res.data.data;
            this.$nextTick(() => {
              this.$refs.pay.children[0].submit();
            });
          }
        } else if (res.data.code === 4001) {
          localStorage.removeItem("UserInfo");
          localStorage.removeItem("Authorization");
          this.$router.push({name: "login"})
        } else {
          this.$message.warning({
            showClose: true,
            message: res.data.msg,
          });
        }
      }).catch((err) => {
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.user-integral {
  .user-head {
    .icon {
      background-position: -35px -104px;
    }
  }

  .main {
    padding: 23px 25px;

    .user-guangzi {
      display: flex;
      width: 100%;
    }

    .balance-info {
      background-color: #e36668;
      flex: 1;
      padding: 20px;
      display: flex;
      border-radius: 10px 0 0 10px;
      justify-content: space-between;
      align-items: center;
    }

    .balance-amount {
      font-size: 30px;
      color: white;
    }

    .currency-symbol {
      font-size: 30px;
    }

    .amount {
      margin-left: 5px;
    }

    .balance-buttons button {
      background-color: #fff;
      border: none;
      color: #FF4D4F;
      padding: 10px 20px;
      margin: 0 5px;
      cursor: pointer;
      border-radius: 4px;
    }

    .transaction-info {
      background-color: #f6f6f6; /* Adjust the color as needed */
      flex: 1;
      display: flex;
      border-radius: 0 10px 10px 0;
      justify-content: space-around;
      align-items: center;
      padding: 20px;
    }

    .info-block {
      text-align: center;
    }

    .info-title {
      font-size: 16px;
      color: #999;
    }

    .info-amount {
      font-size: 24px;
      color: #333;
    }
  }

  .el-table /deep/ {
    th {
      color: #687d93;
      font-size: 14px;
      padding-top: 15px;
      padding-bottom: 15px;
    }

    tr > td {
      padding-top: 20px;
      padding-bottom: 20px;
    }

    tr:hover > td {
      background-color: #f9f9f9;
    }

    .cell {
      color: #909399;
      font-size: 14px;
      line-height: 1.6;
    }
  }

  .text {
    color: #909399;

    &.text1 {
      color: #00be06;
    }

    &.text2 {
      color: #0d6efd;
    }

    &.text3 {
      color: #ff4f4f;
      font-weight: bold;
    }
  }
}

// 弹窗样式
.dialog-open {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  transition: all 0.4s cubic-bezier(0.18, 0.89, 0.32, 1.28);
  transform: scale(0.8);
  visibility: hidden;
  opacity: 0;

  &.active {
    transform: scale(1);
    opacity: 1;
    visibility: visible;
  }

  .dialog-box {
    position: relative;
    text-align: left;
    width: 100%;
    max-width: 556px;
    height: 340px;
    background: #fff;
    box-shadow: 0px 0px 30px 0px rgba(4, 0, 0, 0.3);
    border-radius: 6px;
    padding: 30px 25px;
  }

  .close {
    font-size: 30px;
    color: #96a1ac;
    position: absolute;
    top: 15px;
    right: 15px;
    cursor: pointer;
  }

  .open-title {
    color: #0084ff;
    line-height: 1.2;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 30px;
  }

  .editForm /deep/ {
    .el-input__inner {
      border: 1px solid #a3acb6;
      border-radius: 0;
      height: 32px;
      line-height: 32px;
    }

    .el-form-item--mini.el-form-item,
    .el-form-item--small.el-form-item {
      margin-bottom: 15px;
    }

    .el-form-item__label {
      color: #909399;
    }

    .el-checkbox__input.is-checked .el-checkbox__inner,
    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background-color: #0d6efd;
      border-color: #0d6efd;
    }

    .el-checkbox__inner {
      border-color: #a3acb6;
    }

    .el-checkbox__label {
      color: #333;
    }

    .el-checkbox__input.is-checked + .el-checkbox__label {
      color: #0d6efd;
    }

    .left {
      .pay {
        display: flex;
        margin-bottom: 35px;
      }

      .wechat,
      .aliplay {
        width: 60px;
        border: 4px;
        border: 2px solid transparent;
        flex-shrink: 0;
        border-radius: 4px;
        overflow: hidden;
        cursor: pointer;

        &.active,
        &:hover {
          border-color: #0d6efd;
        }
      }

      .wechat {
        margin-right: 20px;
      }
    }

    .address .el-textarea__inner {
      height: 100px !important;
      line-height: 1.8;
      border-color: #a3acb6;
      border-radius: 0;
      resize: none;

      // 隐藏滚动条
      &::-webkit-scrollbar {
        display: none;
      }

      // 兼容 Firefox
      scrollbar-width: none;
      // 兼容 IE 10 +
      -ms-overflow-style: none;
    }

    .btn-group {
      width: 100%;

      .el-form-item__content {
        text-align: right;
        width: 100%;
      }

      .el-button {
        width: 80px;
        height: 32px;
        line-height: 32px;
        border-radius: 0;
        padding: 0;

        &:focus,
        &:hover {
          background-color: inherit;
          border-color: inherit;
        }

        &.el-button--default {
          border-color: #bdbdbd;
          color: #bdbdbd;
        }
      }
    }
  }
}

@media screen and (max-width: 751px) {
  .user-guangzi .balance-info {
    flex-direction: column;
    align-items: flex-start;
  }

  .user-guangzi .balance-info .balance-amount,
  .user-guangzi .balance-info .balance-buttons {
    width: 100%;
    text-align: center;
  }

  .user-guangzi .balance-info .balance-buttons button {
    width: 100%;
    margin-bottom: 10px;
  }

  .user-guangzi .transaction-info {
    flex-direction: column;
    align-items: center;
  }

  .user-guangzi .info-block {
    width: 100%;
    margin-bottom: 10px;
  }

  .user-integral {
    padding: 15px 20px;

    .user-head {
      border-bottom: 1px dashed #ddd;
      padding-bottom: 15px;
      margin-bottom: 10px;
    }

    .main {
      padding: 0;
    }

    .el-table /deep/ {
      th {
        font-size: 12px;
        padding-top: 10px;
        padding-bottom: 10px;
      }

      tr > td {
        padding-top: 8px;
        padding-bottom: 8px;
      }

      .cell {
        font-size: 12px;
      }
    }

    .dialog-open {
      width: 90%;
      left: 5%;
      margin-left: auto;
    }

    .dialog-open .open-title {
      font-size: 16px;
    }

    .dialog-open .editForm /deep/ {
      .btn-group .el-form-item__content {
        display: flex;
        align-items: center;

        &::before,
        &::after {
          display: none;
        }
      }

      .btn-group .el-button {
        flex-grow: 1;
      }
    }

    //
    .item {
      width: 100%;
      margin-bottom: 20px;
    }

    .add-btn {
      margin-bottom: 0;
    }
  }
}
</style>

<template>
  <div class="securitysetting">
    <div class="content">
      <h4>安全设置</h4>
      <el-row class="list">
        <el-col :span="24" class="list-content">
          <div class="left">
            <i>
              <svg t="1633602516259" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="20160" data-spm-anchor-id="a313x.7781069.0.i31" width="50" height="50">
                <path d="M309.4 244.9c22.6 0 41 18.3 41 41v170.8c0 22.6-18.3 41-41 41-22.6 0-41-18.3-41-41V285.9c0.1-22.6 18.4-41 41-41zM713.5 240.9c22.6 0 41 18.3 41 41v23.4c0 22.6-18.3 41-41 41-22.6 0-41-18.3-41-41v-23.4c0.1-22.6 18.4-41 41-41z" fill="#cdcdcd" p-id="20161" data-spm-anchor-id="a313x.7781069.0.i30" class="selected"></path><path d="M511.5 120.9c89 0 161.1 72.1 161.1 161.1h81.9c0-134.2-108.8-243-243-243s-243 108.8-243 243h81.9c0-89 72.1-161.1 161.1-161.1zM877.4 932.6V476.8c-3.4-33.4-31-59.7-64.9-61H212.7c-37.5 0-68 30.4-68.1 67.9V928c4 30.2 27.8 54.2 57.9 58.5H824c27-5.5 48.2-26.8 53.4-53.9z" fill="#cdcdcd" p-id="20162" data-spm-anchor-id="a313x.7781069.0.i28" class="selected"></path><path d="M436.9 701.1c0-22.6 18.3-41 41-41h66.2c22.6 0 41 18.3 41 41 0 22.6-18.3 41-41 41h-66.2c-22.6-0.1-41-18.4-41-41z" fill="#FFFFFF" p-id="20163"></path>
              </svg>
            </i>
            <b>登录密码</b>
            <span>经常更改密码有助于保护您的帐号安全</span>
          </div>
          <div class="right">
            <span class="security" v-if="this.user.is_pass">已设置</span>
            <span class="unsafe" v-else>未设置</span>
            <el-button type="text" @click="dialogpassword = true">修改</el-button>
          </div>
        </el-col>
        <el-col :span="24" class="list-content">
          <div class="left">
            <i>
              <svg t="1633602465236" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18926" width="50" height="50">
                <path d="M926 215.173333l-384-170.666666a21.333333 21.333333 0 0 0-17.333333 0l-384 170.666666A21.333333 21.333333 0 0 0 128 234.666667v217.813333c0 219.866667 129.373333 419.02 329.6 507.333333 32.793333 14.466667 57.566667 21.5 75.733333 21.5s42.94-7.033333 75.733334-21.5c200.226667-88.333333 329.6-287.486667 329.6-507.333333V234.666667a21.333333 21.333333 0 0 0-12.666667-19.493334z m-206.913333 205.246667l-213.333334 213.333333a21.333333 21.333333 0 0 1-30.173333 0l-128-128a21.333333 21.333333 0 0 1 30.173333-30.173333L490.666667 588.5l198.246666-198.253333a21.333333 21.333333 0 1 1 30.173334 30.173333z" fill="#cdcdcd" p-id="18927"></path>
              </svg>
            </i>
            <b>安全问题</b>
            <span>设置安全问题，保护帐号密码安全，也可用于找回密码</span>
          </div>
          <div class="right">
            <span class="security" v-if="this.user.is_problem">已设置</span>
            <span class="unsafe" v-else>未设置</span>
            <el-button type="text" @click="dialogproblem = true" v-if="!this.user.is_problem">设置</el-button>
            <el-button type="text" style="background-color:rgb(205, 205, 205)" v-else>设置</el-button>
          </div>
        </el-col>
        <el-col :span="24" class="list-content">
          <div class="left">
            <i>
              <svg t="1633601364703" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="17790" width="50" height="50">
                <path d="M149.6 171.8h691.9c47.2 0 85.9 37.7 86.5 83.9L495.7 493 63.5 256c0.4-46.4 38.8-84.2 86.1-84.2z m-86.1 175l-0.4 419.6c0 46.7 38.9 84.9 86.5 84.9h691.9c47.6 0 86.5-38.2 86.5-84.9V346.6L505.9 572.8c-6.5 3.5-14.3 3.5-20.7 0l-421.7-226z" p-id="17791" fill="#cdcdcd"></path>
              </svg>
            </i>
            <b>手机绑定</b>
            <span>已绑定手机：{{ this.user.phone }}</span>
          </div>
          <div class="right">
            <span class="security" v-if="this.user.phone">已绑定</span>
            <span class="unsafe" v-else>未绑定</span>
            <el-button type="text" @click="dialogphone = true">修改</el-button>
          </div>
        </el-col>
        <el-col :span="24" class="list-content">
          <div class="left">
            <i>
              <svg t="1633601364703" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="17790" width="50" height="50">
                <path d="M149.6 171.8h691.9c47.2 0 85.9 37.7 86.5 83.9L495.7 493 63.5 256c0.4-46.4 38.8-84.2 86.1-84.2z m-86.1 175l-0.4 419.6c0 46.7 38.9 84.9 86.5 84.9h691.9c47.6 0 86.5-38.2 86.5-84.9V346.6L505.9 572.8c-6.5 3.5-14.3 3.5-20.7 0l-421.7-226z" p-id="17791" fill="#cdcdcd"></path>
              </svg>
            </i>
            <b>邮箱设置</b>
            <span>可用于找回登录密码</span>
          </div>
          <div class="right">
            <span class="security" v-if="this.user.email">已设置</span>
            <span class="unsafe" v-else>未设置</span>
            <el-button type="text" @click="dialogFormVisible = true">绑定</el-button>
          </div>
        </el-col>
      </el-row>
    </div>

    <!--登录密码-弹窗-->
    <el-dialog title="密码修改" :visible.sync="dialogpassword">
      <el-form ref="ruleForm" :model="ruleForm" status-icon :rules="rules" label-width="100px" class="demo-ruleForm">
        <el-form-item prop="oldpassword">
          <label>当前密码</label>
          <el-input type="password" v-model="ruleForm.oldpassword" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item prop="pass">
          <label>新密码</label>
          <el-input type="password" v-model="ruleForm.pass" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item prop="checkPass">
          <label>确认密码</label>
          <el-input type="password" v-model="ruleForm.checkPass" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogpassword = false">取 消</el-button>
        <el-button type="primary" @click="passwordChange">确 定</el-button>
      </div>
    </el-dialog>

    <!--设置安全问题-弹窗-->
    <el-dialog :model="securityFrom" title="安全问题" :visible.sync="dialogproblem">
      <div class="questions">
        <h6>安全问题一经设置，不可再次修改。</h6>
        <div class="questions-buttom">
          <div class="one">
            <label>安全问题1</label>
            <p>你的父母名字</p>
            <label>答案</label>
            <input type="text" placeholder="安全问题1答案" v-model="securityFrom.answer1"/>
          </div>
          <div class="one">
            <label>安全问题2</label>
            <p>你的老师名字</p>
            <label>答案</label>
            <input type="text" placeholder="安全问题2答案" v-model="securityFrom.answer2"/>
          </div>
          <div class="one">
            <label>安全问题3</label>
            <p>你的爱人的名字</p>
            <label>答案</label>
            <input type="text" placeholder="安全问题3答案" v-model="securityFrom.answer3"/>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogproblem = false">取 消</el-button>
        <el-button type="primary" @click="security">确 定</el-button>
      </div>
    </el-dialog>

    <!--手机绑定-弹窗-->
    <el-dialog title="手机绑定" :visible.sync="dialogphone">
      <el-form ref="bindmobileFrom" :model="bindmobileFrom" status-icon :rules="rules" label-width="0" class="demo-ruleForm">
        <el-form-item prop="password">
          <label>您的登录密码</label>
          <el-input type="password" v-model="bindmobileFrom.password" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item prop="tel">
          <el-input v-model="bindmobileFrom.tel" auto-complete="off" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item prop="smscode" class="code" >
          <el-input v-model="bindmobileFrom.smscode" placeholder="验证码" style="width: 60%"></el-input>
          <el-button type="primary" :disabled='isDisabled' @click="sendCode" style="float: right;">{{buttonText}}</el-button>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogphone = false">取 消</el-button>
        <el-button type="primary" @click="bindmobile">确 定</el-button>
      </div>
    </el-dialog>

    <!--邮箱设置-弹窗-->
    <el-dialog title="邮箱设置" :visible.sync="dialogFormVisible">
      <el-form ref="emailForm" :model="emailForm" :rules="rules" label-width="120px">
        <el-form-item prop="email">
          <label>新邮箱</label>
          <el-input v-model="emailForm.email"></el-input>
        </el-form-item>
        <el-form-item prop="password">
          <label>登录密码</label>
          <el-input type="password" v-model="emailForm.password" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="bindemail">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {isEmail, isPhone} from '../../utils/validate'
import {checkMobile} from '../../utils/common'

    export default {
      name: "security",
      data() {
        var validatePass = (rule, value, callback) => {
          if (this.ruleForm.checkPass !== '') {
            this.$refs.ruleForm.validateField('checkPass');
          }
          callback();
        };

        var validateCheckPass = (rule, value, callback) => {
          if (value !== this.ruleForm.pass) {
            callback(new Error('两次输入密码不一致!'));
          }
          callback();
        };

        return {
          // 表单验证
          rules: {
            oldpassword:[{required: true, message: '请填写密码', trigger: 'change'}],
            password:[{required: true, message: '请填写密码', trigger: 'change'}],
            pass: [{ required: true, message: '请填写新密码', trigger: 'change' }, { validator: validatePass, trigger: 'blur' }],
            checkPass: [{required:true, message:'请填写确认新密码', trigger:'change'}, { validator: validateCheckPass, trigger: 'blur' }],
            tel: [{ required: true, message: '请填写手机号', trigger: 'change' }, { validator: isPhone, trigger: 'change' }],// 绑定手机号验证
            smscode: [{ required: true, message: '请填写验证码', trigger: 'change' }],
            email: [{ required: true, message: '请填写邮箱', trigger: 'change' }, { validator: isEmail, trigger: 'blur' }],// 邮箱验证
          },

          // 密码修改
          ruleForm: {
            pass: '',
            checkPass: '',
            oldpassword: ''
          },
          // 安全问题
          securityFrom: {
            answer1:"",
            answer2:"",
            answer3:"",
          },
          //邮箱
          emailForm: {
            email: "",
            password:"",
          },
          // 手机号绑定
          bindmobileFrom: {
            password:"",
            tel: "",
            smscode: "",
          },

          dialogproblem:false, //安全问题
          dialogFormVisible: false, //邮箱绑定
          dialogphone:false,//手机号绑定
          dialogpassword:false, //密码修改
          isDisabled: false, // 是否禁止点击发送验证码按钮
          buttonText: '发送验证码',
          flag: true,

          user: {},
      }
      },
      mounted() {
        this.user = JSON.parse(localStorage.getItem('UserInfo'));
      },
      methods: {
        // <!--发送验证码-->
        sendCode () {
          let tel = this.bindmobileFrom.tel
          if (checkMobile(tel)) {
            let time = 60
            this.buttonText = '已发送'
            this.isDisabled = true
            if (this.flag) {
              this.flag = false;
              let timer = setInterval(() => {
                time--;
                this.buttonText = time + ' 秒'
                if (time === 0) {
                  clearInterval(timer);
                  this.buttonText = '重新获取'
                  this.isDisabled = false
                  this.flag = true;
                }
              }, 1000);

              this.$axios.post(this.url + '/api/verification-codes',{
                phone: tel,
                scene: 'set-phone'
              }).then(res => {
              }).catch(err => {
              });
            }
          }
        },

        // 密码修改
        passwordChange(){
          let user = this.user
          this.$refs.ruleForm.validate(valid => {
            if (valid) {
              this.$axios.post(this.url + '/api/user/set-password',{
                old_password: this.ruleForm.oldpassword,
                password: this.ruleForm.pass
              }).then(res => {
                if (res.data.code !== 200) {
                  this.$message.warning({
                    showClose: true,
                    message: res.data.msg
                  });
                } else {
                  user.is_pass = true;
                  localStorage.setItem('UserInfo', JSON.stringify(user));
                  this.dialogpassword = false
                }
              }).catch(err => {
              });
            } else {
              // 登录表单校验失败
              return false;
            }
          });
        },

        // 安全问题
        security(){
          let answer1 = this.securityFrom.answer1
          let answer2 = this.securityFrom.answer2
          let answer3 = this.securityFrom.answer3
          let user = this.user

          if (answer1 == '' || answer2 == '' || answer3 == '') {
            this.$message.warning({
              showClose: true,
              message: '请填写安全问题'
            });
            return false;
          }

          this.$axios.post(
            this.url + '/api/user/security',
            {data: {
                1:answer1,
                2:answer2,
                3:answer3,
              }}
          ).then(res => {
            if (res.data.code !== 200) {
              this.$message.warning({
                showClose: true,
                message: res.data.msg
              });
            } else {
              // 设置成功
              user.is_problem = true;
              localStorage.setItem('UserInfo', JSON.stringify(user));
              this.dialogproblem = false
            }
          }).catch(err => {
          });
        },

        // 手机号绑定
        bindmobile(){
          this.$refs.bindmobileFrom.validate(valid => {
            if (valid) {
              let user = this.user
              this.$axios.post(
                this.url + '/api/user/set-phone',
                {
                  password: this.bindmobileFrom.password,
                  phone: this.bindmobileFrom.tel,
                  code: this.bindmobileFrom.smscode,
                }
              ).then(res => {
                if (res.data.code !== 200) {
                  this.$message.warning({
                    showClose: true,
                    message: res.data.msg
                  });
                } else {
                  // 绑定成功
                  user.phone = true;
                  localStorage.setItem('UserInfo', JSON.stringify(user));
                  this.dialogphone = false
                }
              }).catch(err => {
              });
            } else {
              // 登录表单校验失败
              return false;
            }
          });
        },

        // 邮箱设置
        bindemail() {
            this.$refs.emailForm.validate(valid => {
              if (valid) {
                this.$axios.post(
                  this.url + '/api/user/set-email',
                  {
                    password: this.emailForm.password,
                    email: this.emailForm.email,
                  }
                ).then(res => {
                  if (res.data.code !== 200) {
                    this.$message.warning({
                      showClose: true,
                      message: res.data.msg
                    });
                  } else {
                    this.$message.success({
                      showClose: true,
                      message: res.data.msg
                    });
                    // 绑定成功
                    this.dialogFormVisible = false
                  }
                }).catch(err => {
                });
              } else {
                // 登录表单校验失败
                return false;
              }
            });
        }
      }
    }
</script>

<style scoped lang="scss">
.securitysetting{
  text-align: left;
  padding: 20px;
  .content{
    h4{
      font-size: 16px;
      border-bottom: 1px solid #eeeeee;
      line-height: 40px;
    }
    .list{
      /*margin-top: 30px;*/
      .list-content{
        margin-top: 30px;
        display: flex;
        height: 60px;
        line-height: 60px;
        justify-content: space-between;
        border-bottom: 1px solid #eeeeee;
        .left{
          i{
            svg{
              width: 30px;
              color: #eeeeee;
            }
          }
          b{
            vertical-align: middle;
            font-size: 14px;
            color: rgba(0,0,0,.88);
            font-weight: 500;
            margin-right: 24px;
            line-height: 34px;
            margin-left: 10px;
          }
          span{
            vertical-align: middle;
            font-size: 12px;
            color: rgba(0,0,0,.56);
            line-height: 34px;
          }
        }
        .right{
          span{
            margin-right: 10px;
          }
          .security{
            color: #43bc60;
            font-size: 12px;
          }
          .unsafe{
            color: #ffa51f;
            font-size: 12px;
          }
          button{
            display: inline-block;
            text-align: center;
            vertical-align: middle;
            touch-action: manipulation;
            cursor: pointer;
            white-space: nowrap;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            font-size: 14px;
            font-weight: 500;
            padding: 9px 12px;
            line-height: 1;
            border: 1px solid transparent;
            background: none;
            border-radius: 4px;
            outline: none;
            background: #fff;
            border-color: rgba(0,0,0,.3);
            color: rgba(0,0,0,.88)!important;
            span{
              color: rgba(0,0,0,.88)!important;
            }
          }
          .el-button{
            color: rgba(0,0,0,.88)!important;
          }
        }
      }
    }
  }
  label{
    font-weight: 400;
    color: rgba(0,0,0,.56);
    line-height: 1;
    margin-bottom: 0;
    font-size: 12px;
    width: 100%;
    margin-top: 10px;
  }
  .questions{
    h6{
      background: rgba(252,144,0,0.1);
      color: rgba(252,144,0,1);
      line-height: 40px;
      font-size: 14px;
      padding-left: 10px;
    }
    .questions-buttom{
      .one{
        margin-top: 15px;
        p{
          line-height: 20px;
          white-space: nowrap;
          overflow: hidden;
          font-size: 14px;
          color: rgba(0,0,0,.88);
          border-bottom: 1px solid rgba(0,0,0,.16);
          padding: 5px 0 -1px;
        }
        input{
          width: 100%;
          border: none;
          border-bottom: 1px solid rgba(0,0,0,.16);
          outline: none;
          color: rgba(0,0,0,.88);
          font-size: 12px;
        }
      }
    }
  }
}
@media only screen and (max-width: 900px){
  .list-content{
    .left{
      span{
        display: none;
      }
    }
  }
}
</style>

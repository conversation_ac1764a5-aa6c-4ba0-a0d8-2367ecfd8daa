<template>
  <div class="security">
    <el-row>
      <el-col :span="24">
        <el-card class="box-card">
          <div class="clearfix left">
            <div style="float: left; display: inline-block;line-height: 32px">
              <svg t="1632791172785" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8749" width="200" height="200"><path d="M683.058 364.695c11 0 22 1.016 32.943 1.976C686.564 230.064 538.896 128 370.681 128c-188.104 0.66-342.237 127.793-342.237 289.226 0 93.068 51.379 169.827 136.725 229.256L130.72 748.43l119.796-59.368c42.918 8.395 77.37 16.79 119.742 16.79 11 0 21.46-0.48 31.914-1.442a259.168 259.168 0 0 1-10.455-71.358c0.485-148.002 128.744-268.297 291.403-268.297l-0.06-0.06z m-184.113-91.992c25.99 0 42.913 16.79 42.913 42.575 0 25.188-16.923 42.579-42.913 42.579-25.45 0-51.38-16.85-51.38-42.58 0-25.784 25.93-42.574 51.38-42.574z m-239.544 85.154c-25.384 0-51.374-16.85-51.374-42.58 0-25.784 25.99-42.574 51.374-42.574 25.45 0 42.918 16.79 42.918 42.575 0 25.188-16.924 42.579-42.918 42.579z m736.155 271.655c0-135.647-136.725-246.527-290.983-246.527-162.655 0-290.918 110.88-290.918 246.527 0 136.128 128.263 246.587 290.918 246.587 33.972 0 68.423-8.395 102.818-16.85l93.809 50.973-25.93-84.677c68.907-51.93 120.286-119.815 120.286-196.033z m-385.275-42.58c-16.923 0-34.452-16.79-34.452-34.179 0-16.79 17.529-34.18 34.452-34.18 25.99 0 42.918 16.85 42.918 34.18 0 17.39-16.928 34.18-42.918 34.18z m188.165 0c-16.984 0-33.972-16.79-33.972-34.179 0-16.79 16.927-34.18 33.972-34.18 25.93 0 42.913 16.85 42.913 34.18 0 17.39-16.983 34.18-42.913 34.18z" fill="#09BB07" p-id="8750"></path></svg>
              <span>微信登录</span>
            </div>
            <div style="float: right;" class="rightcont">
              <span style="color: #42B983" v-if="user.is_wechat">已绑定</span>
              <span v-else>未绑定</span>
              <el-button v-if="!user.is_wechat" @click="bind('wechat')">绑定账号</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="24" v-if="user.is_wechat">
        <div class="jiesao" style="text-align: center; margin-top: 20px;">
            <p>
              为享受更好的服务，建议您开启微信课程通知。<br/>
              扫码关注服务号，即可成功开启课程通知
            </p>
        </div>
        <div class="imgconter">
          <ul>
            <li>
              <el-col :span="24">
                <img src="../../assets/img/wechat1.jpg" alt="">
              </el-col>
            </li>
          </ul>
        </div>
      </el-col>

      <el-col :span="24" style="margin-top: 40px;">
        <el-card class="box-card">
          <div class="clearfix left">
            <div style="float: left; display: inline-block;line-height: 32px">
              <svg style="width: 26px;height: 26px;" t="1633576209110" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4413" width="20" height="20"><path d="M512 0C229.003636 0 0 229.003636 0 512s229.003636 512 512 512 512-229.003636 512-512S794.996364 0 512 0z m210.385455 641.396364c-7.447273 9.309091-26.996364-1.861818-41.89091-32.581819-3.723636 13.963636-13.032727 36.305455-34.443636 64.232728 35.374545 8.378182 44.683636 42.821818 33.512727 61.44-8.378182 13.032727-26.996364 24.203636-59.578181 24.203636-58.647273 0-83.781818-15.825455-95.883637-26.996364-1.861818-2.792727-5.585455-3.723636-10.24-3.723636-4.654545 0-7.447273 0.930909-10.24 3.723636-11.170909 11.170909-37.236364 26.996364-95.883636 26.996364-32.581818 0-52.130909-11.170909-59.578182-24.203636-12.101818-18.618182-1.861818-53.061818 33.512727-61.44-20.48-27.927273-29.789091-50.269091-34.443636-64.232728-13.963636 30.72-34.443636 42.821818-41.890909 32.581819-5.585455-8.378182-8.378182-26.065455-7.447273-38.167273 3.723636-46.545455 34.443636-85.643636 53.061818-106.123636-2.792727-5.585455-8.378182-40.029091 14.894546-63.301819v-1.861818c0-92.16 65.163636-158.254545 148.014545-158.254545 81.92 0 148.014545 66.094545 148.014546 158.254545v1.861818c23.272727 23.272727 17.687273 57.716364 14.894545 63.301819 17.687273 20.48 49.338182 59.578182 53.061818 106.123636 0.930909 12.101818-0.930909 29.789091-7.447272 38.167273z" fill="#30A5DD" p-id="4414"></path>
              </svg>
              <span>QQ登录</span>
            </div>
            <div style="float: right;" class="rightcont">
              <span style="color: #42B983" v-if="user.is_qq">已绑定</span>
              <span v-else>未绑定</span>
              <el-button  v-if="!user.is_qq" @click="bind('qq')">绑定账号</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  inject: ['reload'],
  name: "socialite",
  data(){
    return{
      showif:false,
      showiftwo:false,

      user: {}
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.user = JSON.parse(localStorage.getItem('UserInfo'));
    })
  },
  methods: {
    bind(type){
      if (type === 'wechat') {
        window.open('https://main.radirhino.com/auth/weixinweb?user_id=' + this.user.id, '_blank');
        localStorage.removeItem('IsWechatBind');
        localStorage.removeItem('bindTips');
        localStorage.removeItem('bindCode');
        let _this = this;
        this.timer = setInterval(() => {
          let IsWechatBind = localStorage.getItem('IsWechatBind');
          let bindCode = localStorage.getItem('bindCode');
          let bindTips = localStorage.getItem('bindTips');

          if (IsWechatBind == 'true') {
            clearInterval(this.timer)
            let user = this.user;

            if (bindCode == '200') {
              this.$axios.get(this.url + '/api/user',{
              }).then(res => {
                if (res.data.code) {
                  this.$nextTick(() => {
                    _this.user = res.data.data;
                  })
                  localStorage.setItem('UserInfo', JSON.stringify(res.data.data));
                }
              }).catch(err => {
              })

              this.$message.success(bindTips);
            } else {
              this.$message.warning(bindTips);
            }
            console.log('bind.success');

            localStorage.removeItem('IsWechatBind');
            localStorage.removeItem('bindTips');
            localStorage.removeItem('bindCode');
            this.reload()
          }
        }, 3000);
      } else if (type === 'qq') {
        window.open('https://main.radirhino.com/auth/qq?user_id=' + this.user.id, '_blank');
        localStorage.removeItem('IsWechatBind');
        localStorage.removeItem('bindTips');
        localStorage.removeItem('bindCode');
        let _this = this;
        this.timer = setInterval(() => {
          let IsWechatBind = localStorage.getItem('IsWechatBind');
          let bindCode = localStorage.getItem('bindCode');
          let bindTips = localStorage.getItem('bindTips');

          if (IsWechatBind == 'true') {
            clearInterval(this.timer)
            let user = this.user;

            if (bindCode == '200') {
              this.$axios.get(this.url + '/api/user',{
              }).then(res => {
                if (res.data.code) {
                  this.$nextTick(() => {
                    _this.user = res.data.data;
                  })
                  localStorage.setItem('UserInfo', JSON.stringify(res.data.data));
                }
              }).catch(err => {
              })

              this.$message.success(bindTips);
            } else {
              this.$message.warning(bindTips);
            }
            console.log('bind.success');

            localStorage.removeItem('IsWechatBind');
            localStorage.removeItem('bindTips');
            localStorage.removeItem('bindCode');
            this.reload()
          }
        }, 3000);
      }
    }
  }
}
</script>

<style scoped lang="scss">
ul,li,ol{
  list-style: none;
}
.el-card.is-always-shadow, .el-card.is-hover-shadow:focus, .el-card.is-hover-shadow:hover{
  box-shadow: initial;
}
.security{
  text-align: left;
  .left{
    text-align: left;
    .icon{
      width: 25px;
      height: 25px;
    }
    .rightcont{
      font-size: 12px;
      color: #ff9901;
      button{
        color: #24416b!important;
        margin-left: 20px;
      }
    }
  }
}
.jiesao{
  p{
    margin-top: 20px;
    font-weight: bold;
  }
}
.imgconter{
  text-align: center;
  margin-top: 40px;
  img{
    width:30%;
  }
}
@media only screen and (max-width: 900px){
  .imgconter{
    text-align: center;
    margin-top: 40px;
    img{
      width:100%;
    }
  }
}
</style>

<template>
  <div class="payment">
    <!--导航-->
    <headback></headback>
    <div class="payment-content">
      <div class="zhong">
          <el-result :title="title" :icon="icon" :subTitle="subTitle">
            <template slot="extra">
              <el-button type="primary" size="medium" @click="goHome">返回首页</el-button>
            </template>
          </el-result>
      </div>
    </div>

  </div>
</template>

<script>
  import headback from "../headback";

  export default {
    name: "emailverify",
    components:{
      headback,
    },
    data(){
      return{
        title: '邮箱验证中',
        subTitle: '',
        icon: '',
        code: 0
      }
    },
    computed: {

    },
    mounted(){
      this.verify()
    },
    methods:{
      verify(){
        var email = this.$route.query.email;
        var _token = this.$route.query._token;
        var timestamp = this.$route.query._timestamp;
        var verify_code = this.$route.query.verify_code;

        if (email == '' || email == undefined || _token == '' || _token == undefined
          || timestamp == '' || timestamp == undefined || verify_code == '' || verify_code == undefined) {
          this.title = '邮箱验证：参数错误';
          this.icon = 'error';
          this.subTitle = '';
          return false;
        }

        let user = JSON.parse(localStorage.getItem('UserInfo'));

        this.$axios.post(this.url + '/api/email-verify', {
          email: email,
          _token: _token,
          timestamp: timestamp,
          verify_code: verify_code,
        }).then(res => {
          this.title = res.data.msg;
          this.code = res.data.code;
          if (res.data.code == 200) {
            user.email = email
            localStorage.setItem('UserInfo', JSON.stringify(user));

            this.icon = 'success';
            this.subTitle = '保护好你的邮箱，如果忘记来密码，你可以用它来重置你的密码哦';
          } else {
            this.icon = 'error';
          }
        }).catch(err => {
        });
      },

      goHome(){
        this.$router.push({path: '/'});
      }

    },
    created() {
    },
  }

</script>

<style scoped lang="scss">
  .blue {
    border: 2px solid #E6A23C!important;
  }
  //导航
  .navbar-expand-lg .navbar-collapse{
    display:flex;
    justify-content:space-between;
  }
  .navbar{
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    width: 100%;
    /*position: relative;*/
    /*top: 0;*/
    z-index: 99;
    /*position: absolute;*/
    /*top:0;*/
    /*left: 0;*/
  }
  button{
    outline: none;
  }
  .bg-info{
    /*background-color: rgba(0,0,0,0) !important;*/
    background-color: rgb(17, 19, 42)!important;
  }
  .navbar-dark .navbar-brand,.navbar-dark .navbar-nav .nav-link{
    color: white;
  }
  .btn-success{
    color: white!important;
    background: none!important;
    border: none!important;
    margin-top: 5px;
  }

  .payment-content{
    margin: 40px 15%;
    background: white;
    padding: 50px 0;
    .zhong{
      width: 70%;
      margin: 0 auto;
      .payment-top{
        width: 80%;
        margin: 0 auto;
        h4{
          text-align: left;
          margin-bottom: 10px;
          line-height: 80px;
          font-size: 40px;
        }
        .payment-top-text{
          /*background: rgba(252, 144, 0, 0.1);*/
          color: #24416b;
          text-align: left;
          padding: 15px;
          padding-left: 0;
          p{
            margin: 0;
            span{
              color: red;
              font-size: 20px;
              padding-left: 5px;
            }
          }
        }
        .complete-zhong{
          margin-top: 20px;
          text-align: left;
          border-bottom: 1px solid #eeeeee;
          padding-bottom: 50px;
          button{
            padding:10px 20px;
            background: #E6A23C;
            outline: none;
            border: none;
            color: white;
            margin-right: 20px;
          }
        }
        .complete-bottom{
          text-align: left;
          margin: 50px 0;
        }
      }
    }
  }


  @media only screen and (max-width: 900px){
    .navbar-expand-lg .navbar-collapse{
      display:block;
    }
    .navbar-dark .navbar-toggler{
      color: rgba(0,0,0,0.5);
      border-color: rgba(0,0,0,0.5);
    }
    /deep/.navbar-dark .navbar-toggler-icon{
      background-image: url("../../assets/img/nav.png");
    }
    .payment-content{
      margin: 40px 5%;
    }
  }



</style>

<template>
  <div class="maincont">
    <el-row :gutter="10" v-for="item in learnData.slice((currentPage-1)*pagesize,currentPage*pagesize)" :key="item.id">
      <el-col :span="6"><div class="grid-content bg-purple">
        <div class="block">
          <el-image :src="item.course.iamges_src"></el-image>
        </div>
      </div></el-col>
      <el-col :span="14"><div class="grid-content bg-purple-light">
        <div class="content">
          <h5>{{item.course.name}}</h5>
          <div class="progress">
            <span class="text" style="width: 40px;">进度</span>
            <el-progress :percentage="100" :show-text="text"></el-progress>
          </div>
        </div>
      </div></el-col>
      <el-col :span="4"><div class="grid-content continue">
        <router-link :to="{name:'coursepage',params:{id: item.course_id}}">
          <el-button type="success">进入课程</el-button>
        </router-link>
      </div></el-col>
    </el-row>
    <el-pagination
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-size="pagesize"
      layout="total, prev, pager, next"
      :total="learnData.length" v-if="learnData.length !== 0">
    </el-pagination>
  </div>
</template>

<script>
  export default {
    name: "finish",
    data(){
      return{
        currentPage:1,
        pagesize:5,
        text:true,
        learnData:[]
      }
    },
    mounted() {
      // console.log(111);
      this.getCourse()
    },
    methods:{
      handleSizeChange(val) {
        this.pagesize = val;
      },
      handleCurrentChange(val) {
        this.currentPage = val;
      },

      getCourse() {
        let user = JSON.parse(localStorage.getItem('UserInfo'))
        if (user && Object.keys(user).length !== 0) {
          this.$axios.get(this.url + '/api/user/courses', {
            params: {
              type: 'learn',
              status: 1,
            }
          }).then(res => {
            if (res.data.code === 200) {
              this.learnData = res.data.data.data
            }
          }).catch(err => {
          });
        }
      }
    },
  }
</script>

<style scoped lang="scss">
  .el-row{
    margin-top: 20px;
  }
  .content{
    width: 100%;
    margin-top: 15px;
    /deep/ h5{
      width: 100%;
      word-wrap:break-word;
      word-break:normal;
      text-overflow: -o-ellipsis-lastline;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      //-webkit-box-orient: vertical;
    }
    .progress{
      background-color: #fff;
      margin-top: 40px;
      .el-progress{
        width: 100%;
      }
    }
  }
  .continue{
    margin-top: 40px;
  }
  @media only screen and (max-width: 900px){
    .maincont{
      overflow: auto;
    }
    .content{
      margin-top: 0;
      /deep/ h5{
        font-size: 12px;
      }
      .progress{
        margin-top: 0;
        /deep/.el-progress{
          font-size: 12px !important;
        }
      }
    }
    .continue{
      margin-top: 5px;
      padding: 5px 8px;
    }
  }
</style>

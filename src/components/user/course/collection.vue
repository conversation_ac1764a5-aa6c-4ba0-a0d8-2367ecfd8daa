<template>
  <div class="maincont">
    <el-row :gutter="10" v-for="item in collectData.slice((currentPage-1)*pagesize,currentPage*pagesize)" :key="item.id">
      <el-col :span="6"><div class="grid-content bg-purple">
        <div class="block">
          <el-image :src="item.course.images_src" style="height: 100px;width: 100%"></el-image>
        </div>
      </div></el-col>
      <el-col :span="14"><div class="grid-content bg-purple-light">
        <div class="content">
          <h5>{{item.course.name}}</h5>
        </div>
      </div></el-col>
      <el-col :span="4"><div class="grid-content continue">
        <el-button type="primary" @click="cancelCollect(item.id)">取消收藏</el-button>
      </div></el-col>
    </el-row>
    <el-pagination
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-size="pagesize"
      layout="total, prev, pager, next"
      :total="collectData.length" v-if="collectData.length !== 0">
    </el-pagination>
    <el-empty description="暂无收藏课程" v-if="collectData.length === 0"></el-empty>
  </div>
</template>

<script>
  export default {
    name: "collection",
    data(){
      return{
        currentPage:1,
        pagesize:5,
        text:true,
        collectData:[]
      }
    },
    mounted() {
      this.getCourse()
    },
    methods:{
      handleSizeChange(val) {
        this.pagesize = val;
      },
      handleCurrentChange(val) {
        this.currentPage = val;
      },

      getCourse() {
        let user = JSON.parse(localStorage.getItem('UserInfo'))
        if (user && Object.keys(user).length !== 0) {
          this.$axios.get(this.url + '/api/user/courses', {
            params: {
              type: 'collect'
            }
          }).then(res => {
            if (res.data.code === 200) {
              this.collectData = res.data.data.data
            } else if (res.data.code === 4001) {
              localStorage.removeItem('UserInfo');
              localStorage.removeItem('Authorization');
              this.$router.push({name:"login"})
            }
          }).catch(err => {
          });
        }
      },

      cancelCollect(userCourseId){
        if (userCourseId) {
          this.$axios.post(this.url + '/api/courses/collect/cancel', {
            user_course_id: userCourseId
          }).then(res => {
            if (res.data.code === 200) {
              this.$message.success({
                showClose: true,
                message: res.data.msg
              });

              let newCollectData = [];
              this.collectData.forEach((item, index)=>{
                if (item.id !== userCourseId) {
                  newCollectData.push(item)
                }
              })
              this.$nextTick(()=>{
                this.collectData = newCollectData;
              })
            } else if (res.data.code === 4001) {
              localStorage.removeItem('UserInfo');
              localStorage.removeItem('Authorization');
              this.$router.push({name:"login"})
            } else {
              this.$message.warning({
                showClose: true,
                message: res.data.msg
              });
            }
          }).catch(err => {
          });
        } else {
          this.$message.warning({
            showClose: true,
            message: '取消好像出了点小问题，先缓一缓吧'
          });
          return false;
        }
      }
    }
  }
</script>

<style scoped lang="scss">
  .el-row{
    margin-top: 20px;
  }
  .content{
    width: 100%;
    margin-top: 15px;
    /deep/ h5{
      width: 100%;
      word-wrap:break-word;
      word-break:normal;
      text-overflow: -o-ellipsis-lastline;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      //-webkit-box-orient: vertical;
    }
    .progress{
      background-color: #fff;
      margin-top: 40px;
      .el-progress{
        width: 100%;
      }
    }
  }
  .continue{
    margin-top: 40px;
  }
  @media only screen and (max-width: 900px){
    .maincont{
      overflow: auto;
    }
    .content{
      margin-top: 0;
      /deep/ h5{
        font-size: 12px;
      }
      .progress{
        margin-top: 0;
        /deep/.el-progress{
          font-size: 12px !important;
        }
      }
    }
    .continue{
      margin-top: 5px;
      padding: 5px 8px;
    }
  }
</style>

<template>
<div>

  <el-upload
    :ref="refName"
    v-loading="loading"
    :class="className"
    :data="{}"
    :list-type="listType"
    action=""
    :http-request="fnUploadRequest"
    :accept="accept"
    :limit="limit"
    :multiple="multiple"
    :file-list="fileList"
    :show-file-list="showFileList"
    :before-upload="handleBeforeUpload"
    :on-exceed="handleExceed"
    :on-progress="handleProgress"
    :on-success="handleSuccess"
    :on-preview="handlePicture"
    :on-remove="handleRemove"
    :on-error="handleError"
    :on-change="handleChange"
    class="upload-demo"
    
    >
    <el-button size="small"  type="primary">点击上传</el-button>
  </el-upload>
</div>
  
</template>

<script>
import OSS from 'ali-oss'

export default {
  props: {
    refName: {
      type: String,
      default: `elUpload${String(+new Date()) + Math.random().toString(36).substr(2)}`
    },
    refresh: {
      type: Boolean,
      default: true
    },
    uploadType: {
      type: String,
      default: 'uploadToken'
    },
    listType: {
      type: String,
      default: ''
    },
    accept: {
      type: String,
      default: ''
    },
    limit: {
      type: Number,
      default: 1
    },
    multiple: {
      type: Boolean,
      default: false
    },
    className: {
      type: String,
      default: 'single-upload upload-default'
    },
    showFileList: {
      type: Boolean,
      default: true
    },
    fileList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      oss: {
        AccessKeyId: '',
        AccessKeySecret: '',
        BucketName: '',
        Expiration: '',
        SecurityToken: ''
      },
      loading: false
    }
  },
  created() {
    if (localStorage.getItem('oss')) {
      this.oss = JSON.parse(localStorage.getItem('oss'))
      console.log(this.oss);
    }
  },
  methods: {
    handleBeforeUpload(file) {
      if (this.listType) {
        this.loading = true
      }
      let verifySize
      return new Promise((resolve, reject) => {
        this.$emit('handleBeforeUpload', file, res => {
          if (res) {
              resolve(file)
          } else {
            this.loading = false
            reject(false)
          }
        }, this.refName)
      })
    },
    handleSuccess(res, file, fileList) {
      this.loading = false;
      this.$emit('handleSuccess', res, file, fileList)
    },
    handlePicture(file) {
      this.$emit('handlePicture', file)
    },
    handleChange(file, fileList) {
      this.$emit('handleChange', file, fileList)
    },
    handleExceed(files, fileList) {
      this.$emit('handleExceed', files, fileList)
    },
    handleRemove(file, fileList) {
      console.log(file, fileList, '===============');
      this.$emit('handleRemove', file, fileList)
    },
    handleProgress(event, file, fileList) {
      this.$emit('handleProgress', event, file, fileList)
    },
    handleError(err, file, fileList) {
      this.$emit('handleError', err, file, fileList)
    },
    async fnUploadRequest(options) {
      
      console.log(22222222);
      try {
        // if (getToken(OssKey)) {
        //   this.oss = JSON.parse(getToken(OssKey))
        // }
        
        if (localStorage.getItem('oss')) {
          this.oss = JSON.parse(localStorage.getItem('oss'))
          console.log(this.oss);
        }
        const client = new OSS({
          region: 'oss-cn-beijing',
          accessKeyId: this.oss.AccessKeyId,
          accessKeySecret: this.oss.AccessKeySecret,
          stsToken: this.oss.SecurityToken,
          bucket: this.oss.BucketName,
          refreshSTSToken: async() => {
          }
        })
        console.log(client);
        const _this = this
        let filename = ''
        // filename = `${String(+new Date()) + Math.random().toString(36).substr(2)}.${options.file.name.split('.').pop()}`
        var date = ""+(new Date().getFullYear())+( new Date().getMonth()+1)+(new Date().getDate())
        var time2 = new Date().getTime();
        filename = `clients/uploads/${date}/${time2}${options.file.name.replace(/\s*/g, '')}`
        console.log(filename, options.file);
        // const result = await client.initMultipartUpload(filename);
        // console.log(result);
        client.multipartUpload(filename, options.file, {
            parallel: 4,
            partSize: 1024 * 1024,
            progress: function (p, cpt, res) {
              _this.$emit('elProgress', p, cpt, res)
            }
          })
          .then(res => {
            if (res.res.statusCode === 200) {
              options.onSuccess(res)
            } else {
              options.onError('上传失败')
            }
          })
          .catch((e) => {
            options.onError('上传失败')
            this.$message.error('上传失败，请重新上传')
          })
          .finally(() => {
            this.loading = false
          })
      } catch (e) {
        this.loading = false
        console.log('上传失败');
        console.log(e);
        options.onError('上传失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.single-upload {
  display: inline-block;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.single-upload:hover {
  border-color: #409EFF;
}
.single-upload >>> .el-upload {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
}
.single-upload >>> .el-upload>img {
  width: 100%;
}
.upload-big {
  width: 202px;
  height: 202px;
}
.upload-default {
  width: 142px;
  height: 142px;
}
.upload-mini {
  width: 82px;
  height: 82px;
}

</style>


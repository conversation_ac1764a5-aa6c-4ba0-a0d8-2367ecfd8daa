<template>
  <div class="video-upload-box">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="160px"
      :loading="btnLoading"
    >
      <el-form-item prop="images">
        <span slot="label">素材图片<br />可添加多张</span>
        <fileupload3
          class-name="file-uploader"
          :fileList="form.images"
          ref-name="images"
          :limit="8"
          :accept="'image/*'"
          :listType="'picture-card'"
          @handleBeforeUpload="imagesbeforeAvatarUpload"
          @handleSuccess="imageshandleAvatarSuccess"
          @elProgress="imageshandProgress"
          @handleRemove="imageshandleRemove"
        >
        </fileupload3>
      </el-form-item>

      <el-form-item label="素材名称" prop="name">
        <el-input v-model="form.name" placeholder="素材名称" />
      </el-form-item>
      <el-form-item label="标签" prop="tag">
        <el-select
          v-model="form.tag"
          multiple
          filterable
          placeholder="请选择标签"
        >
          <el-option
            v-for="(item, index) in tagList"
            :key="index"
            :label="item.tag"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="价格" prop="price">
        <el-input-number
          v-model="form.price"
          :precision="2"
          :min="0"
          placeholder="价格"
        />
        <span style="color: red">光子</span> &nbsp;&nbsp;&nbsp;
        <span style="color: #cccccc">注意：1光子 = 10元人民币</span>
      </el-form-item>

      <el-form-item label="分类" prop="cate_id">
        <el-select v-model="form.cate_id" filterable placeholder="请选择分类">
          <el-option-group
            v-for="group in options"
            :key="group.id"
            :label="group.name"
          >
            <div v-for="item in group.children" :key="item.id">
              <el-option
                :label="group.name + ' / ' + item.name"
                :value="item.id"
              ></el-option>
              <div v-for="item2 in item.children" :key="item2.id">
                <el-option
                  :label="group.name + ' / ' + item.name + ' / ' + item2.name"
                  :value="item2.id"
                ></el-option>
                <div v-for="item3 in item2.children" :key="item3.id">
                  <el-option
                    :label="
                      group.name +
                      ' / ' +
                      item.name +
                      ' / ' +
                      item2.name +
                      ' / ' +
                      item3.name
                    "
                    :value="item3.id"
                  ></el-option>
                </div>
              </div>
            </div>
          </el-option-group>
        </el-select>
      </el-form-item>

      <el-form-item label="素材介绍" prop="desc">
        <el-link type="primary" :underline="false" @click="onTinymce(form)"
          >点击编辑</el-link
        >

        <edit-tinymce
          v-if="editTinymceVisible"
          ref="editTinymce"
          @info="onInfo"
        />
      </el-form-item>

      <el-form-item label="素材上传" prop="material">
        <Fileupload4
          class-name="file-uploader"
          :fileList="form.fileList"
          ref-name="material"
          @handleBeforeUpload="filebeforeAvatarUpload"
          @handleSuccess="filehandleAvatarSuccess"
          @elProgress="filehandProgress"
          @handleRemove="filehandleRemove"
        >
        </Fileupload4>

        <div style="max-width: 300px">
          <el-progress
            style="margin: 20px 0"
            :text-inside="true"
            :stroke-width="26"
            :percentage="fileprogress"
          ></el-progress>
        </div>
      </el-form-item>

      <!-- <el-form-item label="" prop="push">
        <el-checkbox v-model="form.push">发布申明：发布内容符合国家法律法规并已熟知《犀光个人发布视频申明要求》</el-checkbox>
      </el-form-item> -->
    </el-form>
    <div slot="footer" class="dialog-footer" style="text-align: center">
      <el-button type="primary" :loading="btnLoading" @click="onFormSubmit(0)">
        存入草稿
      </el-button>
      <el-button type="primary" :loading="btnLoading" @click="onFormSubmit(2)">
        立即发布
      </el-button>
    </div>
  </div>
</template>

<script>
import CustomUpload from "./CustomUpload";
import VideosUpload from "./VideosUpload";
import EditTinymce from "./EditTinymce";
import fileupload3 from "./Fileupload3";
import Fileupload4 from "./Fileupload4";
import { getTagList } from "../../../utils/request";

export default {
  name: "videoUploadBox",
  components: {
    CustomUpload,
    VideosUpload,
    EditTinymce,
    fileupload3,
    Fileupload4,
  },
  props: {
    formrow: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      tagList: [],
      options: [],
      progress: 0,
      visible: false,
      btnLoading: false,
      editTinymceVisible: false,
      imageViewer: false,
      imageViewerList: [],
      domin: "",
      currentName: "",
      // 作者
      authorOptions: [],
      cateOptions: [],
      form: {
        name: "",
        author: "",
        intro: "",
        explain: "",
        images: [],
        type: "",
        web_link: "",
        sz_course_id: "",
        is_publish: 0,
        is_integral: 0,
        integral_num: 0,
        give_integral: 0,
        price: 0,
        send: 0,
        market_price: 0,
        videos: "",
        fileList: [],
        tag:[],
      },
      rules: {
        images: [
          {
            required: true,
            message: "请选择封面图",
            trigger: ["blur", "change"],
          },
        ],
        name: [
          {
            required: true,
            message: "请输入素材名称",
            trigger: ["blur", "change"],
          },
        ],
        price: [
          {
            required: true,
            message: "请输入价格",
            trigger: ["blur", "change"],
          },
        ],
        web_link: [
          {
            required: true,
            message: "请输入转载链接",
            trigger: ["blur", "change"],
          },
        ],
        type: [
          {
            required: true,
            message: "请选择类型",
            trigger: ["blur", "change"],
          },
        ],
        push: [
          {
            required: true,
            message: "请勾选协议",
            trigger: ["blur", "change"],
          },
        ],
        author: [
          {
            required: true,
            message: "请输入创作者",
            trigger: ["blur", "change"],
          },
        ],
        cate_id: [
          {
            required: true,
            message: "请选择分类",
            trigger: ["blur", "change"],
          },
        ],
        desc: [
          {
            required: true,
            message: "请输入课程介绍",
            trigger: ["blur", "change"],
          },
        ],
        videos: [
          {
            required: true,
            message: "请上传视频",
            trigger: ["blur", "change"],
          },
        ],
      },
      fileprogress: 0,
    };
  },

  watch: {
    formrow: {
      handler() {
        // this.form = this.formrow;
        // console.log(this.formrow ,this.form, 'update----');
      },
    },
  },
  created() {
    // if(this.formrow.id){
    //     this.form = this.formrow;
    //   console.log(this.formrow , 'create---');
    // }
    var oss = JSON.parse(localStorage.getItem("oss"));
    this.domin = oss.DoMain;
  },
  mounted() {},
  methods: {
    init(data) {
      console.log(data);
      this.form = data;
      this.getUserCate();
      this.funGetTagList();
    },
    funGetTagList() {
      getTagList(
        this.url,
        (res) => {
          this.tagList = res.data.data;
          console.log(res, "getTag");
        },
        (res) => {
          console.log(res, "getTag");
        }
      );
    },
    filehandleRemove(file, fileList) {
      this.form.fileList = [...fileList];
      // this.row.detail = [...fileList];
    },
    filebeforeAvatarUpload(file, cb, refName) {
      cb(true);
    },
    filehandleAvatarSuccess(response, file) {
      if (response && response.name) {
        this.form.fileList.push({
          name: response.name,
          url: this.domin + response.name,
        });
        console.log(this.form[this.currentName], response.name, this.domin);
      }
    },
    filehandProgress(p, cpt, res) {
      var that = this;
      // that.uploadId = cpt.uploadId;//取消时需要的参数
      that.fileprogress = Number((p * 100).toFixed(0)); //进度条
      // that.progress = Number((p * 100).toFixed(0));//进度条
    },
    imagesbeforeAvatarUpload(file, cb, refName) {
      console.log(file, cb, refName);
      const type = ["image/jpeg", "image/jpg", "image/png", "image/gif"];
      const isLt2M = file.size / 1024 / 1024 < 20;
      if (!type.includes(file.type)) {
        this.$message.error("上传图片只能是 " + type.join(",") + " 格式!");
        cb(false);
        return;
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 20M");
        cb(false);
        return;
      }
      this.currentName = refName;
      cb(true);
    },
    imageshandleAvatarSuccess(response, file) {
      console.log(response, file);
      if (response && response.name) {
        this.form.images.push({
          name: response.name,
          url: this.domin + response.name,
        });
        console.log(this.form[this.currentName], response.name, this.domin);
      }
    },
    imageshandProgress() {},
    imageshandleRemove(file, fileList) {
      this.form.images = [...fileList];
    },
    onFormSubmit(i) {
      this.form.status = i;
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.id) {
            this.$confirm(`确定对[(#${this.form.id})]进行修改?`, "编辑", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(() => {
                this.hanldaddOrUpdate();
              })
              .catch(() => {});
          } else {
            this.hanldaddOrUpdate();
          }
        } else {
          this.$message.error("请填写完整信息");
        }
      });
    },

    hanldaddOrUpdate() {
      if (this.btnLoading) return;
      
      console.log(this.form);

      const data = JSON.parse(JSON.stringify(this.form));
      this.btnLoading = true;
      this.$axios
        .post(this.url + "/api/userput/goods", data)
        .then((res) => {
          this.btnLoading = false;
          if (res.data.code == 200) {
            this.$emit("handsubmit", false);
            this.$message.success("提交成功");
            // this.$router.go(0);
            this.$refs.form.resetFields();
            this.form.fileList = [];
            this.fileprogress = 0;
          } else {
            this.$message.error(res.data.msg);
          }
        })
        .catch((err) => {});
    },
    getUserCate() {
      this.$axios
        .get(this.url + "/api/user/getUserCate?catetype=goods", {})
        .then((res) => {
          if (res.data.code == 200) {
            console.log(res, "res---------------------");
            this.options = [];
            var obj = {};
            // for (let i = 0; i < res.data.data.length; i++) {
            //   console.log(i, res.data.data, "res---------------------");
            //   if (res.data.data[i].parent_id == 0) {
            //     obj[res.data.data[i].id] = {
            //       label: res.data.data[i].name,
            //       options: [],
            //     };
            //   } else {
            //     // obj[res.data.data[i].parent_id].options.push({
            //     //   value: res.data.data[i].id,
            //     //   label: res.data.data[i].name,
            //     // });
            //   }
            // }
            // console.log(obj, 'obj-----------');
            // this.options = obj;
            this.options = this.listTree(res.data.data, "id", "parent_id").map(
              (v) => {
                if (v.alias === "tenant") {
                  v.children.forEach((item, index) => {
                    v.children[index] = { ...item, disabled: true };
                  });
                  return { ...v, disabled: true };
                }
                return v;
              }
            );
            console.log(this.options, "options");
          } else {
          }
        })
        .catch((err) => {});
    },

    listTree(data, id = "id", pid = "pid") {
      const res = [];
      const temp = {};

      for (let i = 0; i < data.length; i++) {
        temp[data[i][id]] = data[i];
      }

      for (let k = 0; k < data.length; k++) {
        if (temp[data[k][pid]] && data[k][id] !== data[k][pid]) {
          if (!temp[data[k][pid]]["children"]) {
            temp[data[k][pid]]["children"] = [];
          }

          if (!temp[data[k][pid]]["_level"]) {
            temp[data[k][pid]]["_level"] = 1;
          }

          data[k]["parent"] = temp[data[k][pid]];
          data[k]["_level"] = temp[data[k][pid]]._level + 1;

          temp[data[k][pid]]["children"].push(data[k]);
        } else {
          res.push(data[k]);
        }
      }
      return res;
    },
    onInfo(value) {
      this.form.desc = value;
      console.log(this.form.desc);
    },
    onTinymce(data) {
      this.editTinymceVisible = true;
      this.$nextTick(() => {
        data.intro = data.desc;
        this.$refs.editTinymce && this.$refs.editTinymce.init(data);
      });
    },
    handProgress(p, cpt, res) {
      var that = this;
      // that.uploadId = cpt.uploadId;//取消时需要的参数
      that.progress = Number((p * 100).toFixed(0)); //进度条
    },
    handleAvatarSuccess(response, file) {
      console.log(response, file);
      if (response && response.name) {
        this.form[this.currentName] = response.name;
        console.log(this.form[this.currentName], response.name, this.domin);
      }
    },
    beforeAvatarUpload(file, cb, refName) {
      console.log(file, cb, refName);
      const type = ["image/jpeg", "image/jpg", "image/png", "image/gif"];
      const isLt2M = file.size / 1024 / 1024 < 20;
      if (!type.includes(file.type)) {
        this.$message.error("上传图片只能是 " + type.join(",") + " 格式!");
        cb(false);
        return;
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 20M");
        cb(false);
        return;
      }
      this.currentName = refName;
      cb(true);
    },
    videobeforeAvatarUpload(file, cb, refName) {
      console.log(file, cb, refName);
      // const type = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
      const isLt2M = file.size / 1024 / 1024 < 200;
      // if (!type.includes(file.type)) {
      //   this.$message.error('上传图片只能是 ' + type.join(',') + ' 格式!')
      //   cb(false)
      //   return
      // }
      if (!isLt2M) {
        this.$message.error("上传视频大小不能超过 200M");
        cb(false);
        return;
      }
      this.currentName = refName;
      cb(true);
    },
  },
};
</script>

<style scoped lang="scss">
.video-upload-box {
  >>> .el-form-item__content {
    text-align: left;
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
  }
}
>>> .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.el-input-number {
  width: 200px;
}

.box-card {
  margin-bottom: 18px;
}
.avatar-uploader {
  display: inline-block;
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
    object-fit: contain;
  }
}
>>> .video-upload {
  .el-upload {
    border: 1px dashed #d9d9d9;
    width: 160px;
    height: 90px;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    line-height: 80px;
  }
}

::v-deep .filter-list-box {
  .wrapper {
    display: inline;
    vertical-align: top;
  }
  .upload-images {
    overflow: hidden;
    background-color: #fff;
    border: 1px solid #c0ccda;
    border-radius: 6px;
    box-sizing: border-box;
    width: 100px;
    height: 100px;
    margin: 0 8px 8px 0;
    display: inline-block;
    position: relative;
    cursor: grabbing;
    .upload-image {
      width: 100%;
      height: 100%;
      display: -webkit-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      -webkit-justify-content: center;
      justify-content: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      -webkit-align-items: center;
      align-items: center;
      & > img {
        width: 100%;
      }
    }
    .upload-actions {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      background-color: rgba(0, 0, 0, 0.5);
      text-align: center;
      display: none;
      i {
        margin-left: 6px;
        margin-top: 6px;
        &:first-child {
          margin-left: 0;
        }
      }
    }
    &:hover .upload-actions {
      display: block;
    }
    .upload-actions i {
      color: #fff;
      font-size: 18px;
      cursor: pointer;
    }
  }
}
.notice {
  margin: 10px 0 0 0;
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
}
</style>

<template>
  <el-dialog
    top="30px"
    width="1000px"
    append-to-body
    :title="'编辑'"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @closed="onClose()"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="80px" v-if="visible">
      <el-form-item label="课程介绍" prop="info">
        <quill-editor
          class="editor"
          v-model="form.info"
          :ref="rename"
          :options="editorOption"
          @blur="onEditorBlur($event)"
          @focus="onEditorFocus($event)"
          @change="onEditorChange($event)"
          @ready="onEditorReady($event)"
        ></quill-editor>
        <editor-upload
          class-name="editor-image-uploader"
          ref-name="editorimage"
          :multiple="true"
          :accept="'image/*'"
          @handleBeforeUpload="beforeAvatarUpload"
          @handleSuccess="handleAvatarSuccess"
        >
          <i
            class="el-icon-plus editor-image-icon"
            id="editorimage"
            style="opacity: 0"
          />
        </editor-upload>
        <!-- <tinymce v-if="visible" v-model="form.info" :height="400" /> -->
        <!-- <textarea :id="id" class="tinymce-textarea" /> -->
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="onFormSubmit()"> 确定 </el-button>
      <el-button @click="visible = false"> 取消 </el-button>
    </div>
  </el-dialog>
</template>

<script>
// import Tinymce from '../../components/Tinymce'
// import { mapGetters } from 'vuex'
import EditorUpload from "./EditorUpload";
import { quillEditor } from "vue-quill-editor";
import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";
export default {
  name: "EditTinymce",
  components: { quillEditor, EditorUpload },
  data() {
    return {
      rename:'myQuillEditor',
      id: "test",
      visible: false,
      form: {
        info: "test",
      },
      rules: {
        info: [
          {
            required: true,
            message: "请输入内容",
            trigger: ["blur", "change"],
          },
        ],
      },
      editorOption: {
        placeholder: "请输入",
        theme: "snow",
        modules: {
          toolbar: {
            container: [
              ["bold", "italic", "underline", "strike"], // 加粗、斜体、下划线、删除线
              ["blockquote", "code-block"], // 块引用、代码块
              [{ header: 1 }, { header: 2 }], // 标题
              [{ list: "ordered" }, { list: "bullet" }], // 有序、无序列表
              [{ script: "sub" }, { script: "super" }], // 下标、上标
              [{ indent: "-1" }, { indent: "+1" }], // 缩进
              [{ direction: "rtl" }], // 文本方向
              [{ header: [1, 2, 3, 4, 5, 6] }], // 字体大小和标题
              ["image"],
            ],
            handlers: {
              //此处是图片上传时候需要使用到的
              image: function (value) {
                console.log(value);
                if (value) {
                  // 点击图片
                  document.querySelector("#editorimage").click();
                }
              },
              size: function (value) {
                if (value) {
                  var current = this.quill.getSelection();
                  console.log(current, value, "value---------font------");
                  if (current) {
                    this.quill.formatText(current, "size", value);
                  } else {
                    this.quill.format("size", value);
                  }
                  // this.quill.setSelection(current);
                  console.log(this.quill.setSelection(current));
                } else {
                  this.quill.format("size", false);
                }
              },
            },
          },
        },
      },
      domain: "",
    };
  },

  computed: {
    // ...mapGetters([
    //   'configInfo', 'info'
    // ])
    
  },
  created(){
    console.log(this.rename, 'this.rename-----------');
  },
  methods: {
    // @handleBeforeUpload="beforeAvatarUpload"
    // @handleSuccess="handleAvatarSuccess"
    beforeAvatarUpload(file, cb, refName) {
      const type = ["image/jpeg", "image/jpg", "image/png", "image/gif"];
      const isLt2M = file.size / 1024 / 1024 < 20;
      if (!type.includes(file.type)) {
        this.$message.error("上传图片只能是 " + type.join(",") + " 格式!");
        cb(false);
        return;
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 20M");
        cb(false);
        return;
      }
      this.currentName = refName;
      cb(true);
    },
    handleAvatarSuccess(response, file) {
      if (response && response.name) {
        var oss = JSON.parse(localStorage.getItem("oss"));
        var imageUrl = oss.DoMain + response.name;
        console.log();
        let quill = this.$refs[this.rename].quill;
        console.log(this.rename, 'rename');
        let length = quill.getSelection().index;
        // 插入图片
        quill.insertEmbed(length, "image", imageUrl); // imageUrl:图片地址
        // 调整光标到最后
        quill.setSelection(length + 1);
      } else if (
        response &&
        response.requestUrls &&
        response.requestUrls.length > 0
      ) {
        console.log(response, file, "success=============");
        var imageUrl = response.requestUrls[0];
        console.log();
        let quill = this.$refs[this.rename].quill;
        let length = quill.getSelection().index;
        // 插入图片
        quill.insertEmbed(length, "image", imageUrl); // imageUrl:图片地址
        // 调整光标到最后
        quill.setSelection(length + 1);
      }
    },
    init(data) {
      this.visible = true;
      if (data) {
        if(data.rename)this.rename = data.rename;
        console.log(data);
        this.form.info = data.intro;
      }
    },
    // 失去焦点事件
    onEditorBlur(quill) {
      console.log("editor blur!", quill);
    },
    // 获得焦点事件
    onEditorFocus(quill) {
      console.log("editor focus!", quill);
    },
    // 准备富文本编辑器
    onEditorReady(quill) {
      console.log("editor ready!", quill);
    },
    // 内容改变事件，只需要这一个方法就够了
    onEditorChange({ quill, html, text }) {
      this.content = html;
    },

    onFormSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // 去除异步加载图片
          this.form.info = this.form.info.replace(
            /crossorigin=\"anonymous\"/g,
            ""
          );

          this.$emit("info", this.form.info);
          this.visible = false;
        }
      });
    },
    onClose() {
      this.visible = false;
      // this.$reset()
    },
  },
};
</script>

<style scoped>
>>> .editor {
  line-height: normal !important;
  height: 350px;
}
>>> .ql-snow .ql-tooltip[data-mode="link"]::before {
  content: "请输入链接地址:";
}
>>> .ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  border-right: 0px;
  content: "保存";
  padding-right: 0px;
}

>>> .ql-snow .ql-picker.ql-size .ql-picker-label::before,
>>> .ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: "14px";
}
>>> .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="small"]::before,
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="small"]::before {
  content: "10px";
}
>>> .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="large"]::before,
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="large"]::before {
  content: "18px";
}
>>> .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="huge"]::before,
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="huge"]::before {
  content: "32px";
}

>>> .ql-snow .ql-picker.ql-header .ql-picker-label::before,
>>> .ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: "文本";
}
>>> .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
>>> .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  content: "标题1";
}
>>> .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
>>> .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  content: "标题2";
}
>>> .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
>>> .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  content: "标题3";
}
>>> .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
>>> .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  content: "标题4";
}
>>> .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
>>> .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  content: "标题5";
}
>>> .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
>>> .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  content: "标题6";
}

>>> .ql-snow .ql-picker.ql-font .ql-picker-label::before,
>>> .ql-snow .ql-picker.ql-font .ql-picker-item::before {
  content: "标准字体";
}
>>> .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="serif"]::before,
>>> .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="serif"]::before {
  content: "衬线字体";
}
>>> .ql-snow
  .ql-picker.ql-font
  .ql-picker-label[data-value="monospace"]::before,
>>> .ql-snow
  .ql-picker.ql-font
  .ql-picker-item[data-value="monospace"]::before {
  content: "等宽字体";
}

>>> .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="12px"]::before,
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="12px"]::before {
  content: "12px";
  vertical-align: top;
}
>>> .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="14px"]::before,
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="14px"]::before {
  content: "14px";
  vertical-align: top;
}
>>> .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="16px"]::before,
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="16px"]::before {
  content: "16px";
  vertical-align: top;
}
>>> .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="18px"]::before,
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="18px"]::before {
  content: "18px";
  vertical-align: top;
}
>>> .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="20px"]::before,
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="20px"]::before {
  content: "20px";
  vertical-align: top;
}
>>> .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="22px"]::before,
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="22px"]::before {
  content: "22px";
  vertical-align: top;
}
>>> .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="24px"]::before,
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="24px"]::before {
  content: "24px";
  vertical-align: top;
}
>>> .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="28px"]::before,
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="28px"]::before {
  content: "28px";
  vertical-align: top;
}
>>> .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="32px"]::before,
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="32px"]::before {
  content: "32px";
  vertical-align: top;
}
>>> .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="36px"]::before,
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="36px"]::before {
  content: "36px";
  vertical-align: top;
}

/* 这个是字号数字对应的px值 */
>>> .ql-editor .ql-size-12 {
  font-size: 12px;
}
>>> .ql-editor .ql-size-14 {
  font-size: 14px;
}
>>> .ql-editor .ql-size-16 {
  font-size: 16px;
}
>>> .ql-editor .ql-size-18 {
  font-size: 18px;
}
>>> .ql-editor .ql-size-20 {
  font-size: 20px;
}
>>> .ql-editor .ql-size-22 {
  font-size: 22px;
}
>>> .ql-editor .ql-size-24 {
  font-size: 24px;
}
>>> .ql-editor .ql-size-28 {
  font-size: 28px;
}
>>> .ql-editor .ql-size-32 {
  font-size: 32px;
}
>>> .ql-editor .ql-size-36 {
  font-size: 36px;
}

/* 选择字号富文本字的大小 */
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="12px"]::before {
  font-size: 12px;
}
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="14px"]::before {
  font-size: 14px;
}
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="16px"]::before {
  font-size: 16px;
}
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="18px"]::before {
  font-size: 18px;
}
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="20px"]::before {
  font-size: 20px;
}
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="22px"]::before {
  font-size: 22px;
}
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="24px"]::before {
  font-size: 24px;
}
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="28px"]::before {
  font-size: 28px;
}
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="32px"]::before {
  font-size: 32px;
}
>>> .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="36px"]::before {
  font-size: 36px;
}
</style>

<template>
  <div class="video-upload-box">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="160px"
      :loading="btnLoading"
    >
      <el-form-item label="封面图" prop="images">
        <custom-upload
          class-name="avatar-uploader"
          ref-name="images"
          @handleBeforeUpload="beforeAvatarUpload"
          @handleSuccess="handleAvatarSuccess"
        >
          <img v-if="form.images" :src="domin + form.images" class="avatar" />
          <i v-else class="el-icon-plus avatar-uploader-icon" />
        </custom-upload>
        <div class="notice">注意：建议封面图尺寸 750*750px</div>
      </el-form-item>

      <el-form-item label="插件名称" prop="name">
        <el-input v-model="form.name" placeholder="插件名称" />
      </el-form-item>

      <el-form-item label="标签" prop="tag">
        <el-select
          v-model="form.tag"
          multiple
          filterable
          placeholder="请选择标签"
        >
          <el-option
            v-for="(item, index) in tagList"
            :key="index"
            :label="item.tag"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="价格" prop="price">
        <el-input-number
          v-model="form.price"
          :precision="2"
          :min="0"
          placeholder="价格"
        />
        <span style="color: red">光子</span> &nbsp;&nbsp;&nbsp;
        <span style="color: #cccccc">注意：1光子 = 10元人民币</span>
      </el-form-item>

      <el-form-item label="类型" prop="type">
        <el-radio v-model="form.type" label="原创">原创</el-radio>
        <el-radio v-model="form.type" label="转载">转载</el-radio>
      </el-form-item>

      <el-form-item label="" prop="external_link" v-if="form.type == '转载'">
        <el-input v-model="form.external_link" placeholder="转载链接" />
      </el-form-item>

      <el-form-item label="作者" prop="author">
        <el-input v-model="form.author" placeholder="作者" />
      </el-form-item>

      <el-form-item label="分类" prop="cate_id">
        <el-select v-model="form.cate_id" filterable placeholder="请选择分类">
          <el-option-group
            v-for="group in options"
            :key="group.id"
            :label="group.name"
          >
            <div v-for="item in group.children" :key="item.id">
              <el-option
                :label="group.name + ' / ' + item.name"
                :value="item.id"
              ></el-option>
              <div v-for="item2 in item.children" :key="item2.id">
                <el-option
                  :label="group.name + ' / ' + item.name + ' / ' + item2.name"
                  :value="item2.id"
                ></el-option>
                <div v-for="item3 in item2.children" :key="item3.id">
                  <el-option
                    :label="
                      group.name +
                      ' / ' +
                      item.name +
                      ' / ' +
                      item2.name +
                      ' / ' +
                      item3.name
                    "
                    :value="item3.id"
                  ></el-option>
                </div>
              </div>
            </div>
          </el-option-group>
        </el-select>
      </el-form-item>

      <div>
        <el-form-item label="添加插件">
          <div>
            <table style="width: 100%">
              <tr v-for="(item, i) in form.detailList" :key="i">
                <td style="width: 33%">{{ item.version_code }}</td>
                <td class="el-table__cell" style="width: 33%">
                  <span v-if="item.type == 'package'">插件</span>
                  <span v-if="item.type == 'manual'">手册</span>
                  <span v-if="item.type == 'case'">案例</span>
                </td>
                <td style="width: 33%">
                  <i class="el-icon-edit" @click="detailListedit(i)"></i
                  >&nbsp;&nbsp;&nbsp;
                  <i class="el-icon-delete" @click="detailListedel(i)"></i>
                </td>
              </tr>
              <tr>
                <td>
                  <i
                    class="el-icon-circle-plus-outline"
                    @click="addDetail()"
                  ></i>
                </td>
                <td></td>
                <td></td>
              </tr>
            </table>
          </div>
        </el-form-item>
      </div>
      <!-- <div>
        <el-form-item label="添加详情" prop="videos">
          <div class="video-upload">
                <videos-upload
                  class-name="avatar-uploader"
                  ref-name="videos"
                  @handleBeforeUpload="videobeforeAvatarUpload"
                  @handleSuccess="handleAvatarSuccess"
                  @elProgress="handProgress"
              >
                <video v-if="form.videos" :src="domin + form.videos" class="avatar" style="width:100%; height:100%"></video>
                <span v-else  >上传视频</span>
              </videos-upload>
              <div style="max-width:300px">
                <el-progress      
                  style="margin: 20px 0"
                  :text-inside="true"
                  :stroke-width="26"
                  :percentage="progress"
                ></el-progress>
              </div>
            
          </div>
        </el-form-item>
      </div> -->

      <el-form-item label="介绍" prop="intro">
        <el-link type="primary" :underline="false" @click="onTinymce(form)"
          >点击编辑</el-link
        >

        <edit-tinymce
          v-if="editTinymceVisible"
          ref="editTinymce"
          @info="onInfo"
        />
      </el-form-item>

      <el-form-item label="功能介绍" prop="web_link">
        <el-input v-model="form.web_link" placeholder="功能介绍" />
      </el-form-item>

      <el-form-item label="展示图片">
        <fileupload2
          class-name="file-uploader"
          :fileList="form.intros"
          ref-name="intros"
          :limit="8"
          :listType="'picture-card'"
          :accept="'image/*'"
          @handleBeforeUpload="introsbeforeAvatarUpload"
          @handleSuccess="introshandleAvatarSuccess"
          @elProgress="introshandProgress"
          @handleRemove="introshandleRemove"
        >
        </fileupload2>
      </el-form-item>

      <el-form-item label="" prop="push">
        <el-checkbox v-model="form.push"
          >发布申明：发布内容符合国家法律法规并已熟知《犀光个人发布视频申明要求》</el-checkbox
        >
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="btnLoading" @click="onFormSubmit(0)">
        存入草稿
      </el-button>
      <el-button type="primary" :loading="btnLoading" @click="onFormSubmit(2)">
        立即发布
      </el-button>
    </div>

    <div class="detail-dialog">
      <el-dialog
        title=""
        :modal-append-to-body="false"
        :visible.sync="detailDialogVisible"
        width="80%"
        :before-close="handleClose"
      >
        <div class="detial-dialog-box">
          <videodetail
            :detailList="form.detailList"
            :row="detaildata"
            :chapteroptions="chapteroptions"
            @fileDel="fileDel"
            @fileSub="fileSub"
            @listSub="listSub"
          />
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import CustomUpload from "./CustomUpload";
import VideosUpload from "./VideosUpload";
import EditTinymce from "./EditTinymce";
import videodetail from "./Videodetail";
import fileupload2 from "./Fileupload2";
import { getTagList } from "../../../utils/request";

export default {
  name: "videoUploadBox",
  components: {
    CustomUpload,
    VideosUpload,
    EditTinymce,
    videodetail,
    fileupload2,
  },
  data() {
    return {
      tagList: [],
      chapteroptions: [],
      detaildata: {
        id: "",
        version_code: "",
        platform: "",
        description: "",
        type: "",
        link: "",
        index: "",
        fileList: [],
      },
      options: [],
      progress: 0,
      visible: false,
      btnLoading: false,
      editTinymceVisible: false,
      imageViewer: false,
      imageViewerList: [],
      domin: "",
      currentName: "",
      // 作者
      authorOptions: [],
      cateOptions: [],
      form: {
        name: "",
        author: "",
        intro: "",
        explain: "",
        external_link: "",
        images: "",
        type: "",
        web_link: "",
        sz_course_id: "",
        is_publish: 0,
        is_integral: 0,
        integral_num: 0,
        give_integral: 0,
        price: 0,
        send: 0,
        market_price: 0,
        videos: "",
        detailList: [],
        intros: [],
        tag:[],
      },
      rules: {
        images: [
          {
            required: true,
            message: "请选择封面图",
            trigger: ["blur", "change"],
          },
        ],
        name: [
          {
            required: true,
            message: "请输入插件名称",
            trigger: ["blur", "change"],
          },
        ],
        price: [
          {
            required: true,
            message: "请输入价格",
            trigger: ["blur", "change"],
          },
        ],
       
        type: [
          {
            required: true,
            message: "请选择类型",
            trigger: ["blur", "change"],
          },
        ],
        push: [
          {
            required: true,
            message: "请勾选协议",
            trigger: ["blur", "change"],
          },
        ],
        author: [
          {
            required: true,
            message: "请输入创作者",
            trigger: ["blur", "change"],
          },
        ],
        cate_id: [
          {
            required: true,
            message: "请选择分类",
            trigger: ["blur", "change"],
          },
        ],
        intro: [
          {
            required: true,
            message: "请输入课程介绍",
            trigger: ["blur", "change"],
          },
        ],
        videos: [
          {
            required: true,
            message: "请上传视频",
            trigger: ["blur", "change"],
          },
        ],
      },
      detailDialogVisible: false,
    };
  },

  created() {
    var oss = JSON.parse(localStorage.getItem("oss"));
    this.domin = oss.DoMain;
    this.getUserCate();
    this.funGetTagList();
  },
  mounted() {},
  methods: {
    funGetTagList() {
      getTagList(
        this.url,
        (res) => {
          this.tagList = res.data.data;
          console.log(res, "getTag");
        },
        (res) => {
          console.log(res, "getTag");
        }
      );
    },
    introsbeforeAvatarUpload(file, cb, refName) {
      console.log(file, cb, refName);
      const type = ["image/jpeg", "image/jpg", "image/png", "image/gif"];
      const isLt2M = file.size / 1024 / 1024 < 20;
      if (!type.includes(file.type)) {
        this.$message.error("上传图片只能是 " + type.join(",") + " 格式!");
        cb(false);
        return;
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 20M");
        cb(false);
        return;
      }
      this.currentName = refName;
      cb(true);
    },
    introshandleAvatarSuccess(response, file) {
      console.log(response, file);
      if (response && response.name) {
        this.form.intros.push({
          name: response.name,
          url: this.domin + response.name,
        });
        console.log(this.form[this.currentName], response.name, this.domin);
      }
    },
    introshandProgress() {},
    introshandleRemove(file, fileList) {
      this.form.intros = [...fileList];
    },
    showFileBos() {
      this.detailDialogVisible = true;
      this.chapteroptions = [];
      for (let i = 0; i < this.form.detailList.length; i++) {
        if (this.form.detailList[i].type == "chapter") {
          this.chapteroptions.push(this.form.detailList[i]);
        }
      }
    },
    fileDel(file, fileList) {
      this.detaildata.fileList = [...fileList];
    },
    fileSub(response, file) {
      console.log(response, file);
      if (response && response.name) {
        this.detaildata.fileList.push({
          name: response.name,
          url: this.domin + response.name,
        });
      }
    },
    listSub(row) {
      if (row.index != "" && row.index != null) {
        this.form.detailList[row.index - 1] = row;
      } else {
        row.index = this.form.detailList.length + 1;
        this.form.detailList.push(row);
        console.log(this.form.detailList);
      }
      this.detaildata = {};
      this.detailDialogVisible = false;
      console.log(this.form.detailList, row.index, "============");
      // row.index = this.form.detailList.length+1;
      // this.form.detailList.push(row);
      // console.log(this.form.detailList);
      // this.detaildata = {};
      // this.detailDialogVisible = false;
    },
    handleClose() {
      this.detailDialogVisible = false;
    },

    detailListedit(i) {
      this.detaildata = this.form.detailList[i];
      this.showFileBos();
    },
    detailListedel(i) {
      this.form.detailList.splice(i, 1);
    },
    addDetail() {
      this.detaildata = {
        id: "",
        version_code: "",
        platform: "",
        description: "",
        type: "",
        link: "",
        index: "",
        fileList: [],
      };
      this.showFileBos();
    },
    onFormSubmit(i) {
      this.form.status = i;
      console.log(this.form);
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.id) {
            this.$confirm(`确定对[(#${this.form.id})]进行修改?`, "编辑", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(() => {
                this.hanldaddOrUpdate();
              })
              .catch(() => {});
          } else {
            this.hanldaddOrUpdate();
          }
        }
      });
    },

    hanldaddOrUpdate() {
      if (this.btnLoading) return;
      const data = JSON.parse(JSON.stringify(this.form));
      this.btnLoading = true;
      this.$axios
        .post(this.url + "/api/userput/plug", data)
        .then((res) => {
          this.btnLoading = false;
          if (res.data.code == 200) {
            this.$message.success("提交成功");
            // this.$router.go(0);
            this.$refs.form.resetFields();
            this.form.detailList = [];
          } else {
            this.$message.error(res.data.msg);
          }
        })
        .catch((err) => {});
    },
    getUserCate() {
      var that = this;
      this.$axios
        .get(this.url + "/api/user/getUserCate?catetype=plug", {})
        .then((res) => {
          console.log(res, "res---------------------");
          if (res.data.code == 200) {
            this.options = [];
            var obj = {};
            console.log("-----", "options----------");
            that.options = that
              .listTree(res.data.data, "id", "parent_id")
              .map((v) => {
                console.log(v, "v--------------");
                if (v.alias === "tenant") {
                  v.children.forEach((item, index) => {
                    v.children[index] = { ...item, disabled: true };
                  });
                  return { ...v, disabled: true };
                }
                return v;
              });

            console.log("-----", "options----------");
          } else {
          }
        })
        .catch((err) => {});
    },
    listTree(data, id = "id", pid = "pid") {
      const res = [];
      const temp = {};

      for (let i = 0; i < data.length; i++) {
        temp[data[i][id]] = data[i];
      }

      for (let k = 0; k < data.length; k++) {
        if (temp[data[k][pid]] && data[k][id] !== data[k][pid]) {
          if (!temp[data[k][pid]]["children"]) {
            temp[data[k][pid]]["children"] = [];
          }

          if (!temp[data[k][pid]]["_level"]) {
            temp[data[k][pid]]["_level"] = 1;
          }

          data[k]["parent"] = temp[data[k][pid]];
          data[k]["_level"] = temp[data[k][pid]]._level + 1;

          temp[data[k][pid]]["children"].push(data[k]);
        } else {
          res.push(data[k]);
        }
      }
      return res;
    },
    onInfo(value) {
      this.form.intro = value;
      console.log(this.form.intro);
    },
    onTinymce(data) {
      this.editTinymceVisible = true;
      this.$nextTick(() => {
        data.rename = 'tinymcePlug';
        this.$refs.editTinymce && this.$refs.editTinymce.init(data);
      });
    },
    handProgress(p, cpt, res) {
      var that = this;
      // that.uploadId = cpt.uploadId;//取消时需要的参数
      that.progress = Number((p * 100).toFixed(0)); //进度条
    },
    handleAvatarSuccess(response, file) {
      console.log(response, file);
      if (response && response.name) {
        this.form[this.currentName] = response.name;
        console.log(this.form[this.currentName], response.name, this.domin);
      }
    },
    beforeAvatarUpload(file, cb, refName) {
      console.log(file, cb, refName);
      const type = ["image/jpeg", "image/jpg", "image/png", "image/gif"];
      const isLt2M = file.size / 1024 / 1024 < 20;
      if (!type.includes(file.type)) {
        this.$message.error("上传图片只能是 " + type.join(",") + " 格式!");
        cb(false);
        return;
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 20M");
        cb(false);
        return;
      }
      this.currentName = refName;
      cb(true);
    },
    videobeforeAvatarUpload(file, cb, refName) {
      console.log(file, cb, refName);
      // const type = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
      const isLt2M = file.size / 1024 / 1024 < 200;
      // if (!type.includes(file.type)) {
      //   this.$message.error('上传图片只能是 ' + type.join(',') + ' 格式!')
      //   cb(false)
      //   return
      // }
      if (!isLt2M) {
        this.$message.error("上传视频大小不能超过 200M");
        cb(false);
        return;
      }
      this.currentName = refName;
      cb(true);
    },
  },
};
</script>

<style scoped lang="scss">
.video-upload-box {
  >>> .el-form-item__content {
    text-align: left;
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
  }
}
>>> .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.el-input-number {
  width: 200px;
}

.box-card {
  margin-bottom: 18px;
}
.avatar-uploader {
  display: inline-block;
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
    object-fit: contain;
  }
}
>>> .video-upload {
  .el-upload {
    border: 1px dashed #d9d9d9;
    width: 160px;
    height: 90px;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    line-height: 80px;
  }
}

::v-deep .filter-list-box {
  .wrapper {
    display: inline;
    vertical-align: top;
  }
  .upload-images {
    overflow: hidden;
    background-color: #fff;
    border: 1px solid #c0ccda;
    border-radius: 6px;
    box-sizing: border-box;
    width: 100px;
    height: 100px;
    margin: 0 8px 8px 0;
    display: inline-block;
    position: relative;
    cursor: grabbing;
    .upload-image {
      width: 100%;
      height: 100%;
      display: -webkit-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      -webkit-justify-content: center;
      justify-content: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      -webkit-align-items: center;
      align-items: center;
      & > img {
        width: 100%;
      }
    }
    .upload-actions {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      background-color: rgba(0, 0, 0, 0.5);
      text-align: center;
      display: none;
      i {
        margin-left: 6px;
        margin-top: 6px;
        &:first-child {
          margin-left: 0;
        }
      }
    }
    &:hover .upload-actions {
      display: block;
    }
    .upload-actions i {
      color: #fff;
      font-size: 18px;
      cursor: pointer;
    }
  }
}
.notice {
  margin: 10px 0 0 0;
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
}
</style>

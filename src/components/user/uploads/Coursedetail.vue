<template>
  <div class="video-upload-box">
    <el-form ref="form" :model="form" :rules="rules" label-width="160px"  :loading="btnLoading" >

      <el-form-item label="封面图" prop="images" >
        <custom-upload
            class-name="avatar-uploader"
            ref-name="images"
            @handleBeforeUpload="beforeAvatarUpload"
            @handleSuccess="handleAvatarSuccess"
        >
          <img v-if="form.images" :src="domin + form.images" class="avatar">
          <i v-else class="el-icon-plus avatar-uploader-icon" />
        </custom-upload>
        <div class="notice">
          注意：建议封面图尺寸 750*750px 
        </div>
      </el-form-item>
      <el-form-item label="标题名称" prop="name">
        <el-input v-model="form.name" placeholder="标题名称" />
      </el-form-item>
      <el-form-item label="类别" prop="type">
        <el-radio v-model="form.type" label="task">任务</el-radio>
        <el-radio v-model="form.type" label="chapter">章节</el-radio>
        <el-radio v-model="form.type" label="annex">附件</el-radio>
      </el-form-item>
      <el-form-item label="章节" prop="parent" v-if="form.type&&form.type!='chapter'">
        <el-select v-model="form.parent" filterable placeholder="请选择分类">
          <el-option
            v-for="item in chapteroptions"
            :key="item.index"
            :label="item.name"
            :value="item.index">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="添加视频" prop="link" v-if="form.type&&form.type!='chapter'">
        <div class="file-upload">
            <fileupload
                class-name="file-uploader"
                :fileList="form.fileList"
                ref-name="link"
                @handleBeforeUpload="filebeforeAvatarUpload"
                @handleSuccess="filehandleAvatarSuccess"
                @elProgress="filehandProgress"
                @handleRemove="filehandleRemove"
            >
          </fileupload>
          <div style="max-width:300px">
            <el-progress      
              style="margin: 20px 0"
              :text-inside="true"
              :stroke-width="26"
              :percentage="fileprogress"
            ></el-progress>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer" style="text-align:right;padding-right:30%;box-sizing:border-box">
      <el-button type="primary"  @click="listSub(0)">
        确定
      </el-button>
    </div>
      
  </div>
  
</template>

<script>

import CustomUpload from './CustomUpload'
import VideosUpload from './VideosUpload'
import fileupload from './FileUpload'
import EditTinymce from './EditTinymce'
import coursedetail from './Coursedetail'

  export default {
    name: "videoUploadBox",
    components: { CustomUpload , VideosUpload, EditTinymce, fileupload, coursedetail},
    props: {
      row: {
        type: Object,
        default() {
          return {
            id:'',
            images:'',
            type:'',
            link:'',
            chapter_id:'',
            parent:'',
            name:'',
            index:'',
            fileList:[],
          }
        }
      },
      detailList:{
        type:Array,
        default(){
          return [];
        }
      },
      chapteroptions:{
        type:Array,
        default(){
          return [];
        }
      },
    },
    data(){
      return{
        fileprogress:0,
        options: [],
        progress: 0,
        visible: false,
        btnLoading: false,
        editTinymceVisible: false,
        imageViewer: false,
        imageViewerList: [],
        domin: '',
        currentName: '',
        // 作者
        authorOptions:[],
        cateOptions:[],
        form: {
          id:'',
          images:'',
          itemimages:'',
          type:'',
          link:'',
          chapter_id:'',
          parent:'',
          name:'',
          index:'',
          fileList:[],
        },
        rules: {
          name: [
            { required: true, message: '请输入标题名称', trigger: ['blur', 'change'] }
          ],
          type: [
            { required: true, message: '请选择类型', trigger: ['blur', 'change'] }
          ],
        },
      }
    },
    
    created() {

      var oss = JSON.parse(localStorage.getItem('oss'))
      this.domin = oss.DoMain;
      this.getUserCate();
    },
    mounted() {
    },
    methods:{
      init(data){
        this.form = data;
      },
      filehandleRemove(file, fileList){
        // this.$emit('fileDel', response, file)  
        this.form.fileList = [...fileList];
      },
      filebeforeAvatarUpload(file, cb, refName) {
        cb(true)
      },
      filehandleAvatarSuccess(response, file){
        
        // this.$emit('fileSub', response, file)  
        if(response&&response.name){
          this.form.fileList.push({'name':response.name, url:this.domin + response.name});
        }
      },
      filehandProgress( p, cpt, res){
        var that = this;
        // that.uploadId = cpt.uploadId;//取消时需要的参数
        that.fileprogress = Number((p * 100).toFixed(0));//进度条
        // that.progress = Number((p * 100).toFixed(0));//进度条
        
      },
      listSub(){
        this.$refs.form.validate(valid => {
          if(valid){
            this.$emit('listSub', this.form)  
          }
        })      
      },
      
      hanldaddOrUpdate(i) {
        if(this.btnLoading)return;
        const data = JSON.parse(JSON.stringify(this.form))
        data.status = i;
        this.btnLoading = true
        this.$axios
        .post(this.url + "/api/userput/course", data)
        .then((res) => {

          this.btnLoading = false;
          if (res.data.code == 200) { 
            this.$message.success('提交成功')
            // this.$router.go(0);
            this.$refs.form.resetFields();
          } else {
            this.$message.error(res.data.msg);
          }
        })
        .catch((err) => {});
      },
      getUserCate()
      { 
         this.$axios
        .get(this.url + "/api/user/getUserCate?catetype=course", {})
        .then((res) => {
          var code = res.data.code;
          var resdata = res.data.data;
          if (res.data.code == 200) { 
            this.options = [];
            var obj = {};
            for (let i = 0; i < resdata.length; i++) {
              console.log( resdata[i], 'obj--------------')
              if(resdata[i].parent_id==0){
                obj[resdata[i].id] = {'label':resdata[i].name, options:[]}
              } else {
                obj[resdata[i].parent_id].options.push({
                  value: resdata[i].id,
                  label: resdata[i].name
                });
              }
            }
            this.options = obj;
            
          } else {

          }
        })
        .catch((err) => {});
        
      },
    
      
      handProgress( p, cpt, res){
        var that = this;
        // that.uploadId = cpt.uploadId;//取消时需要的参数
        that.progress = Number((p * 100).toFixed(0));//进度条
      },
      handleAvatarSuccess(response, file) {
        if(response&&response.name){
          this.form.images = response.name;
          // this.$emit('imageSub', response, file)  
        }
      },
      beforeAvatarUpload(file, cb, refName) {
        const type = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
        const isLt2M = file.size / 1024 / 1024 < 20
        if (!type.includes(file.type)) {
          this.$message.error('上传图片只能是 ' + type.join(',') + ' 格式!')
          cb(false)
          return
        }
        if (!isLt2M) {
          this.$message.error('上传图片大小不能超过 20M')
          cb(false)
          return
        }
        this.currentName = refName
        cb(true)
      },
      videobeforeAvatarUpload(file, cb, refName) {
        console.log(file, cb, refName);
        // const type = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
        const isLt2M = file.size / 1024 / 1024 < 200
        // if (!type.includes(file.type)) {
        //   this.$message.error('上传图片只能是 ' + type.join(',') + ' 格式!')
        //   cb(false)
        //   return
        // }
        if (!isLt2M) {
          this.$message.error('上传视频大小不能超过 200M')
          cb(false)
          return
        }
        this.currentName = refName
        cb(true)
      },

      
    }
  }
</script>

<style scoped lang="scss">
.video-upload-box{
  >>>.el-form-item__content{
    text-align: left;
    .el-upload{
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
  }
}
>>>.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .el-input-number {
  width: 200px;
}

.box-card {
  margin-bottom: 18px;
}
.avatar-uploader {
  display: inline-block;
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
    object-fit: contain;
  }
}
>>>.video-upload{
  .el-upload {
    border: 1px dashed #d9d9d9;
    width: 160px;
    height: 90px;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    line-height: 80px;
  }
}
  
::v-deep .filter-list-box {
  .wrapper {
    display: inline;
    vertical-align: top;
  }
  .upload-images {
    overflow: hidden;
    background-color: #fff;
    border: 1px solid #c0ccda;
    border-radius: 6px;
    box-sizing: border-box;
    width: 100px;
    height: 100px;
    margin: 0 8px 8px 0;
    display: inline-block;
    position: relative;
    cursor: grabbing;
    .upload-image {
      width: 100%;
      height: 100%;
      display: -webkit-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      -webkit-justify-content: center;
      justify-content: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      -webkit-align-items: center;
      align-items: center;
      & > img {
        width: 100%;
      }
    }
    .upload-actions {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      background-color: rgba(0,0,0,0.5);
      text-align: center;
      display: none;
      i {
        margin-left: 6px;
        margin-top: 6px;
        &:first-child {
          margin-left: 0;
        }
      }
    }
    &:hover .upload-actions {
      display: block;
    }
    .upload-actions i {
      color: #fff;
      font-size: 18px;
      cursor: pointer;
    }
  }
}
.notice {
  margin: 10px 0 0 0;
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
}
</style>

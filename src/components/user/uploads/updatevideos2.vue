<template>
  <div class="video-upload-box">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="160px"
      :loading="btnLoading"
    >
 
      <div>
        <el-form-item label="编辑插件">
          <div>
            <table style="width: 100%">
              <tr v-for="(item, i) in form.detailList" :key="i">
                <td style="width: 50%">{{ item.version_code }}</td>
                <td style="width: 50%">
                  <i class="el-icon-edit" @click="detailListedit(i)"></i
                  >&nbsp;&nbsp;&nbsp;
                  <i class="el-icon-delete" @click="detailListedel(i)"></i>
                </td>
              </tr>
              <tr>
                <td></td>
                <td>
                  <i
                    class="el-icon-circle-plus-outline"
                    @click="addDetail()"
                  ></i>
                </td>
              </tr>
            </table>
          </div>
        </el-form-item>
      </div>
    </el-form>
    <div slot="footer" class="dialog-footer" style="text-align: center">
      <el-button type="primary" :loading="btnLoading" @click="onFormSubmit(2)">
        立即发布
      </el-button>
    </div>

    <div class="detail-dialog">
      <el-dialog
        title=""
        :modal-append-to-body="false"
        :visible.sync="detailDialogVisible"
        width="80%"
        :before-close="handleClose"
      >
        <div class="detial-dialog-box">
          <videodetail
            :detailList="form.detailList"
            :row="detaildata"
            :chapteroptions="chapteroptions"
            @fileDel="fileDel"
            @fileSub="fileSub"
            @listSub="listSub"
          />
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import CustomUpload from "./CustomUpload";
import VideosUpload from "./VideosUpload";
import EditTinymce from "./EditTinymce";
import videodetail from "./Videodetail";
import fileupload2 from "./Fileupload2";
import { getTagList } from "../../../utils/request";

export default {
  name: "updatevideos2",
  components: {
    CustomUpload,
    VideosUpload,
    EditTinymce,
    videodetail,
    fileupload2,
  },
  props: {
    formrow: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      tagList: [],
      chapteroptions: [],
      detaildata: {
        id: "",
        version_code: "",
        platform: "",
        description: "",
        type: "",
        link: "",
        index: "",
        fileList: [],
      },
      options: [],
      progress: 0,
      visible: false,
      btnLoading: false,
      editTinymceVisible: false,
      imageViewer: false,
      imageViewerList: [],
      domin: "",
      currentName: "",
      // 作者
      authorOptions: [],
      cateOptions: [],
      form: {
        name: "",
        author: "",
        intro: "",
        explain: "",
        external_link: "",
        images: "",
        type: "",
        web_link: "",
        sz_course_id: "",
        is_publish: 0,
        is_integral: 0,
        integral_num: 0,
        give_integral: 0,
        price: 0,
        send: 0,
        market_price: 0,
        videos: "",
        detailList: [],
        intros: [],
        tag:[],
      },
      rules: {
      
        push: [
          {
            required: true,
            message: "请勾选协议",
            trigger: ["blur", "change"],
          },
        ],
       
        videos: [
          {
            required: true,
            message: "请上传视频",
            trigger: ["blur", "change"],
          },
        ],
      },
      detailDialogVisible: false,
    };
  },

  watch: {
    formrow: {
      handler() {
      },
    },
  },
  created() {
    var oss = JSON.parse(localStorage.getItem("oss"));
    this.domin = oss.DoMain;
  }, 
  mounted() {},
  methods: {
    init(data) {
      console.log(data);
      this.form = data;
    },

    showFileBos() {
      this.detailDialogVisible = true;
      this.chapteroptions = [];
      for (let i = 0; i < this.form.detailList.length; i++) {
        if (this.form.detailList[i].type == "chapter") {
          this.chapteroptions.push(this.form.detailList[i]);
        }
      }
    },
    fileDel(file, fileList) {
      this.detaildata.fileList = [...fileList];
    },
    fileSub(response, file) {
      console.log(response, file);
      if (response && response.name) {
        this.detaildata.fileList.push({
          name: response.name,
          url: this.domin + response.name,
        });
      }
    },
    listSub(row) {
      if (row.index != "" && row.index != null) {
        this.form.detailList[row.index - 1] = row;
      } else {
        row.index = this.form.detailList.length + 1;
        this.form.detailList.push(row);
        console.log(this.form.detailList);
      }
      this.detaildata = {};
      this.detailDialogVisible = false;
      console.log(this.form.detailList, row.index, "============");
    },
    handleClose() {
      this.detailDialogVisible = false;
    },

    detailListedit(i) {
      this.detaildata = this.form.detailList[i];
      this.showFileBos();
    },
    detailListedel(i) {
      this.form.detailList.splice(i, 1);
    },
    addDetail() {
      this.detaildata = {
        id: "",
        version_code: "",
        platform: "",
        description: "",
        type: "",
        link: "",
        index: "",
        fileList: [],
      };
      this.showFileBos();
    },
    onFormSubmit(i) {
      this.form.status = i;
      console.log(this.form);
      this.$refs.form.validate((valid) => {
        if (valid) {

          if (this.form.id) {
            this.$confirm(`确定对[(#${this.form.id})]进行修改?`, "编辑", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(() => {
                this.hanldaddOrUpdate();
              })
              .catch(() => {});
          } else {
            this.hanldaddOrUpdate();
          }
        } else {
          this.$message.error("请填写完整信息");
        }
      });
    },

    hanldaddOrUpdate() {
      if (this.btnLoading) return;
      this.form.reload = 1;
      const data = JSON.parse(JSON.stringify(this.form));
      this.btnLoading = true;
      this.$axios
        .post(this.url + "/api/userput/plug", data)
        .then((res) => {
          this.btnLoading = false;
          if (res.data.code == 200) {
            this.$emit("handsubmit2", false);
            this.$message.success("提交成功");
            // this.$router.go(0);
            this.$refs.form.resetFields();
          } else {
            this.$message.error(res.data.msg);
          }
        })
        .catch((err) => {});
    },


  },
};
</script>

<style scoped lang="scss">
.video-upload-box {
  min-height: 100%;
  height: 400px;
  overflow: auto;
  >>> .el-form-item__content {
    text-align: left;
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
  }
}
>>> .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.el-input-number {
  width: 200px;
}

.box-card {
  margin-bottom: 18px;
}
.avatar-uploader {
  display: inline-block;
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
    object-fit: contain;
  }
}
>>> .video-upload {
  .el-upload {
    border: 1px dashed #d9d9d9;
    width: 160px;
    height: 90px;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    line-height: 80px;
  }
}

::v-deep .filter-list-box {
  .wrapper {
    display: inline;
    vertical-align: top;
  }
  .upload-images {
    overflow: hidden;
    background-color: #fff;
    border: 1px solid #c0ccda;
    border-radius: 6px;
    box-sizing: border-box;
    width: 100px;
    height: 100px;
    margin: 0 8px 8px 0;
    display: inline-block;
    position: relative;
    cursor: grabbing;
    .upload-image {
      width: 100%;
      height: 100%;
      display: -webkit-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      -webkit-justify-content: center;
      justify-content: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      -webkit-align-items: center;
      align-items: center;
      & > img {
        width: 100%;
      }
    }
    .upload-actions {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      background-color: rgba(0, 0, 0, 0.5);
      text-align: center;
      display: none;
      i {
        margin-left: 6px;
        margin-top: 6px;
        &:first-child {
          margin-left: 0;
        }
      }
    }
    &:hover .upload-actions {
      display: block;
    }
    .upload-actions i {
      color: #fff;
      font-size: 18px;
      cursor: pointer;
    }
  }
}
.notice {
  margin: 10px 0 0 0;
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
}
</style>

<template>
  <div class="user-course">
    <!-- 我的课程 -->
    <div class="user-head">
      <!-- 标题 -->
      <div class="title">
        <div class="icon"></div>
        <div class="text">我的投稿</div>
      </div>
    </div>
    <div class="main">
      <el-tabs
        v-model="activeName2"
        class="user-tabs"
        @tab-click="handleClick2"
      >
        <!-- 我的学习 -->
        <el-tab-pane label="我的学习" name="learn"> </el-tab-pane>
        <!-- 我的收藏 -->
        <el-tab-pane label="我的收藏" name="collect" class="tab-pane2">
        </el-tab-pane>
        <el-tab-pane label="我的上传" name="upload"> </el-tab-pane>

        <el-tab-pane label="投稿管理" name="putlist"> </el-tab-pane>
      </el-tabs>
      <el-tabs v-model="activeName" class="user-tabs" @tab-click="handleClick">
        <!-- 我的学习 -->
        <el-tab-pane label="视频投稿" name="course"> </el-tab-pane>
        <!-- 我的收藏 -->
        <el-tab-pane label="插件投稿" name="plug"> </el-tab-pane>
        <!-- 我的收藏 -->
        <el-tab-pane label="素材投稿" name="goods"> </el-tab-pane>
      </el-tabs>
      <div class="tab-box-items">
        <div>
          <el-button
            class="button-new-tag"
            :class="status == null ? 'action' : ''"
            size="small"
            @click="gaojian()"
            >全部稿件</el-button
          >
          <el-button
            class="button-new-tag"
            :class="status == 0 ? 'action' : ''"
            size="small"
            @click="gaojian(0)"
            >草稿</el-button
          >
        </div>
        <div class="">
          <el-input
            placeholder="搜索稿件"
            prefix-icon="el-icon-search"
            v-model="name"
            style="width: 305px"
          >
            <i
              slot="append"
              class="el-icon-search"
              @click="getData(activeName)"
            ></i>
          </el-input>
        </div>
      </div>
      <div class="tab-box-items">
        <div>
          <el-button
            class="button-new-tag"
            :class="isCheck == 1 ? 'action' : ''"
            size="small"
            @click="checked(1)"
            >进行中</el-button
          >
          <el-button
            class="button-new-tag"
            :class="isCheck == 2 ? 'action' : ''"
            size="small"
            @click="checked(2)"
            >已通过</el-button
          >
          <el-button
            class="button-new-tag"
            :class="isCheck == 3 ? 'action' : ''"
            size="small"
            @click="checked(3)"
            >未通过</el-button
          >
        </div>
        <div>
          <el-select
            v-model="cateId"
            placeholder="请选择"
            style="width: 150px"
            @change="catechange()"
          >
            <el-option-group
              v-for="group in options"
              :key="group.label"
              :label="group.label"
            >
              <el-option
                v-for="item in group.options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-option-group>
          </el-select>
          <el-select v-model="sort" placeholder="请选择" style="width: 150px">
            <el-option
              v-for="item in options2"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <br />
      <br />
      <div class="list" v-loading="loading">
        <div class="list-item" v-for="(item, index) in items" :key="index">
          <div class="xg-image image">
            <img
              v-if="item.cover"
              :src="domain + item.cover"
              fit="cover"
              alt=""
            />
            <img v-else :src="domain + item.images" fit="cover" alt="" />
          </div>
          <div class="info">
            <el-row>
              <el-col :span="24"
                ><div class="title" style="text-align: left">
                  {{ item.name }}
                </div></el-col
              >
            </el-row>
            <el-row>
              <el-col :span="24"
                ><div class="price" style="text-align: left">
                  金额(光子): {{ item.price }}
                </div></el-col
              >
            </el-row>
            <el-row>
              <el-col :span="16">
                <div class="tougao" style="text-align: left">
                  投稿时间: {{ item.created_at }}
                </div>
                <div
                  class="tougao"
                  v-if="
                    (item.is_check == 1 || !item.is_check) && item.status == 0
                  "
                  style="text-align: left; color: rgb(239 212 57)"
                >
                  未发布
                </div>
                <div
                  class="tougao"
                  v-if="
                    (item.is_check == 1 || !item.is_check) && item.status != 0
                  "
                  style="text-align: left"
                >
                  审核中
                </div>
                <div
                  class="tougao"
                  v-if="item.is_check == 2 && item.status != 0"
                  style="text-align: left; color: rgb(96 239 57)"
                >
                  已通过
                </div>
                <div
                  class="tougao"
                  v-if="item.is_check == 3 && item.status != 0"
                  style="text-align: left; color: red"
                >
                  未通过 : {{ item.errors }}
                </div>
              </el-col>
              <el-col :span="8">
                <div class="" style="text-align: right">
                  <el-button
                    v-if="item.is_check != 2"
                    icon="el-icon-edit"
                    size="mini"
                    @click="editGaojian(item)"
                    >编辑</el-button
                  >
                  <el-button
                    v-if="activeName == 'plug' && item.is_check == 2"
                    icon="el-icon-edit"
                    size="mini"
                    @click="editGaojian2(item)"
                    >编辑</el-button
                  >
                  <el-button
                    v-if="item.is_check != 2"
                    icon="el-icon-delete"
                    size="mini"
                    @click="delGaojian(item)"
                    >删除</el-button
                  >
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
      <!-- 分页 -->
      <el-pagination
        background
        class="xg-pagination"
        layout="prev, pager, next,jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="page"
        :page-size="pagesize"
        :total="total"
      >
      </el-pagination>

      <div class="detail-dialog">
        <el-dialog
          title=""
          :modal-append-to-body="false"
          :visible.sync="detailDialogVisible"
          width="80%"
          :before-close="handleClose"
        >
          <div class="detial-dialog-box">
            <updatecourse
              ref="updatecourse"
              v-if="activeName == 'course' && detailDialogVisible"
              :user="user"
              :formrow="row"
              :options="options"
              @handsubmit="handsubmit"
            />
            <updatevideos
              ref="updatevideos"
              v-if="activeName == 'plug' && detailDialogVisible"
              :user="user"
              :formrow="row"
              :options="options"
              @handsubmit="handsubmit"
            />
            <updategoods
              ref="updategoods"
              v-if="activeName == 'goods' && detailDialogVisible"
              :user="user"
              :formrow="row"
              :options="options"
              @handsubmit="handsubmit"
            />
          </div>
        </el-dialog>
      </div>

      <div class="detail-dialog">
        <el-dialog
          title=""
          :modal-append-to-body="false"
          :visible.sync="detailDialogVisible2"
          width="80%"
          :before-close="handleClose2"
        >
          <div class="detial-dialog-box">
            <updatevideos2
              ref="updatevideos2"
              v-if="activeName == 'plug' && detailDialogVisible2"
              :user="user"
              :formrow="row"
              :options="options"
              @handsubmit2="handsubmit2"
            />
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import updategoods from "./uploads/updategoods";
import updatevideos2 from "./uploads/updatevideos2";
import updatevideos from "./uploads/updatevideos";
import updatecourse from "./uploads/updatecourse";

export default {
  components: { updatevideos, updategoods, updatecourse, updatevideos2 },
  data() {
    return {
      activeName2: "putlist",
      row: {},
      detailDialogVisible2:false,
      detailDialogVisible: false,
      loading: false,
      pagesize: 10,
      status: null,
      isCheck: null,
      name: "",
      cateId: null,
      sort: "time",
      page: 1, //投稿
      total: 0,
      user: {},
      activeName: "course",
      items: [],
      options: [],
      options2: [{ value: "time", label: "按投稿时间排序" }],
      domain: "",
      footerhtml:
        '<div data-v-4db96594="" class="footer-item"><span data-v-4db96594="" class="i-list i-1"><a data-v-4db96594="" target="_blank" class="title">禁止发布的视频内容</a></span><span data-v-4db96594="" class="i-list i-1"><span data-v-4db96594="" class="title">视频大小</span><span data-v-4db96594="" class="title-block"><span data-v-4db96594="">网页端上传的文件大小上限为8G</span><br data-v-4db96594=""><span data-v-4db96594="">视频内容时长最大10小时</span><br data-v-4db96594=""><span data-v-4db96594="">（提升电磁力，即可体验32G超大文件上限哦！前往创作实验室了解更多）</span><br data-v-4db96594=""></span></span><span data-v-4db96594="" class="i-list i-1"><span data-v-4db96594="" class="title">视频格式</span><span data-v-4db96594="" class="title-block"><span data-v-4db96594="">网页端、桌面客户端推荐上传的格式为：mp4,flv</span><br data-v-4db96594=""><span data-v-4db96594="">（推荐上传的格式在转码过程更有优势，审核过程更快哦！）</span><br data-v-4db96594=""><span data-v-4db96594="">其他允许上传的格式：mp4,flv,avi,wmv,mov,webm,mpeg4,ts,mpg,rm,rmvb,mkv,m4v</span><br data-v-4db96594=""></span></span><span data-v-4db96594="" class="i-list i-1"><span data-v-4db96594="" class="title">视频码率</span><span data-v-4db96594="" class="title-block"><span data-v-4db96594="">分辨率最大支持 8192*4320</span><br data-v-4db96594=""><span data-v-4db96594="">推荐视频分辨率：1920*1080 或者 3840*2160</span><br data-v-4db96594=""><span data-v-4db96594="">推荐视频码率：1080p大于6000kbps；4k大于20000kbps；8k大于40000kbps</span><br data-v-4db96594=""><span data-v-4db96594="">音频采样率48000Hz</span><br data-v-4db96594=""><span data-v-4db96594="">推荐音频码率：320kbps</span><br data-v-4db96594=""><span data-v-4db96594="">逐行扫描</span><br data-v-4db96594=""><span data-v-4db96594="">智能识别输出HDR</span><br data-v-4db96594=""><span data-v-4db96594="">智能识别全景视频</span><br data-v-4db96594=""></span></span></div>',
    };
  },
  beforeMount() {
    this.getOss();
    this.getData(this.activeName);
    this.getUserInfo();
    this.getUserCate(this.activeName);
  },
  methods: {
    handsubmit(status) {
      this.detailDialogVisible = status;
      this.getData(this.activeName);
    },
    handsubmit2(status) {
      this.detailDialogVisible2 = status;
      this.getData(this.activeName);
    },
    handleClose() {
      this.detailDialogVisible = false;
    },
    handleClose2() {
      this.detailDialogVisible2 = false;
    },
    editGaojian2(item) {
      this.detailDialogVisible2 = true;
      this.row = {};
      if (item.detail){
        item.detailList = JSON.parse(item.detail)
      } else {
        item.detailList = [];
      }
     
      this.$nextTick(() => {
        console.log(this.activeName, "this.activeName---------");
        this.$refs.updatevideos2 && this.$refs.updatevideos2.init(item);
      });
    },
    editGaojian(item) {
      this.detailDialogVisible = true;
      this.row = {};
      if (item.detail) item.detailList = JSON.parse(item.detail);
      if (item.intros) {
        item.intros = JSON.parse(item.intros);
      }
      if (this.activeName == "goods") {
        if (item.material) {
          item.fileList = [
            { name: item.material, url: this.domain + item.material },
          ];
        } else {
          item.fileList = [];
        }

        console.log(item.images);
        var images = [];
        var imageArr = [];
        if (typeof item.images == "string") {
          images = JSON.parse(item.images);
          for (let i = 0; i < images.length; i++) {
            imageArr.push({ name: images[i], url: this.domain + images[i] });
          }
          item.images = imageArr;
        }
      }

      // this.row = item;

      this.$nextTick(() => {
        console.log(this.activeName, "this.activeName---------");
        if (this.activeName == "course") {
          this.$refs.updatecourse && this.$refs.updatecourse.init(item);
        } else if (this.activeName == "plug") {
          this.$refs.updatevideos && this.$refs.updatevideos.init(item);
        } else if (this.activeName == "goods") {
          this.$refs.updategoods && this.$refs.updategoods.init(item);
        }
      });
    },
    delGaojian(item) {
      this.loading = true;
      var _this = this;
      this.$confirm(`确定进行[删除]操作?`, "删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
        cancelButtonClass: "btn-custom-cancel",
      })
        .then(() => {
          this.$axios
            .get(
              this.url +
                "/api/delUserPut/" +
                this.activeName +
                "?id=" +
                item.id,
              {}
            )
            .then((res) => {
              console.log(res, "res---------------------");
              _this.loading = false;
              if (res.data.code == 200) {
                this.getData(this.activeName);
              } else {
              }
            })
            .catch((err) => {});
        })
        .catch(() => {});
    },
    catechange() {
      console.log(this.cateId);
      this.page = 1;
      this.items = [];
      this.getData(this.activeName);
    },
    getUserCate(type) {
      this.loading = true;
      var _this = this;
      this.$axios
        .get(this.url + "/api/user/getUserCate?catetype=" + type, {})
        .then((res) => {
          console.log(res, "res---------------------");
          _this.loading = false;
          if (res.data.code == 200) {
            this.options = [];
            var obj = {};
            for (let i = 0; i < res.data.data.length; i++) {
              if (res.data.data[i].parent_id == 0) {
                obj[res.data.data[i].id] = {
                  label: res.data.data[i].name,
                  options: [],
                };
              } else {
                obj[res.data.data[i].parent_id].options.push({
                  value: res.data.data[i].id,
                  label: res.data.data[i].name,
                });
              }
            }

            this.options = obj;
          } else {
          }
        })
        .catch((err) => {});
    },
    gaojian(i = null) {
      this.page = 1;
      this.status = i;
      this.items = [];
      this.getData(this.activeName);
    },
    checked(i) {
      this.page = 1;
      this.isCheck = i;
      this.items = [];
      this.getData(this.activeName);
    },
    getUserInfo() {
      this.$axios
        .get(this.url + "/api/user", {})
        .then((res) => {
          if (res.data.code != 200) {
            localStorage.removeItem("UserInfo");
            localStorage.removeItem("Authorization");
            this.$router.push({ name: "login" });
          } else {
            this.user = res.data.data;
          }
        })
        .catch((err) => {});
    },
    getOss() {
      this.$axios
        .get(this.url + "/api/oss", {})
        .then((res) => {
          console.log(res);
          if (res.data.code == 200) {
            this.domain = res.data.data.oss.DoMain;
            localStorage.setItem("oss", JSON.stringify(res.data.data.oss));
          }
        })
        .catch((err) => {});
    },

    handleSizeChange(val) {
      this.pagesize = val;
    },
    handleCurrentChange(val) {
      this.page = val;
      this.getData(this.activeName);
    },
    getData(type = "course") {
      this.items = [];
      let user = JSON.parse(localStorage.getItem("UserInfo"));
      if (user && Object.keys(user).length !== 0) {
        let data = {
          status: this.status,
          isCheck: this.isCheck,
          name: this.name,
          cateId: this.cateId,
          sort: this.sort,
          page: this.page,
          limit: this.pagesize,
          type: type,
        };

        this.$axios
          .get(this.url + "/api/getUserPut/" + type, {
            params: data,
          })
          .then((res) => {
            if (res.data.code === 200) {
              this.items = res.data.data.data;
              this.total = res.data.data.total;
            } else if (res.data.code === 4001) {
              localStorage.removeItem("UserInfo");
              localStorage.removeItem("Authorization");
              this.$router.push({ name: "login" });
            }
          })
          .catch((err) => {});
      }
    },

    //  跳转
    jump(id) {
      this.$router.push({ name: "coursepage", params: { id: id } });
    },

    // 取消收藏
    cancelCollect(userCourseId) {
      if (userCourseId) {
        this.$axios
          .post(this.url + "/api/courses/collect/cancel", {
            user_course_id: userCourseId,
          })
          .then((res) => {
            if (res.data.code === 200) {
              this.$message.success({
                showClose: true,
                message: res.data.msg,
              });

              let newCollectData = [];
              this.items.forEach((item, index) => {
                if (item.id !== userCourseId) {
                  newCollectData.push(item);
                }
              });
              this.$nextTick(() => {
                this.items = newCollectData;
              });
            } else if (res.data.code === 4001) {
              localStorage.removeItem("UserInfo");
              localStorage.removeItem("Authorization");
              this.$router.push({ name: "login" });
            } else {
              this.$message.warning({
                showClose: true,
                message: res.data.msg,
              });
            }
          })
          .catch((err) => {});
      } else {
        this.$message.warning({
          showClose: true,
          message: "取消好像出了点小问题，先缓一缓吧",
        });
        return false;
      }
    },
    handleClick2() {
      if (this.activeName2 != "putlist") {
        // http://localhost:8081/#/user/course
        this.$router.push("course?type=" + this.activeName2);
      }
      console.log(this.activeName2);
    },
    handleClick() {
      this.page = 1;
      this.items = [];
      this.cateId = null;
      this.status = null;
      this.isCheck = null;
      this.name = "";
      this.getUserCate(this.activeName);
      this.getData(this.activeName);
    },
  },
};
</script>

<style lang="scss" scoped>
>>> .el-dialog__body {
  max-height: 100%;
  overflow: auto;
}
.tab-box-items {
  text-align: left;
  display: flex;
  justify-content: space-between;
  >>> .el-input-group__append {
    padding: 0;
    .el-icon-search {
      padding: 8px 10px;
    }
  }
  >>> .button-new-tag {
    border: 0;
    width: 50px !important;
    display: inline-block !important;
    color: #222222;
    text-align: left !important;
    background-color: #fff !important;

    span {
      color: #222222;
    }
    &.action {
      color: #00a1d6;
      span {
        color: #00a1d6;
      }
    }
  }
}
.disable-upload {
  color: #999999;
}
>>> .xg-image {
  img {
    max-height: 150px;
    // max-width: 150px;
    width: auto;
  }
}
.video-entrance * {
  box-sizing: border-box;
}
>>> .disable-upload-wrp {
  margin-top: 10px;
  position: relative;
  border: 2px dashed #ccc;
  padding: 60px 0;
  text-align: center;
  color: #999;
  font-size: 14px;
  text-align: center;
}
>>> .upload-body {
  padding: 8px 0 16px;
  height: calc(100% - 64px);
  overflow: auto;
  > div {
    max-width: 830px;
    margin: 0 auto;
    .upload-wrp {
      margin-top: 10px;
      position: relative;
      border: 2px dashed #ccc;
      .upload {
        margin-top: 62px;
        margin-bottom: 62px;
        display: flex;
        justify-content: center;
        position: relative;
        color: #999;
        font-size: 14px;
        text-align: center;
      }
    }
    .banner-entry {
      margin-top: 20px;
      height: 88px;
      background-color: rgba(0, 161, 214, 0.05);
      position: relative;
      display: flex;
      align-items: center;
      .banner-entry-icon {
        color: #00a1d6;
        width: 50px;
        height: 50px;
        margin: 0 15px 0 20px;
      }
      .banner-entry-text {
        flex: 1;
        text-align: left;
        .main-text {
          font-size: 16px;
          padding-bottom: 4px;
          color: #212121;
          font-weight: 700;
        }
        .sub-text {
          padding-top: 4px;
          font-size: 12px;
          color: #505050;
          position: relative;
          cursor: pointer;
          span {
            vertical-align: middle;
          }
        }
      }
      .banner-entry-right {
        width: 220px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding-right: 40px;
        z-index: 2;
        .enter-btn {
          display: inline-block;
          width: 90px;
          height: 30px;
          line-height: 30px;
          border: 1px solid #00aeec;
          background-color: #fff;
          color: #00aeec;
          border-radius: 4px;
          cursor: pointer;
          font-size: 13px;
          text-align: center;
          margin-left: 5px;
        }
      }
      .banner-entry-icon-wrapper {
        height: 83px;
        width: 83px;
        position: absolute;
        bottom: 0;
        right: 0;
      }
    }
  }
}

>>> .footer-item {
  .list {
    a,
    .item {
      color: #00a1d6;
      font-size: 12px;
    }
  }
}
.upload-box {
  >>> .el-tabs__nav-scroll {
    background-color: #fff;
  }
  >>> .upload-home {
  }
}

.user-course {
  padding-bottom: 50px;
  .user-head {
    .icon {
      background-position: -35px -49px;
    }
  }
  .main {
    padding: 25px;
  }
  /deep/ .user-tabs .el-tabs__header {
    margin-bottom: 27px;
  }
  .user-tabs {
  }
  .list {
  }
  .list-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;
    &:last-child {
      margin-bottom: 0;
    }
    .image {
      width: 245px;
      border-radius: 5px;
      overflow: hidden;
      flex-shrink: 0;
      margin-right: 20px;
    }
    .info {
      min-width: 0;
      flex-grow: 1;
    }
    .title {
      font-size: 16px;
      color: #000;
      line-height: 1.6;
      height: 3.2em;
      margin-bottom: 15px;
    }
    .price,
    .tougao {
      font-size: 12px;
      color: #bbbbbb;
      line-height: 1.6;
    }
    .progress-rate {
    }
    .text {
      justify-content: space-between;
      width: 100%;
      margin-bottom: 10px;
      font-size: 14px;
      line-height: 1.2;
      .text1 {
        color: #687d93;
      }
      .text2 {
        color: #67c23a;
      }
    }
  }
  .xg-progress {
    margin-bottom: 15px;
  }
  .btn-group {
    display: flex;
    align-items: center;
  }
  .xg-pagination {
    margin-top: 64px;
  }
  // 我的收藏
  .tab-pane2 {
    .list-item {
      align-items: stretch;
      padding-bottom: 25px;
      margin-bottom: 25px;
      border-bottom: 1px dashed #d3d3d3;
      &:last-child {
        margin-bottom: 0;
      }
    }
    .info {
      display: flex;
      flex-flow: column;
      align-items: flex-start;
      justify-content: space-between;
    }
    .title {
    }
    .el-pagination {
      margin-top: 40px;
    }
  }
}

@media screen and (max-width: 751px) {
  .user-course {
    padding: 15px 20px;
    .main {
      padding: 0;
    }
    .list-item {
      flex-wrap: wrap;
      .image {
        width: 100%;
        margin-right: 0;
        margin-bottom: 15px;
      }
      .info {
        width: 100%;
      }
      .title {
        font-size: 14px;
        margin-bottom: 10px;
      }
      .text {
        font-size: 12px;
      }
    }
    .xg-progress {
      margin-bottom: 10px;
    }
    .el-button {
      width: 48%;
      margin-left: 0;
      &.el-button--primary {
        margin-right: 4%;
      }
    }
    .xg-pagination {
      margin-top: 30px;
    }
  }
}
</style>

<template>
  <div class="information">
    <!-- 欢迎 -->
    <div class="welcome">
      <div class="text">欢迎回来，{{ user.name }}</div>
      <!--      <span>今日登录 +5 光子</span>-->
    </div>
    <div class="main">
      <div class="head">
        <!-- 头像 -->
        <div class="avatar-box">
          <el-upload
            class="avatar-uploader"
            :action="url + '/api/upload'"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="user.avatar" :src="user.avatar" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
          <div class="name">上传</div>
        </div>
        <!--  -->
        <div>
          <div class="info">
            <!-- 光子 -->
            <div class="item item1">
              <div class="number">{{ user.integral }}</div>
              <div class="text">光子</div>
            </div>
            <!-- 待发货 -->
            <!--          <div class="item item2">-->
            <!--            <div class="number">{{ user.wait_delivery }}</div>-->
            <!--            <div class="text">待发货</div>-->
            <!--          </div>-->
            <!-- 未读消息 -->
            <div class="item item3">
              <div class="number">{{ user.wait_read }}</div>
              <div class="text">未读消息</div>
              <div>　</div>
            </div>
          </div>
          <div class="fans-box">
            <div class="item item1">
              <span class="cur" @click="go(1)"
                ><span class="text">关注的人:</span
                ><span class="text">{{ attentionNum }}</span></span
              >
              <span class="text">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span
              >
              <span class="cur" @click="go(2)"
                ><span class="text">粉丝</span
                ><span class="text">{{ fansNum }}</span></span
              >
            </div>
          </div>
        </div>
      </div>

      <!-- 基本信息 -->
      <div class="basic-information">
        <div class="title">基本信息</div>
        <div class="tip">
          基本资料请以真实信息为准，以方便我们更好为您服务。
        </div>
        <el-form
          ref="form"
          :rules="rules"
          :model="user"
          label-width="100px"
          class="xg-form"
        >
          <el-form-item label="昵称" prop="name">
            <el-input
              placeholder="请输入昵称"
              v-model="user.name"
              @blur="update('name', $event)"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="性别" prop="sex">
            <el-select
              v-model="user.sex"
              value-key="key"
              placeholder="请选择性别"
              @change="update('sex', $event)"
            >
              <el-option
                v-for="item in sexOptions"
                :key="item.key"
                :label="item.desc"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="签名" prop="sign">
            <el-input
              placeholder="请输入签名"
              v-model="user.sign"
              @blur="update('sign', $event)"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item label="" prop="intro">
            <span slot="label">个人简介<br />此个性签名将同步到主页 </span>
            <el-input
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
              placeholder="请输入简介"
              @blur="update('intro', $event)"
              class="intro"
              v-model="user.intro"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="职业" prop="occupation">
            <el-input
              placeholder="请输入职业"
              v-model="user.occupation"
              @blur="update('occupation', $event)"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item label="微信号" prop="wechat">
            <el-input
              placeholder="请输入微信号"
              v-model="user.wechat"
              @blur="update('wechat', $event)"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item label="QQ号" prop="qq">
            <el-input
              placeholder="请输入QQ"
              v-model="user.qq"
              @blur="update('qq', $event)"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="submit2()">提交</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { mapMutations } from "vuex";

export default {
  name: "info",
  data() {
    return {
      user: {},
      value: 0,
      fansNum: 0,
      attentionNum: 0,

      sexOptions: [
        { key: "0", desc: "性别" },
        { key: "1", desc: "女" },
        { key: "2", desc: "男" },
      ],

      // 表单验证
      rules: {
        name: [
          { required: true, message: "请输入昵称", trigger: "blur" },
          { min: 1, max: 8, message: "长度在 3 到 5 个字符", trigger: "blur" },
        ],
        sex: [{ required: true, message: "请选择性别", trigger: "change" }],
        sign: [{ required: true, message: "请输入签名", trigger: "blur" }],
        intro: [{ required: true, message: "请输入个人简介", trigger: "blur" }],
        occupation: [
          { required: true, message: "请输入职业", trigger: "blur" },
        ],
        wechat: [{ required: true, message: "请输入微信号", trigger: "blur" }],
        qq: [{ required: true, message: "请输入QQ号", trigger: "blur" }],
      },
    };
  },

  mounted() {
    this.getUserInfo();
  },
  methods: {
    ...mapMutations(["changeLogin"]),
    go(type) {
      //关注列表
      if (type == 1) {
        this.$router.push('/attentionlist/'+this.user.id);
      } else if (type == 2) {
        this.$router.push('/userfanslist/'+this.user.id);
        //粉丝列表
      }
    },
    getUserFansInfo() {
      this.$axios
        .get(this.url + "/api/getUserFansInfo/" + this.user.id, {})
        .then((res) => {
          console.log(res, "res--------");
          if (res.data.code !== 200) {
          } else {
            this.isfans = res.data.data.isfans;
            this.fansNum = res.data.data.fansNum;
            this.attentionNum = res.data.data.attentionNum;
            this.isfansShow = res.data.data.isfansShow;
          }
        })
        .catch((err) => {});
    },
    submit2() {
      // this.update();
      console.log(111111);
      this.$message.success({
        message: "提交成功",
      });
    },
    getUserInfo() {
      this.$axios
        .get(this.url + "/api/user", {})
        .then((res) => {
          if (res.data.code != 200) {
            localStorage.removeItem("UserInfo");
            localStorage.removeItem("Authorization");
            this.$router.push({ name: "login" });
          } else {
            this.user = res.data.data;

            this.getUserFansInfo();
            let option = {};
            let sex = this.user.sex;
            this.sexOptions.forEach(function (item) {
              if (item.key == sex) {
                option = item;
              }
            });
            this.user.sex = option;
          }
        })
        .catch((err) => {});
    },

    update(type, event) {
      let user = JSON.parse(localStorage.getItem("UserInfo"));
      let value = type === "sex" ? event.key : event.currentTarget.value;
      if (user[type] != "undefined" && user[type] != value) {
        this.$axios
          .post(this.url + "/api/user/update", {
            type: type,
            value: value,
          })
          .then((res) => {
            if (res.data.code !== 200) {
              this.$message.warning({
                showClose: true,
                message: res.data.msg,
              });
            } else {
              user[type] = value;
              localStorage.setItem("UserInfo", JSON.stringify(user));
            }
          })
          .catch((err) => {});
      }
    },

    handleAvatarSuccess(res, file) {
      let avatar = this.url + "/" + res.data.filename;
      let user = JSON.parse(localStorage.getItem("UserInfo"));
      this.user.avatar = avatar;
      if (res.data.filename) {
        this.$axios
          .post(this.url + "/api/user/update", {
            type: "avatar",
            value: avatar,
          })
          .then((res) => {
            if (res.data.code === 200) {
              user["avatar"] = avatar;
              localStorage.setItem("UserInfo", JSON.stringify(user));
            }
          })
          .catch((err) => {});
      }
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === "image/jpeg";
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        alert("上传头像图片只能是 JPG 格式!");
        // this.$message.error('上传头像图片只能是 JPG 格式!');
      }
      if (!isLt2M) {
        alert("上传头像图片大小不能超过 2MB!");
        // this.$message.error('上传头像图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },
  },
};
</script>

<style scoped lang="scss">
.cur {
  cursor: pointer;
}
.information {
  text-align: left;
  .welcome {
    background: #a3acb6;
    height: 46px;
    display: flex;
    align-items: center;
    padding-left: 20px;
    padding-right: 20px;
    .text {
      color: #fff;
      font-size: 13px;
      line-height: 1.2;
      margin-right: 14px;
    }
    span {
      background: #f8e715;
      font-size: 12px;
      color: #000;
      padding-left: 10px;
      padding-right: 10px;
      border-radius: 100px;
      font-weight: bold;
    }
  }
  .main {
    padding: 40px;
  }
  .head {
    padding-bottom: 40px;
    border-bottom: 1px solid #dbdddf;
    margin-bottom: 40px;
    display: flex;
    align-items: center;
    .avatar-box {
      position: relative;
      width: 124px;
      height: 124px;
      border-radius: 100%;
      overflow: hidden;
      .avatar-uploader {
        width: 100%;
        height: 100%;
        /deep/ .el-upload {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .name {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 30px;
        line-height: 30px;
        text-align: center;
        color: #fff;
        text-align: center;
        font-size: 12px;
        background: rgba(0, 0, 0, 0.7);
      }
    }
    .info {
      text-align: left;
      display: flex;
      align-items: center;
      .item {
        margin-left: 80px;
        &.item1 > .number {
          color: #0d6efd;
        }
      }
      .number {
        font-size: 21px;
        color: #000;
        font-weight: bold;
        line-height: 1.2;
        margin-bottom: 20px;
      }
      .text {
        font-size: 14px;
        color: #818e9d;
        line-height: 1.2;
      }
    }
    .fans-box {
      margin-top: 20px;
      padding-left: 80px;
      font-size: 14px;
      color: #818e9d;
    }
  }
  .basic-information {
    .title {
      font-size: 14px;
      color: #000;
      line-height: 1.2;
      margin-bottom: 20px;
      font-weight: bold;
    }
    .tip {
      background: #f4f4f4;
      height: 40px;
      line-height: 40px;
      padding-left: 20px;
      padding-right: 20px;
      font-size: 12px;
      color: #818e9d;
      margin-bottom: 40px;
      text-align: left;
    }
    .xg-form /deep/ {
      .el-input__inner {
        height: 44px;
        line-height: 44px;
        border-radius: 4px;
        padding: 0 15px;
      }
      .el-select {
        width: 100%;
      }
      .el-form-item__label {
        color: #818e9d;
      }
      .el-button {
        width: 120px;
        height: 46px;
        line-height: 46px;
        background-color: #f4f4f4;
        border-radius: 4px;
        text-align: center;
        font-size: 14px;
        color: #818e9d;
        border: 0;
        padding: 0;
        &:hover {
          background: #0d6efd;
          color: #fff;
        }
      }
      .intro textarea {
        height: 140px !important;
      }
    }
  }
}

@media screen and (max-width: 751px) {
  .information .welcome {
    padding: 8px 10px;
    font-size: 12px;
    height: auto;
    flex-wrap: wrap;
    .text {
      margin-right: 0;
      line-height: 1.6;
    }
    span {
      display: block;
      margin-top: 5px;
    }
  }
  .information .main {
    padding: 15px;
  }
  .information .head {
    flex-wrap: wrap;
    padding-bottom: 20px;
    margin-bottom: 20px;
  }
  .information .head .avatar-box {
    width: 120px;
    height: 120px;
    display: block;
    margin: 0 auto 20px;
  }
  .information .head .info {
    width: 100%;
  }
  .information .head .info .item {
    margin-left: 0;
    width: 32%;
  }
  .information .head .info .item:nth-child(3n-1) {
    margin-left: 2%;
    margin-right: 2%;
  }
  .information .head .info .number {
    font-size: 18px;
    margin-bottom: 5px;
  }
  .information .basic-information .tip {
    height: auto;
    line-height: 1.6;
    padding: 8px 10px;
    font-size: 12px;
    margin-bottom: 20px;
  }
  .information .xg-form /deep/ .el-form-item {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    margin-bottom: 5px;
    &:before,
    &:after {
      display: none;
    }
    .el-form-item__label {
      width: 100% !important;
      text-align: left;
      font-size: 12px;
    }
    .el-form-item__content {
      margin-left: 0 !important;
      width: 100%;
    }
    .el-input__inner {
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      padding-right: 10px;
    }
    .el-button {
      width: 100%;
      height: 36px;
      line-height: 36px;
      padding: 0;
      font-size: 12px;
      margin-top: 10px;
    }
  }
}
</style>

<template>
  <div class="securitysetting">

    <div class="user-head">
      <!-- 标题 -->
      <div class="title">
        <div class="icon"></div>
        <div class="text">安全设置</div>
      </div>
    </div>
    <!-- 设置列表 -->
    <div class="itembox">
      <div class="item">
        <div class="icon"><img src="../../assets/img/img31.png"></div>
        <div class="text1">修改密码</div>
        <div class="text2">经常更改密码有助于保护您的帐号安全</div>
        <div class="text3" :class="[this.user.is_pass?'success':'danger']">
          {{this.user.is_pass?'已设置':'未设置'}}
        </div>
        <el-button :class="{'primary':!this.user.is_pass}" @click="dialogpassword = true">修改</el-button>
      </div>
      <div class="item">
        <div class="icon"><img src="../../assets/img/img32.png"></div>
        <div class="text1">安全问题</div>
        <div class="text2">设置安全问题，保护帐号密码安全，也可用于找回密码</div>
        <div class="text3" :class="[this.user.is_problem?'success':'danger']">
          {{this.user.is_problem?'已设置':'未设置'}}
        </div>
        <el-button class="primary" @click="dialogproblem = true" v-if="!this.user.is_problem">设置</el-button>
        <el-button v-else>设置</el-button>
      </div>
      <div class="item">
        <div class="icon"><img src="../../assets/img/img33.png"></div>
        <div class="text1">手机绑定</div>
        <div class="text2">已绑定手机：{{ this.user.phone }}</div>
        <div class="text3" :class="[this.user.phone?'success':'danger']">
          {{this.user.phone?'已设置':'未设置'}}
        </div>
        <el-button :class="{'primary':!this.user.phone}" @click="dialogphone = true">修改</el-button>
      </div>
      <div class="item">
        <div class="icon"><img src="../../assets/img/img34.png"></div>
        <div class="text1">邮箱设置</div>
        <div class="text2">可用于找回登录密码</div>
        <div class="text3" :class="[this.user.email?'success':'danger']">
          {{this.user.email?'已设置':'未设置'}}
        </div>
        <el-button :class="{'primary':!this.user.email}" @click="dialogFormVisible = true">绑定</el-button>
      </div>
    </div>


    <!--登录密码-弹窗-->
    <el-dialog title="密码修改" :visible.sync="dialogpassword" class="dialog01">
      <el-form ref="ruleForm" :model="ruleForm" status-icon :rules="rules" class="demo-ruleForm">
        <el-form-item prop="oldpassword" label="当前密码">
          <el-input type="password" v-model="ruleForm.oldpassword" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item prop="pass" label="新密码">
          <el-input type="password" v-model="ruleForm.pass" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item prop="checkPass" label="确认密码">
          <el-input type="password" v-model="ruleForm.checkPass" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button type="primary" @click="passwordChange">确 定</el-button>
        <el-button @click="dialogpassword = false">取 消</el-button>
      </div>
    </el-dialog>

    <!--设置安全问题-弹窗-->
    <el-dialog :model="securityFrom" title="安全问题" :visible.sync="dialogproblem" class="dialog02">
      <div class="dialog-item">
        <label class="dialog-label" for="answer1">父母名字</label>
        <el-input type="text" autocomplete="off" placeholder="您的父母名字" v-model="securityFrom.answer1" id="answer1"></el-input>
      </div>
      <div class="dialog-item">
        <label class="dialog-label" for="answer2">老师名字</label>
        <el-input type="text" autocomplete="off" placeholder="您老师的名字" v-model="securityFrom.answer2" id="answer2"></el-input>
      </div>
      <div class="dialog-item">
        <label class="dialog-label" for="answer3">爱人名字</label>
        <el-input type="text" autocomplete="off" placeholder="您爱人的名字" v-model="securityFrom.answer3" id="answer3"></el-input>
      </div>
      <div class="dialog-footer">
        <div class="text">
          安全问题一经设置，不可再次修改。
        </div>
        <el-button type="primary" @click="security">确认</el-button>
        <el-button @click="dialogproblem = false">取消</el-button>
      </div>
    </el-dialog>

    <!--手机绑定-弹窗-->
    <el-dialog title="手机绑定" :visible.sync="dialogphone" class="dialog03">
      <el-form ref="bindmobileFrom" :model="bindmobileFrom" status-icon :rules="rules">
        <el-form-item prop="password" label="登录密码">
          <el-input type="password" v-model="bindmobileFrom.password" autocomplete="off" placeholder="输入登录密码"></el-input>
        </el-form-item>
        <el-form-item prop="tel" label="手机号码">
          <el-input v-model="bindmobileFrom.tel" auto-complete="off" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item prop="smscode" class="code">
          <label class="dialog-label" for="code">验证码</label>
          <div class="code-item">
            <el-input v-model="bindmobileFrom.smscode" placeholder="验证码" id="code"></el-input>
            <el-button type="success" :class="{'default':isDisabled}" :disabled='isDisabled' @click="sendCode">{{buttonText}}</el-button>
          </div>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button type="primary" @click="bindmobile">确认</el-button>
        <el-button @click="dialogphone = false">取消</el-button>
      </div>
    </el-dialog>

    <!--邮箱设置-弹窗-->
    <el-dialog title="邮箱设置" :visible.sync="dialogFormVisible" class="dialog04">
      <el-form ref="emailForm" :model="emailForm" :rules="rules">
        <el-form-item prop="email">
          <div class="dialog-item">
            <label class="dialog-label" for="email">新邮箱</label>
            <el-input id="email" v-model="emailForm.email"></el-input>
          </div>
        </el-form-item>
        <el-form-item prop="password">
          <div class="dialog-item">
            <label class="dialog-label" for="password">登录密码</label>
            <el-input type="password" id="password" v-model="emailForm.password" autocomplete="off"></el-input>
          </div>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button type="primary" @click="bindemail">确认</el-button>
        <el-button @click="dialogFormVisible = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {isEmail, isPhone} from '../../utils/validate'
import {checkMobile} from '../../utils/common'

    export default {
      name: "security",
      data() {
        var validatePass = (rule, value, callback) => {
          if (this.ruleForm.checkPass !== '') {
            this.$refs.ruleForm.validateField('checkPass');
          }
          callback();
        };

        var validateCheckPass = (rule, value, callback) => {
          if (value !== this.ruleForm.pass) {
            callback(new Error('两次输入密码不一致!'));
          }
          callback();
        };

        return {
          // 表单验证
          rules: {
            oldpassword:[{required: true, message: '请填写密码', trigger: 'change'}],
            password:[{required: true, message: '请填写密码', trigger: 'change'}],
            pass: [{ required: true, message: '请填写新密码', trigger: 'change' }, { validator: validatePass, trigger: 'blur' }],
            checkPass: [{required:true, message:'请填写确认新密码', trigger:'change'}, { validator: validateCheckPass, trigger: 'blur' }],
            tel: [{ required: true, message: '请填写手机号', trigger: 'change' }, { validator: isPhone, trigger: 'change' }],// 绑定手机号验证
            smscode: [{ required: true, message: '请填写验证码', trigger: 'change' }],
            email: [{ required: true, message: '请填写邮箱', trigger: 'change' }, { validator: isEmail, trigger: 'blur' }],// 邮箱验证
          },

          // 密码修改
          ruleForm: {
            pass: '',
            checkPass: '',
            oldpassword: ''
          },
          // 安全问题
          securityFrom: {
            answer1:"",
            answer2:"",
            answer3:"",
          },
          //邮箱
          emailForm: {
            email: "",
            password:"",
          },
          // 手机号绑定
          bindmobileFrom: {
            password:"",
            tel: "",
            smscode: "",
          },

          dialogproblem:false, //安全问题
          dialogFormVisible: false, //邮箱绑定
          dialogphone:false,//手机号绑定
          dialogpassword:false, //密码修改
          isDisabled: false, // 是否禁止点击发送验证码按钮
          buttonText: '发送验证码',
          flag: true,

          user: {},
      }
      },
      mounted() {
        this.user = JSON.parse(localStorage.getItem('UserInfo'));
      },
      methods: {
        // <!--发送验证码-->
        sendCode () {
          let tel = this.bindmobileFrom.tel
          if (checkMobile(tel)) {
            let time = 60
            this.buttonText = '已发送'
            this.isDisabled = true
            if (this.flag) {
              this.flag = false;
              let timer = setInterval(() => {
                time--;
                this.buttonText = time + ' 秒'
                if (time === 0) {
                  clearInterval(timer);
                  this.buttonText = '重新获取'
                  this.isDisabled = false
                  this.flag = true;
                }
              }, 1000);

              this.$axios.post(this.url + '/api/verification-codes',{
                phone: tel,
                scene: 'set-phone'
              }).then(res => {
              }).catch(err => {
              });
            }
          }
        },

        // 密码修改
        passwordChange(){
          let user = this.user
          this.$refs.ruleForm.validate(valid => {
            if (valid) {
              this.$axios.post(this.url + '/api/user/set-password',{
                old_password: this.ruleForm.oldpassword,
                password: this.ruleForm.pass
              }).then(res => {
                if (res.data.code !== 200) {
                  this.$message.warning({
                    showClose: true,
                    message: res.data.msg
                  });
                } else {
                  user.is_pass = true;
                  localStorage.setItem('UserInfo', JSON.stringify(user));
                  this.dialogpassword = false
                }
              }).catch(err => {
              });
            } else {
              // 登录表单校验失败
              return false;
            }
          });
        },

        // 安全问题
        security(){
          let answer1 = this.securityFrom.answer1
          let answer2 = this.securityFrom.answer2
          let answer3 = this.securityFrom.answer3
          let user = this.user

          if (answer1 == '' || answer2 == '' || answer3 == '') {
            this.$message.warning({
              showClose: true,
              message: '请填写安全问题'
            });
            return false;
          }

          this.$axios.post(
            this.url + '/api/user/security',
            {data: {
                1:answer1,
                2:answer2,
                3:answer3,
              }}
          ).then(res => {
            if (res.data.code !== 200) {
              this.$message.warning({
                showClose: true,
                message: res.data.msg
              });
            } else {
              // 设置成功
              user.is_problem = true;
              localStorage.setItem('UserInfo', JSON.stringify(user));
              this.dialogproblem = false
            }
          }).catch(err => {
          });
        },

        // 手机号绑定
        bindmobile(){
          this.$refs.bindmobileFrom.validate(valid => {
            if (valid) {
              let user = this.user
              this.$axios.post(
                this.url + '/api/user/set-phone',
                {
                  password: this.bindmobileFrom.password,
                  phone: this.bindmobileFrom.tel,
                  code: this.bindmobileFrom.smscode,
                }
              ).then(res => {
                if (res.data.code !== 200) {
                  this.$message.warning({
                    showClose: true,
                    message: res.data.msg
                  });
                } else {
                  // 绑定成功
                  user.phone = true;
                  localStorage.setItem('UserInfo', JSON.stringify(user));
                  this.dialogphone = false
                }
              }).catch(err => {
              });
            } else {
              // 登录表单校验失败
              return false;
            }
          });
        },

        // 邮箱设置
        bindemail() {
            this.$refs.emailForm.validate(valid => {
              if (valid) {
                this.$axios.post(
                  this.url + '/api/user/set-email',
                  {
                    password: this.emailForm.password,
                    email: this.emailForm.email,
                  }
                ).then(res => {
                  if (res.data.code !== 200) {
                    this.$message.warning({
                      showClose: true,
                      message: res.data.msg
                    });
                  } else {
                    this.$message.success({
                      showClose: true,
                      message: res.data.msg
                    });
                    // 绑定成功
                    this.dialogFormVisible = false
                  }
                }).catch(err => {
                });
              } else {
                // 登录表单校验失败
                return false;
              }
            });
        }
      }
    }
</script>

<style scoped lang="scss">
.securitysetting{
  .user-head {
    .icon {
      background-position: -35px -320px;
    }
  }
  // 安全设置 列表
  .itembox{padding:25px;}
  .item{
    display:flex;align-items:center;padding-bottom:20px;padding-top:20px;border-bottom:1px solid #dfdfdf;
    .icon{width:40px;flex-shrink:0;margin-right:20px;}
    .text1{font-size:14px;color:#000;line-height:1.2;margin-right:40px;flex-shrink:0;}
    .text2{font-size:14px;color:#a3acb6;line-height:1.2;min-width:0;flex-grow:1;padding-right:20px;text-align:left;}
    .text3{
      font-size:14px;line-height:1.2;
      margin-right:25px;
      &.success{color:#67c23a;}
      &.danger{color:#ff4f4f;}
    }
    .el-button{width:78px;height:32px;border-radius:4px;background:#dfdfdf;color:#8c8c8c;padding:0;border:0;font-size:14px;
      &.primary{background:#0d6efd;color:#fff;}
    }
  }
  // 密码对话框
  /deep/ .el-dialog{
    .el-dialog__header{margin-bottom:30px;}
    .el-dialog__body{padding-left:20px;padding-right:20px;height:100%;}
    .el-form-item__label{color:#687d93;font-size:14px;font-weight:bold;
      &:before{display:none;}
    }
    .el-input__inner{height:46px;line-height:46px;border:1px solid #dfdfdf;padding-left:20px;padding-right:20px;background:#fff;border-radius: 4px;
        &::-webkit-input-placeholder { /* WebKit browsers */
            color:#d2d2d2;font-size:12px;
        }
        &:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
            color:#d2d2d2;font-size:12px;
        }
        &::-moz-placeholder { /* Mozilla Firefox 19+ */
            color:#d2d2d2;font-size:12px;
        }
        &:-ms-input-placeholder { /* Internet Explorer 10+ */
            color:#d2d2d2;font-size:12px;
        }
    }
    .dialog-footer{display:flex;align-items:center;justify-content:flex-end;margin-top:30px;
      .text{color:#ff0000;font-size:12px;text-align:right;min-width:0;flex-grow:1;}
    }
    .dialog-item{margin-bottom:20px;}
    .dialog-label{display:block;font-size:14px;color:#687d93;font-weight:bold;margin-bottom:5px;}
    .el-form-item{
      .dialog-item{margin-bottom: 0;}
    }
  }
  /deep/ .dialog03{
    .code{
      .code-item{display:flex;align-items:center;justify-content:flex-start;}
      .el-input{width:auto;}
      .el-input__inner{width:120px;}
      .el-button{width:120px;height:46px;line-height:46px;text-align:center;color:#fff;background:#00be06;border-radius:4px;border:0;margin-left:20px;
        &.default{background:#dfdfdf;color:#687d93;}
      }
    }
  }

}
@media screen and (max-width: 751px) {
  .securitysetting {
    padding: 15px 20px;
    .user-head{border-bottom:1px dashed #ddd;margin-bottom:15px;padding-bottom:15px;}
    .itembox {
      padding: 0;
    }
    .item{flex-wrap:wrap;padding:15px 0;
      .icon{width:10%;margin-right:2%;margin-bottom: 5px;}
      .text1{width:88%;margin-right:0;text-align: left;margin-bottom:5px;}
      .text2{font-size:12px;margin-bottom:10px;line-height:1.8;text-align:justify;padding-right:0;}
      .text3{width:70%;margin-right:0;text-align:left;font-size:12px;}
      .el-button{width:30%;height:auto;padding-top:5px;padding-bottom:5px;line-height:inherit;font-size:12px;}
    }
    /deep/ .el-dialog{
      .el-dialog__body{padding:0;}
      .el-input__inner{height:35px;line-height:35px;padding-left:10px;padding-right:10px;}
      .el-form-item{margin-bottom:10px;}
      .dialog-footer{
        .el-button{width:48%;margin-left:0;}
        .el-button--primary{margin-right:4%;}
      }
    }
    /deep/ .dialog02{
      .dialog-footer{flex-wrap:wrap;margin-top:20px;
        .text{width:100%;margin-bottom:10px;text-align: left;}
      }
    }
    /deep/ .dialog03{
      .code{
        .el-input__inner{width:100%;}
        .el-input{flex-grow:1;min-width:0;}
        .el-button{height:35px;line-height:35px;font-size:12px;}
      }
    }
  }
}
</style>

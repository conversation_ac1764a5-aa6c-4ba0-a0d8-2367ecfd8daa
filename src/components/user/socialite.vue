<template>
  <!-- 第三方绑定 -->
  <div class="three-bind">
    <div class="user-head">
      <!-- 标题 -->
      <div class="title">
        <div class="icon"></div>
        <div class="text">第三方登录</div>
      </div>
    </div>
    <div class="body">
      <div class="title">绑定第三方平台 登录更方便</div>
      <div class="itembox">
        <div class="item">
          <div class="icon"><img src="../../assets/img/img35.png" alt="微信登录"></div>
          <div class="text1">微信登录</div>
          <div class="text2" :class="{'active':user.is_wechat}" v-if="user.is_wechat">已绑定</div>
          <el-button type="success" v-if="!user.is_wechat" @click="bind('wechat')">立即绑定</el-button>
        </div>
        <div class="item">
          <div class="icon"><img src="../../assets/img/img36.png" alt="QQ登录"></div>
          <div class="text1">QQ登录</div>
          <div class="text2" :class="{'active':user.is_qq}" v-if="user.is_qq">已绑定</div>
          <el-button type="primary" v-if="!user.is_qq" @click="bind('qq')">立即绑定</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  inject: ["reload"],
  name: "socialite",
  data() {
    return {
      showif: false,
      showiftwo: false,

      user: {},
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.user = JSON.parse(localStorage.getItem("UserInfo"));
    });
  },
  methods: {
    bind(type) {
      if (type === "wechat") {
        window.open(
          "https://main.radirhino.com/auth/weixinweb?user_id=" + this.user.id,
          "_blank"
        );
        localStorage.removeItem("IsWechatBind");
        localStorage.removeItem("bindTips");
        localStorage.removeItem("bindCode");
        let _this = this;
        this.timer = setInterval(() => {
          let IsWechatBind = localStorage.getItem("IsWechatBind");
          let bindCode = localStorage.getItem("bindCode");
          let bindTips = localStorage.getItem("bindTips");

          if (IsWechatBind == "true") {
            clearInterval(this.timer);
            let user = this.user;

            if (bindCode == "200") {
              this.$axios
                .get(this.url + "/api/user", {})
                .then((res) => {
                  if (res.data.code) {
                    this.$nextTick(() => {
                      _this.user = res.data.data;
                    });
                    localStorage.setItem(
                      "UserInfo",
                      JSON.stringify(res.data.data)
                    );
                  }
                })
                .catch((err) => {});

              this.$message.success(bindTips);
            } else {
              this.$message.warning(bindTips);
            }
            console.log("bind.success");

            localStorage.removeItem("IsWechatBind");
            localStorage.removeItem("bindTips");
            localStorage.removeItem("bindCode");
            this.reload();
          }
        }, 3000);
      } else if (type === "qq") {
        window.open(
          "https://main.radirhino.com/auth/qq?user_id=" + this.user.id,
          "_blank"
        );
        localStorage.removeItem("IsWechatBind");
        localStorage.removeItem("bindTips");
        localStorage.removeItem("bindCode");
        let _this = this;
        this.timer = setInterval(() => {
          let IsWechatBind = localStorage.getItem("IsWechatBind");
          let bindCode = localStorage.getItem("bindCode");
          let bindTips = localStorage.getItem("bindTips");

          if (IsWechatBind == "true") {
            clearInterval(this.timer);
            let user = this.user;

            if (bindCode == "200") {
              this.$axios
                .get(this.url + "/api/user", {})
                .then((res) => {
                  if (res.data.code) {
                    this.$nextTick(() => {
                      _this.user = res.data.data;
                    });
                    localStorage.setItem(
                      "UserInfo",
                      JSON.stringify(res.data.data)
                    );
                  }
                })
                .catch((err) => {});

              this.$message.success(bindTips);
            } else {
              this.$message.warning(bindTips);
            }
            console.log("bind.success");

            localStorage.removeItem("IsWechatBind");
            localStorage.removeItem("bindTips");
            localStorage.removeItem("bindCode");
            this.reload();
          }
        }, 3000);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.three-bind {
  .user-head {
    .icon {
      background-position: -35px -373px;
    }
  }
  .body{
    min-height:760px;
    padding-top:110px;
    .title{font-size:24px;color:#687d93;font-weight:bold;text-align:center;margin-bottom:50px;line-height:1.2;}
  }
  .itembox{display:flex;align-items:stretch;justify-content:center;}
  .item{
    padding:40px;
    width:330px;margin-right:50px;
    border: solid 6px #f4f4f4;
    &:last-child{margin-right:0;}
    .icon{width:90px;margin:0 auto 20px;display:block;}
    .text1{font-size:14px;color:#687d93;line-height:1.4;margin-bottom:15px;}
    .text2{font-size:14px;color:#a3acb6;line-height:1.4;margin-bottom:40px;
      &.active{color:#0d6efd;}
    }
    .el-button{
      width:100%;height:50px;border:0;line-height:50px;padding:0;border-radius:4px;font-size:14px;color:#fff;
      &.el-button--success{background-color:#21a50e;}
      &.el-button--primary{background-color:#0d6efd}
    }
  }
}

@media screen and (max-width: 751px) {
  .three-bind {
    padding: 15px 20px;
    .user-head{margin-bottom:15px;padding-bottom:15px;border-bottom:1px dashed #ddd;}
    .body{
      min-height:inherit;
      padding-top:20px;
      .title{font-size:14px;font-weight:bold;margin-bottom:30px;}
    }
    .itembox{flex-wrap:wrap;}
    .item{width:100%;margin-right:0;border-width:3px;padding:20px;margin-bottom:20px;
      &:last-child{margin-bottom:0;}
      .icon{width:45px;margin-bottom:10px;}
      .text1{margin-bottom:10px;}
      .text2{margin-bottom:20px;}
      .el-button{height:35px;line-height:35px;font-size:12px;}
    }
  }
}
</style>

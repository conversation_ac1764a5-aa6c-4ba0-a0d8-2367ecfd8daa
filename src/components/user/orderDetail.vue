<template>
  <!-- 订单详情 -->
  <div class="user-order-detail">
    <div class="user-head">
      <!-- 标题 -->
      <div class="title">
        <div class="icon"><img src="@/assets/img/order.png" alt="" /></div>
        <div class="text">订单详情</div>
      </div>
    </div>
    <!--  -->
    <div class="main">
      <!-- 信息 -->
      <div class="info">
        <div class="left">
          <div class="item"><span>订  单  号：</span>{{ orderInfo.order_no }}</div>
          <div class="item" v-if="orderInfo.consignee"><span>收货信息：</span><span>{{ orderInfo.consignee.address }}, {{ orderInfo.consignee.name }}, {{ orderInfo.consignee.phone }}</span></div>
          <div class="item"><span>成交时间：</span>{{ orderInfo.created_at }}</div>
          <div class="item"><span>付款时间：</span>{{ orderInfo.pay_time }}</div>
          <div class="item" v-if="orderInfo.logistic"><span>发货时间：</span><span>{{ orderInfo.logistic.delivery_time }}</span></div>
        </div>
        <div class="right">
          <div class="item text1"><span>付款方式：</span>
            <strong v-if="orderInfo.pay_method == 0">无</strong>
            <strong v-if="orderInfo.pay_method == 1">微信</strong>
            <strong v-if="orderInfo.pay_method == 2">支付宝</strong>
          </div>
          <div class="item text2" v-if="orderInfo.logistic"><span>{{ orderInfo.logistic.logistics_name }}快递单号：</span>{{ orderInfo.logistic.logistics_no }}</div>
<!--          <div class="item text2" v-if="orderInfo.product_type == 3 && orderInfo.logistic"><span>{{ orderInfo.logistic.logistics_name }}快递单号：</span>{{ orderInfo.logistic.logistics_no }}  <a href="javacript:;">去官网查物流 ></a></div>-->
        </div>
      </div>
      <!-- 清单 -->
      <el-table :data="realthingList" class="realthing-table">
        <el-table-column label="商品" min-width="230" align="center">
          <template slot-scope="scope">
            <div class="product">
              <img :src="scope.row.product_img" alt="" class="image" />
              <div class="text">{{scope.row.product_name}}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="订单价格(光子)" align="center">
          <template slot-scope="scope">
            <div class="price-text1">{{scope.row.order_amount}}</div>
          </template>
        </el-table-column>
        <el-table-column prop="num" label="优惠码(光子)" align="center">
          <template slot-scope="scope">
            <div class="price-text1">-{{ scope.row.deduct_price }}</div>
            <div class="price-text2" v-if="scope.row.deduct_price > 0">已使用优惠券</div>
          </template>
        </el-table-column>
        <el-table-column prop="num" label="数量" align="center"></el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">
            <div class="state state3" v-if="scope.row.status == 0">待支付</div>
            <div class="state state3" v-if="scope.row.status == 1">交易成功</div>
            <div class="state state3" v-if="scope.row.status == 3">已关闭</div>
          </template>
        </el-table-column>
      </el-table>
      <!-- 结算 -->
      <div class="settlement">
        <div>订单金额：{{ orderInfo.total_amount }} 光子</div>
        <div v-if="orderInfo.deduct_price > 0">优 惠 码：-{{ orderInfo.deduct_price }} 光子</div>
        <div>运 　费：{{ orderInfo.postage }} 光子</div>
        <div><strong>实  付  款：</strong><span>{{ orderInfo.order_amount }} 光子</span></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      // 实体订单数据
      orderInfo: {},
      realthingList: [],
    };
  },
  beforeMount() {
    this.getData();
  },
  methods: {
    getData() {
      let order_id = this.$route.params.id;
      if (order_id) {
        this.$axios.get(this.url + '/api/order/'+order_id, {
        }).then(res => {
          if (res.data.code === 200) {
            this.orderInfo = res.data.data;
            let data = [];
            data.push(this.orderInfo);
            this.realthingList = data;
          } else if (res.data.code === 4001) {
            localStorage.removeItem('UserInfo');
            localStorage.removeItem('Authorization');
            this.$router.push({name:"login"})
          } else {
            this.$router.go(-1);
          }
        }).catch(err => {
        })
      } else {
        this.$router.go(-1);
      }
    }
  },
};
</script>

<style scoped lang="scss">
.user-order-detail {
  text-align:left;

  // 主体
  .main{
    padding:30px 25px 90px;
    .item{color:#687d93;margin-bottom:10px;line-height:1.6;font-size:13px;
      span{color:#909399;font-weight:400;}
    }
    .info{display:flex;align-items:flex-start;justify-content:space-between;margin-bottom:30px;}
    .left{
      width:56%;
      &>div{}

    }
    .right{
      width:42%;
      strong{color:#00be06}
      .text2{
        color:#687d93;font-weight:bold;
        a{text-decoration: underline!important;color:#687d93;font-weight:bold;}
      }
    }
  }
  //
  .realthing-table{
    margin-bottom:40px;
    border:1px solid #dedede;
    border-bottom:0;
    color:#687d93;
    /deep/ .el-table__cell{padding-top:20px;padding-bottom:20px;}
    /deep/ th.el-table__cell>.cell{color:#687d93;}
    .cell{font-size:12px;color:#909399;}
    .product{
      display:flex;align-items:center;
      .image{width:60px;flex-shrink:0;margin-right:12px;}
      .text{font-size:12px;line-height:1.6;color:#687d93;text-align:justify;}
    }
    .number,
    .date{
      .cell{
        color:#909399;font-size:12px;
      }
    }
    .price-text1{font-size:12px;color:#ff0000;font-weight:bold;text-align:center;}
    .price-text2{font-size:12px;color:#687d93;font-size:12px;}
    .state{
      text-align:left;
      font-size:12px;color:#000000;line-height:1.6;
      &.state1{color:#ff0000;}
      &.state2{color:#0d6efd;}
      &.state3{color:#00be06;}
    }
  }
  // 结算
  .settlement{
    display:table;margin-left:auto;margin-right:0;
    color:#687d93;font-size:13px;line-height:1.8;
    &>div{margin-bottom:10px;
      &:last-child{margin-bottom:0;}
    }
    strong{color:#000;}
    span{color:#ff0000;}
  }
}
@media screen and (max-width: 751px) {
  .user-order-detail {
    padding: 15px 20px;
  }
  .user-order-detail .main{padding:0;}
  .user-order-detail .main .info{
    flex-wrap:wrap;margin-bottom:20px;
    .left{width:100%;}
    .right{width:100%;}
    .item{font-size:12px;}
  }
  .user-order-detail .realthing-table{margin-bottom:20px;}
  .user-order-detail .realthing-table /deep/ .el-table__cell{padding-top:10px;padding-bottom:10px;}
  .user-order-detail .settlement{
    font-size:12px;
    &>div{margin-bottom:5px;}
  }
}
</style>

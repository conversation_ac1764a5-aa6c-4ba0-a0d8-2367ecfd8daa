<template>
  <div class="user-address">
    <!-- 收货地址 -->
    <div class="user-head">
      <!-- 标题 -->
      <div class="title">
        <div class="icon"></div>
        <div class="text">收货地址</div>
      </div>
    </div>
    <!-- 主体 -->
    <div class="main">
      <div class="itembox">
        <!-- 添加 active 类，可使用选中样式 -->
        <div class="item" :class="{'active':active === index}" @click="active = index" v-for="(item,index) in addressList" :key="index">
          <!-- 姓名 -->
          <div class="head">
            <div class="name">{{item.name}}</div>
            <div class="close el-icon-close" @click="del(item, index)"></div>
          </div>
          <div class="info">
            <!-- 联系电话 -->
            <div class="phone">{{item.phone}}</div>
            <!-- 联系地址 -->
            <div class="address">{{item.address}}</div>
            <!-- 邮编 -->
            <div class="post_code">{{item.post_code}}</div>
          </div>
          <!-- 操作 -->
          <div class="option">
            <el-button type="text" v-if="item.status!=1">设为默认</el-button>
            <el-button type="text" @click="edit(item, index)">编辑</el-button>
          </div>
        </div>
        <div class="item add-btn" @click="editDialog = true">
          <div class="icon el-icon-plus"></div>
          <div class="text">使用新地址</div>
        </div>
      </div>
    </div>

    <!-- 修改地址弹窗 -->
    <div class="dialog-open manual-open" @click="close" :class="{ active: editDialog }">
      <div class="dialog-box" @click.stop>
        <div class="close el-icon-close" @click="close"></div>
        <div class="open-title">{{dialogText}}</div>
        <div class="body">
          <el-form ref="form" :model="form" :rules="rules" class="editForm" label-width="80px">
            <el-form-item label="收件人" prop="name">
              <el-input v-model="form.name"></el-input>
            </el-form-item>
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone"></el-input>
            </el-form-item>
            <el-form-item label="联系地址" prop="address">
              <el-input v-model="form.address" class="address" type="textarea"></el-input>
            </el-form-item>
            <el-form-item label="邮政编码">
              <el-input v-model="form.post_code"></el-input>
            </el-form-item>
            <el-form-item>
              <el-checkbox v-model="form.status">设为默认地址</el-checkbox>
            </el-form-item>
            <el-form-item class="btn-group">
              <el-button type="primary" @click="handleAddress()">确认</el-button>
              <el-button @click="close">取消</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {isEmpty} from "../../utils/common";

export default {
  components: {},
  data() {
    return {
      // 地址列表
      addressList:[],
      // 修改弹窗打开状态
      editDialog:false,
      //
      form:{},
      // 修改地址表单验证
      rules: {
        name: [{ required: true, message: '请输入收件人', trigger: 'blur' }],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3|5|7|8|9]\d{9}$/, message: '请输入正确的号码格式', trigger: 'blur' }
        ],
        address: [{required: true, message: '请输入联系地址', trigger: 'blur' }]
      },
      // 选中地址
      active:0,
      // 编辑
      isAdd:true,
      // 编辑框文本
      dialogText:"添加新地址"
    };
  },

  beforeMount() {
    this.getData()
  },

  methods: {
    getData() {
      this.$axios.get(this.url + "/api/address", {
      }).then((res) => {
        if (res.data.code === 200) {
          this.addressList = res.data.data;
        }
      }).catch((err) => {});
    },

    // 添加新地址
    handleAddress(){
      // 进行表单验证
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 表单验证成功
          // 判断是否是新添加地址
          if(this.isAdd){
            // 将新添加的地址 push 到 地址列表
            this.addressList.push(this.form)
          }else{
            // 赋值给原来的数组
            this.addressList[this.form.index] = this.form
            //
            this.isAdd = true
            // 将弹框修改成“添加新地址”
            this.dialogText = "添加新地址"
          }

          let addressInfo = this.addressList[this.form.index];
          if (isEmpty(addressInfo)) {
            this.$axios.post(this.url + '/api/address',this.form)
                .then(res => {})
                .catch(err => {});
          } else {
            this.$axios.put(this.url + "/api/address/"+addressInfo.id, this.form)
                .then((res) => {})
                .catch((err) => {});
          }

          // 清空表单对象
          this.form = {}
          // 关闭弹窗
          this.editDialog = false
        } else {
          // 失败
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 编辑地址
    edit(item, index){
      this.isAdd = false
      // 打开弹窗
      this.editDialog = true
      // 将数据赋值给表单对象 form
      this.form = this.addressList[index]
      this.$set(this.form, "status", item.status ? true : false);
      // 赋值 编辑的对象 index
      this.form.index = index
      this.dialogText = "编辑地址"
    },
    // 删除地址
    del(item, index){
      this.addressList.splice(index, 1)
      this.$axios.delete(this.url + "/api/address/"+item.id+"/delete", this.form)
          .then((res) => {})
          .catch((err) => {});
    },
    // 关闭弹窗
    close(){
      // 如果是编辑状态下打开弹窗，则关闭弹窗的时候吧 form 表单对象清空
      // 以便点击添加地址时，表单对象是空的
      if(this.form.index != undefined){
        this.form = {}
      }
      this.editDialog = false
      // 将弹框修改成“添加新地址”
      this.dialogText = "添加新地址"
    }
  },
};
</script>

<style lang="scss" scoped>
.user-address {
  .user-head {
    .icon {
      background-position: -35px -265px;
    }
  }
  .main {
    padding: 25px;
  }
  .itembox{display:flex;align-items:stretch;flex-wrap:wrap;}
  .item{
    cursor: pointer;
    text-align:left;
    background:#fff;
    border: solid 1px #dfdfdf;
    padding:20px;position:relative;
    padding-bottom:10px;display:flex;flex-flow:column;align-items:flex-start;justify-content:flex-start;
    &:hover,
    &.active{
      border-color:#0d6efd;border-width:2px;
      .head,
      .info{color:#000;}
    }
    .head{font-size:14px;line-height:1.2;color:#a3acb6;padding-bottom:10px;border-bottom:1px solid #dfdfdf;margin-bottom:5px;width:100%;}
    .close{position: absolute;top:8px;right:8px;font-size:20px;color:#96a1ac;}
    .info{color:#a3acb6;font-size:14px;text-align:justify;line-height:1.8;margin-bottom:5px;flex-grow:1;width:100%;}
    .option{text-align:right;align-self: flex-end;}
    .el-button{padding:0;}
  }
  .add-btn{
    display:flex;flex-flow: column;justify-content:center;align-items:center;
    border-style:none!important;min-height:190px;
    background:#f1f1f1;color:#a3acb6;text-align:center;
    &:hover{background:#e3e3e3;}
    .icon{font-size:26px;font-weight:bold;margin-bottom:20px;}
    .text{font-size:14px;}
  }

  // 弹窗样式
  .dialog-open{
    position: fixed;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;z-index:999;
    transition: all 0.4s cubic-bezier(0.18, 0.89, 0.32, 1.28);
    transform: scale(0.8);
    visibility: hidden;
    opacity: 0;

    &.active {
      transform: scale(1);
      opacity: 1;
      visibility: visible;
    }
    .dialog-box {
      position:relative;
      text-align: left;
      width:100%;
      max-width: 556px;
      height: 440px;
      background: #fff;
      box-shadow: 0px 0px 30px 0px rgba(4, 0, 0, 0.3);
      border-radius: 6px;
      padding: 30px 25px;
    }
    .close {
      font-size: 30px;
      color: #96a1ac;
      position: absolute;
      top: 15px;
      right: 15px;
      cursor: pointer;
    }
    .open-title {
      color: #0084ff;
      line-height: 1.2;
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 30px;
    }
    .editForm /deep/{
      .el-input__inner{border:1px solid #a3acb6;border-radius:0;height:32px;line-height:32px;}
      .el-form-item--mini.el-form-item,
      .el-form-item--small.el-form-item{margin-bottom:15px;}
      .el-form-item__label{color:#909399;}
      .el-checkbox__input.is-checked .el-checkbox__inner,
      .el-checkbox__input.is-indeterminate .el-checkbox__inner{background-color:#0d6efd;border-color:#0d6efd;}
      .el-checkbox__inner{border-color:#a3acb6;}
      .el-checkbox__label{color:#333;}
      .el-checkbox__input.is-checked+.el-checkbox__label{color:#0d6efd;}
      .address .el-textarea__inner{height:100px!important;line-height:1.8;border-color:#a3acb6;border-radius:0;resize: none;

        // 隐藏滚动条
        &::-webkit-scrollbar {
          display: none;
        }
        // 兼容 Firefox
        scrollbar-width: none;
        // 兼容 IE 10 +
        -ms-overflow-style: none;
      }
      .btn-group{width:100%;
        .el-form-item__content{text-align:right;width:100%;}
        .el-button{width:80px;height:32px;line-height:32px;border-radius:0;padding:0;
          &:focus,
          &:hover{background-color:inherit;border-color:inherit;}
          &.el-button--default{border-color:#bdbdbd;color:#bdbdbd;}
        }
      }
    }
  }
}

@media screen and (min-width: 751px) {
  .user-address {
    .item{
      width:32%;
      &:nth-child(3n-1){margin-left:2%;margin-right:2%;}
      &:nth-child(3)~.item{margin-top:2%;}
    }
  }
}
@media screen and (max-width: 751px) {
  .user-address {
    padding: 15px 20px;
    .main {
      padding: 0;
    }

    .dialog-open{width:90%;left:5%;margin-left:auto;}
    .dialog-open .open-title{font-size:16px;}
    .dialog-open .editForm /deep/ {
      .btn-group .el-form-item__content{display:flex;align-items:center;
        &::before,
        &::after{display:none;}
      }
      .btn-group .el-button{flex-grow:1;}
    }
    //
    .item{width:100%;margin-bottom:20px;}
    .add-btn{margin-bottom:0;}
  }
}
</style>

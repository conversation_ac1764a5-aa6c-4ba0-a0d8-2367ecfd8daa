<template>
  <div class="user-message">
    <!-- 消息回复 -->
    <div class="user-head">
      <!-- 标题 -->
      <div class="title">
        <div class="icon"></div>
        <div class="text">消息回复</div>
      </div>
    </div>
    <!-- 主体 -->
    <div class="main">
      <!-- 消息列表 -->
      <div class="list">
        <div class="list-item" v-for="(item,index) in comments" :key="index">
          <!-- 评论 -->
          <div class="comment">
            <div class="xg-line-1 text">
              我的评论：{{ item.comment.content }}
            </div>
            <div class="time">{{ item.comment.created_at }}</div>
          </div>
          <!-- 回复 -->
          <div class="reply">
            <div class="xg-line-1 text">
              {{ item.user.name }}回复：{{ item.content }}
            </div>
            <div class="time">{{ item.created_at }}</div>
          </div>
          <!-- 原链接 -->
          <div class="links">
            <router-link :to="{name:'coursepage',params:{id:item.comment.target_id}}" v-if="item.comment.target_type=='task'">查看原链接</router-link>
            <router-link :to="{name:'peripheryDetails',params:{id:item.comment.target_id}}" v-if="item.comment.target_type=='goods'">查看原链接</router-link>
          </div>
        </div>
      </div>
      <!-- 分页 -->
      <el-pagination
        background
        class="xg-pagination"
        layout="prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="offset"
        :page-size="limit"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      // 评论
      comments:[],
      // 评论分页
      limit: 15,
      offset: 1,
      total: 0,
    };
  },
  beforeMount() {
    this.getComment();
  },
  methods: {
    handleSizeChange(val) {
      this.limit = val;
    },
    handleCurrentChange(val) {
      let page = val - 1;
      this.getComment(page);
    },
    //获取评论
    getComment(offset = 0) {
      this.$axios.get(this.url + "/api/user/reply", {
            params: {
              limit: this.limit,
              offset: offset,
            },
          })
          .then((res) => {
            if (res.data.code === 200) {
              this.comments = res.data.data.lists;
              this.total = res.data.data.total;
            }
          })
          .catch((err) => {
            this.comments = [];
          });
    },
  },
};
</script>

<style lang="scss" scoped>
.user-message {
  .user-head {
    .icon {
      background-position: -35px -158px;
    }
  }
  .main {
    padding:25px;
  }
  .list{}
  .list-item{
    display:block;border-radius:8px;border:1px solid #dedede;padding:20px;padding-top:0;margin-bottom:20px;text-align:left;
    &:last-child{margin-bottom:0;}
    .text{flex-grow:1;min-width:0;padding-right:20px;font-size:12px;}
    .time{font-size:12px;color:#687d93;flex-shrink:0;}
  }
  .comment,
  .reply{display:flex;align-items:center;padding-top:20px;padding-bottom:20px;border-bottom:1px solid #dedede;}
  .comment{
    .text{color:#000;}
  }
  .reply{
    .text{color:#687d93;}
  }
  .links{text-align:right;font-size:12px;color:#0d6efd;padding-top:20px;}
  .el-pagination{margin-top:30px;}
}

@media screen and (max-width: 751px) {
  .user-message {
    padding: 15px 20px;
    .main{padding:0;}
    .list-item{padding:0 10px 10px;
      .text{width:100%;line-height:1.6em;padding-right:0;height:3.2em;margin-bottom:5px;
        white-space: inherit;
        overflow: hidden;
        word-break: break-all;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-flex: 2;
        -webkit-box-orient: vertical;
      }
    }
    .comment,
    .reply{flex-wrap:wrap;padding-top:10px;padding-bottom:10px;}
    .links{padding-top:10px;}
  }
}
</style>

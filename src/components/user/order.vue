<template>
  <div class="user-order">
    <!-- 订单搜索 -->
    <div class="user-head">
      <!-- 标题 -->
      <div class="title">
        <div class="icon"></div>
        <div class="text">订单列表</div>
      </div>
      <!-- 搜索 -->
      <div class="search">
        <el-input
          placeholder="请输入搜索关键词"
          v-model="keywords"
          class="input-with-select"
        >
          <el-button
            slot="append"
            icon="el-icon-search"
            @click="search"
          ></el-button>
        </el-input>
      </div>
    </div>
    <!-- 订单列表 -->
    <div class="order-list">
      <el-tabs v-model="activeName" class="user-tabs" @tab-click="tabChange">
        <el-tab-pane label="虚拟订单" name="0"></el-tab-pane>
<!--        <el-tab-pane label="实物订单" name="1"></el-tab-pane>-->
      </el-tabs>
      <!-- 订单列表 swiper -->
      <swiper ref="tabSwiper" :options="swiperOption" @slideChange="swiperChange">
        <!-- 虚拟订单 -->
        <swiper-slide>
          <el-table :data="virtualList" class="realthing-table">
            <el-table-column prop="product_name" label="商品名称" min-width="240">
            </el-table-column>
            <el-table-column label="类型">
              <template slot-scope="scope">
                <div v-if="scope.row.product_type == 1">课程</div>
                <div v-if="scope.row.product_type == 2">插件</div>
                <div v-if="scope.row.product_type == 3">商品</div>
              </template>
            </el-table-column>
            <el-table-column prop="order_no" label="订单号" width="180"></el-table-column>
            <el-table-column prop="order_amount" label="支付金额(光子)" width="120"></el-table-column>
            <el-table-column label="状态">
              <template slot-scope="scope">
                <div class="state state1" v-if="scope.row.status== 0">待支付</div>
                <div class="state state3" v-if="scope.row.status== 1">已支付</div>
                <div class="state state2" v-if="scope.row.status== 2">已关闭</div>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="140" class="date"></el-table-column>
            <el-table-column fixed="right" label="操作" class="option" width="60" align="right">
              <template slot-scope="scope">
                <router-link class="state state2" :to="{name:'userOrderDetail', params:{id:scope.row.id}}" v-if="scope.row.status == 1">详情</router-link>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <el-pagination
            background
            class="xg-pagination"
            layout="prev, pager, next,jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="virtualPage.page"
            :page-size="virtualPage.pagesize"
            :total="virtualPage.total">
          </el-pagination>
        </swiper-slide>
        <!-- 实物订单 -->
        <swiper-slide>
          <el-table :data="realthingList" class="realthing-table">
            <el-table-column label="商品" min-width="200">
              <template slot-scope="scope">
                <div class="product">
                  <img :src="scope.row.product_img" alt="" class="image" />
                  <div class="text">{{scope.row.product_name}}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="order_no" label="订单号" class="number" min-width="100"></el-table-column>
            <el-table-column label="支付金额(光子)" align="center" width="120">
              <template slot-scope="scope">
                <div class="price-text1">{{scope.row.order_amount}}</div>
<!--                <div class="price-text2" v-if="scope.row.deduct_integral > 0">(含{{ scope.row.deduct_integral }}光子,优惠码）</div>-->
              </template>
            </el-table-column>
            <el-table-column label="状态">
              <template slot-scope="scope">
                <div class="state state1" v-if="scope.row.status== 0">待支付</div>
                <div class="state state3" v-if="scope.row.status== 1">已支付</div>
                <div class="state state2" v-if="scope.row.status== 2">已关闭</div>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" class="date"></el-table-column>
            <el-table-column fixed="right" label="操作" class="option" width="60" align="right">
              <template slot-scope="scope">
                <router-link class="state state2" :to="{name:'userOrderDetail', params:{id:scope.row.id}}" v-if="scope.row.status == 1">详情</router-link>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <el-pagination
            background
            class="xg-pagination"
            layout="prev, pager, next,jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="realthingPage.page"
            :page-size="realthingPage.pagesize"
            :total="realthingPage.total">
          </el-pagination>
        </swiper-slide>
      </swiper>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      // 订单选项卡激活项
      activeName: "0",
      // swiper 初始化配置
      swiperOption: {
        autoHeight:true,
        spaceBetween: 20, // swiper-solid 间距
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
        // allowTouchMove: false,// 禁止滑动
        preventClicks: false, //默认true
      },
      // 搜索关键词
      keywords:'',
      virtualPage:{
        pagesize: 10,
        page: 1,
        total: 0
      },
      // 虚拟订单数据
      virtualList: [],

      realthingPage:{
        pagesize: 10,
        page: 1,
        total: 0
      },
      // 实体订单数据
      realthingList: [],
    };
  },
  beforeMount() {
    this.getOrderList()
  },
  methods: {
    handleSizeChange(val) {
      if (this.activeName=='0') {
        this.virtualPage.pagesize = val;
      } else {
        this.realthingPage.pagesize = val;
      }
    },
    handleCurrentChange(val) {
      if (this.activeName=='0') {
        this.virtualPage.page = val;
      } else {
        this.realthingPage.page = val;
      }
      this.getOrderList(val-1)
    },

    handleClick(row) {
      console.log(row);
    },
    /**
     * 分类选项点击事件
     */
    tabChange(e) {
      this.getOrderList()
      this.$refs.tabSwiper.swiper.slideTo(e.name);
    },
    /**
     * swiper切换事件
     */
    swiperChange(e) {
      this.activeName = this.$refs.tabSwiper.swiper.activeIndex + "";
    },
    /**
     * 点击搜索
     */
    search(){
      this.getOrderList(0, this.keywords);
    },

    getOrderList(page = 0, keywords = '') {
      let data = {
        cate_id:this.activeName,
        offset: page,
      };
      if (this.activeName=='0') {
        data.limit= this.virtualPage.pagesize;
      } else {
        data.limit= this.realthingPage.pagesize;
      }

      if (keywords) {
        data.keywords = keywords;
      }

      this.$axios.get(this.url + '/api/user/order-list', {
        params: data
      }).then(res => {
        if (res.data.code === 200) {
          if (this.activeName=='0') {
            this.virtualList = res.data.data.lists;
            this.virtualPage.total = res.data.data.total;
          } else {
            this.realthingList = res.data.data.lists;
            this.realthingPage.total = res.data.data.total;
          }

        } else if (res.data.code === 4001) {
          localStorage.removeItem('UserInfo');
          localStorage.removeItem('Authorization');
          this.$router.push({name:"login"})
        }
      }).catch(err => {
      })
    }
  }
};
</script>

<style scoped lang="scss">
.user-order {
  .user-head{
    .icon{background-position: -35px 4px;}
  }
  .order-list /deep/ {
    padding: 20px 25px 75px;
    // 订单列表
    .realthing-table{
      .el-table__cell{vertical-align:top;padding-top:20px;padding-bottom:20px;}
      th.el-table__cell>.cell{color:#687d93;}
      .cell{font-size:12px;color:#909399;}
      .product{
        display:flex;align-items:flex-start;
        .image{width:60px;flex-shrink:0;margin-right:12px;}
        .text{font-size:12px;line-height:1.6;color:#687d93;text-align:justify;}
      }
      .number,
      .date{
        .cell{
          color:#909399;font-size:12px;
        }
      }
      .price-text1{line-height: 1.6;font-size:12px;color:#ff0000;font-weight:bold;text-align:center;}
      .price-text2{line-height: 1.6;font-size:12px;color:#909399;}
      .state{
        text-align:left;
        font-size:12px;color:#000000;line-height:1.6;
        &.state1{color:#ff0000;}
        &.state2{color:#0d6efd;}
        &.state3{color:#00be06;}
      }
    }
    .xg-pagination{margin-top:50px;}
  }
}
@media screen and (max-width: 751px) {
  .user-order{padding:15px 20px;}
  .user-order .order-list{padding:0px;}
  .user-order .order-list /deep/ .realthing-table .el-table__cell{padding-top:10px;padding-bottom:10px;}
  .user-order .order-list /deep/ .xg-pagination{margin-top:20px;}
}
</style>

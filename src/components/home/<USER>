<template>
  <div class="xg-home">

    <!-- 第一屏视频 start -->
    <div class="xg-banner" @click="clickScroll">
      <video
        autoplay="autoplay"
        loop="loop"
        muted="true"
        playsinline="true"
        type="video/mp4"
        webkit-playsinline="true"
        x-webkit-airplay="true"
        x5-video-orientation="h5"
        x5-video-player-fullscreen="true"
        x5-video-player-type="h5"
        poster="@/assets/img/video-img.jpg"
      >
        <source src="https://radirhino.oss-accelerate.aliyuncs.com/files/homevideo.mp4" />
      </video>
      <div class="info">
        <div class="xg-line-2 desc">
          <p>犀光，</p>
          <p>参数化你的工作流。</p>
        </div>
        <div class="xg-more">开始探索</div>
      </div>
    </div>
    <div ref="go1"></div>
    <!-- 第一屏视频 end -->

    <!-- 课程 模块 start -->
    <xgCourse></xgCourse>
    <!-- 课程 模块 end -->

    <!-- 插件 模块 start -->
    <xgPlug></xgPlug>
    <!-- 插件 模块 end -->

    <!-- 周边 模块 start -->
    <xgPeriphery></xgPeriphery>
    <!-- 周边 模块 end -->

    <!-- AI作品集 模块 start -->
    <aiGallery></aiGallery>
    <!-- AI作品集 模块 end -->
    
    <!-- 新闻资讯 模块 start -->
    <xgNews></xgNews>
    <!-- 新闻资讯 模块 end -->

  </div>
</template>

<script>
// 引入 课程 模块
import xgCourse from "./course";
// 引入 插件 模块
import xgPlug from "./plug";
// 引入 周边 模块
import xgPeriphery from "./periphery";
// 引入 AI作品集 模块
import aiGallery from "./gallery";
// 引入 新闻 模块
import xgNews from "./news";
import { mapMutations } from 'vuex'

export default {
  inject: ['reload'],
  name: "xgHome",
  components: {
    xgCourse,
    xgPlug,
    xgPeriphery,
    xgNews,
    aiGallery
  },
  props: {
    title: {
      //模块标题
      type: String,
      default: ""
    }
  },
  data () {
    return {
      windowHeight: '',
      autopl: false,
      scrollFlag: false,
      scrolldwom: true,
      imgList: [],
      plugs: [],
      news: [],
      imgHeight: "",
      currentDate: new Date(),
      heightFlag:false
    }
  },
  created () {
    this.login();
    this.bind();
  },
  mounted () {
    window.addEventListener('scroll', this.handleScroll);
    this.windowHeight = document.documentElement.clientHeight + "px";
  },
  methods: {
    ...mapMutations(['changeLogin']),
    handleScroll () {
      setTimeout(() => {
        //代码
        let _this = this;
        var scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
        if (scrollTop) {
          _this.scrollFlag = true
          _this.scrolldwom = false
        } else {
          _this.scrollFlag = false
          _this.scrolldwom = true
        }
      }, 100)
    },
    // 点击滚动一屏方法
    go () {
      this.$refs["go1"].scrollIntoView(true);
    },

    /**
     * 登录方法
     */
    login () {
      let token = this.$route.query.token;
      let _this = this;
      // console.log(token);
      if (token != '' && token != undefined && token != null) {
        localStorage.setItem('Authorization', 'Bearer ' + token);
        this.$axios.get(this.url + '/api/user', {
        }).then(res => {
          localStorage.setItem('IsLogin', 'true');
          _this.userInfo = res.data.data;
          _this.userToken = 'Bearer ' + token;
          _this.changeLogin({
            Authorization: _this.userToken,
            UserInfo: _this.userInfo
          });
          if (isWeiXin()) {
            this.reload()
          } else {
            window.close();
          }
        }).catch(err => {
          window.close();
        })
      }
    },

    bind () {
      let bind = this.$route.query.bind;
      let code = this.$route.query.code;
      // console.log('IndexBind:' + bind);
      // console.log(code);
      if (bind != '' && bind != undefined && bind != null) {
        // console.log(bind);
        localStorage.setItem('IsWechatBind', 'true');
        localStorage.setItem('bindTips', JSON.parse(bind));
        localStorage.setItem('bindCode', code);
        window.close();
      }
    },
    /**
     * 点击下滑一屏
     */
    clickScroll() {
      if (this.heightFlag) return
      // const orangeHeight = window.pageYOffset
      // let i = 0
      this.heightFlag = true
      this.interval = setInterval(() => {
        // 可视屏幕高度 - 导航高度 = 滚动高度
        let widwonHeight = window.screen.height - 140
        window.scrollTo(0, widwonHeight)// 第二个数表示滚动距离
        clearInterval(this.interval)
        this.heightFlag = false
        // i++
      }, 16.7)
    }
  },
  watch: {
    "$route": {
      handler (route) {
        this.login();
        this.bind();
      }
    }
  }
}
</script>

<style scoped lang="scss">
video::-webkit-media-controls-fullscreen-button {
  display: none;
}
/* ==================== 第一屏视频 start ==================== */

@media screen and (min-width: 751px) {
  .xg-home{margin-top:-84px;}
}
.xg-banner {
  font-size: 0;
  position: relative;
  // banner 上方文字
  .info {
    width: 100%;
    max-width: 1220px;
    padding-left: 20px;
    padding-right: 20px;
    pointer-events: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    .desc {
      font-size: 48px;
      color: #fff;
      line-height: 1.6;
      text-align: left;
      p {
        margin-bottom: 0;
        line-height: inherit;
      }
    }
    .xg-more {
      font-size: 18px;
      color: #0d6efd;
      line-height: 1.2;
      display: block;
      margin-top: 15px;
      text-align: left;
    }
  }
  // 视频样式
  video {
    width: 100%;
    height: 100vh;
    object-fit: cover;
    display: block;
  }
}
@media screen and (max-width: 751px) {
  .xg-banner .info .desc {
    font-size: 18px;
  }
  .xg-banner .info .more {
    font-size: 12px;
  }
  .xg-banner video {
    height: calc(100vh - 60px) !important;
  }
}
/* ==================== 第一屏视频 end ==================== */
</style>

<template>
  <!-- 课程 -->
  <section class="xg-course">
    <div class="xg-box">
      <xgTitle
          title="课 程"
          text1="犀光参数化平台，入门、进阶从零开始，学习如此简单"
      ></xgTitle>
      <xgTabs :tabsData="courseData" :tabsRoute="courseRoute" :key="key">
        <template #items="{ first }">
          <div class="itembox xg-row-4">
            <router-link
                :to="{ name: 'coursepage', params: { id: item.id } }"
                class="xg-col item"
                v-for="item in first.list"
                :key="item.id"
            >
              <!-- 封面图 -->
              <div class="image" :style="`background-image:url('${item.images}')`">
                <!-- <div class="xg-image">
                  <img
                    :src="item.images"
                    :alt="item.name"
                    :title="item.name"
                  />
                </div> -->
                <div class="info">{{ item.name }}</div>
              </div>
              <!-- 标题 -->
              <div class="xg-line-2 title">
                {{ item.name }}
              </div>
              <div class="bottom">
                <!-- 标签 -->
                <!--                <div class="tag">{{ first.name }}</div>-->
                <div class="cate" style="color: #0c63e4;font-weight: bold" v-if="item.purchase == 1">已加入</div>
                <div class="cate" style="color: red;font-weight: bold" v-else-if="item.price>0">{{ item.price }} 光子</div>
                <div class="cate" style="color: green;font-weight: bold" v-else>免费</div>
                <!-- 分类 -->
                <!--                  <div class="cate">{{ item.cate }}</div>-->
                <!-- 打折前的价格 -->
                <div class="last-price" v-if="item.market_price!=item.price">{{ item.market_price }} 光子</div>
              </div>
            </router-link>
          </div>
        </template>
      </xgTabs>
    </div>
  </section>
</template>

<script>
// 引入通用 标题 组件
import xgTitle from "./title.vue";
// 引入通用 选项卡 组件
import xgTabs from "./tabs.vue";
export default {
  name: "xg-course",
  data () {
    return {
      // 课程数据
      courseRoute: 'course',
      courseData:[],
      key:0,
    };
  },
  beforeMount () {
    this.getCarousel();
  },
  props: {

  },
  methods: {
    getCarousel () {
      this.$axios.get(this.url + '/api/home/<USER>', {}).then(res => {
        if (res.data.code === 200) {
          this.$nextTick(() => {
            this.courseData = res.data.data;
            this.key = this.courseData[0].name
          });
        }
      }).catch(err => {
      })
    },

  },
  components: {
    xgTitle,
    xgTabs
  },
};

</script>

<style lang="scss">
/* ==================== 课程 start ==================== */
.xg-course {
  background: url(../../assets/img/img01.jpg) no-repeat center top #fff;
  .item {
    display: block;
    &:hover {
      .info {
        opacity: 1;
      }
    }
    .image{height:180px;background-size:cover;background-position:center;}
  }
  .image {
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 10px;
    position: relative;
    .info {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #0d6efd;
      color: #fff;
      font-size: 18px;
      line-height: 1.6;
      padding: 20px;
      transition: all 0.4s;
      opacity: 0;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .title {
    font-size: 16px;
    line-height: 1.8;
    color: #000;
    text-align: justify;
  }
  .bottom {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
  }
  .tag {
    background: #a3acb6;
    padding: 3px 5px;
    color: #fff;
    font-size: 12px;
    border-radius: 2px;
  }
  .cate {
    font-size: 14px;
    color: #a3acb6;
    line-height: 1.2;
  }
  // 打折前的价格
  .last-price{
    font-size: 14px;
    color: #a9b2bb;
    text-decoration: line-through;
  }
}
@media screen and (min-width: 751px) {
  .xg-course {
    & > .xg-box {
      padding-top: 100px;
      padding-bottom: 80px;
    }
    .item:nth-child(4) ~ .item {
      margin-top: 45px;
    }
  }
}
@media screen and (max-width: 751px) {
  .xg-course {
    .title {
      font-size: 14px;
      line-height: 1.4;
      height: 2.8em;
    }
    .image {
      .info {
        display: none;
        font-size: 14px;
      }
    }
  }
}
/* ==================== 课程 end ==================== */
</style>

<template>
  <!-- 插件 -->
  <section class="xg-plug">
    <div class="xg-box">
      <xgTitle
        styleName="style01"
        title="插 件"
        text1="快来了解一下一些有趣插件，它能解放你的双手，为你创造巨大的生产力"
      ></xgTitle>
      <xgTabs
        className="xg-plug"
        styleName="style01"
        :tabsData="plugData"
        :tabsRoute="plugRoute"
        :key="key"
      >
        <template #items="{ first }">
          <div class="xg-flex itembox">
            <router-link
                :to="{ name: 'plug', params: { id: item.id } }"
                class="item"
                v-for="item in first.list"
                :key="item.id"
            >
              <!-- 背景图 -->
              <div
                class="item-bg"
                :style="`background-image:url(${item.images})`"
              ></div>
              <div class="item-content">
                <!-- 封面图 -->
                <div class="xg-image image">
                  <img
                    :src="item.images"
                    :alt="item.name"
                    :title="item.name"
                  />
                </div>
                <div class="info">
                  <!-- 名称 -->
                  <div class="xg-line-1 title">{{ item.name }}</div>
                  <!-- 描述 -->
                  <div class="xg-line-4 desc">
                    {{ item.intro }}
                  </div>
                </div>
                <div class="tag-group">
                  <!-- 作者 -->
                  <div class="author">{{ item.author }}</div>
                  <!-- 标签 -->
<!--                    <div class="tag">{{ item.type }}</div>-->
                </div>
              </div>
            </router-link>
          </div>
        </template>
      </xgTabs>
    </div>
  </section>
</template>

<script>
// 引入通用 标题 组件
import xgTitle from "./title";
// 引入通用 选项卡 组件
import xgTabs from "./tabs";
export default {
  name: "xg-plug",
  data () {
    return {
      plugRoute:'plug',
      plugData:[],
      key:0,
    };
  },
  beforeMount () {
    this.getPlugs();
  },
  props: {

  },
  methods: {
    getPlugs () {
      this.$axios.get(this.url + '/api/home/<USER>', {}).then(res => {
        if (res.data.code === 200) {
          this.plugData = res.data.data
          this.key = this.plugData[0].name
        }
      }).catch(err => {
      })
    },
  },
  components: {
    xgTitle,
    xgTabs
  },
};

</script>

<style lang="scss">
/* ==================== 插件 start ==================== */
.xg-plug{
    background:url(../../assets/img/img10.jpg) no-repeat center;
    background-size:cover;
    .item{
        position:relative;
        margin-right:2px;
        height:400px;
        display:flex;justify-content:center;align-items:center;padding-left:20px;padding-right:20px;overflow:hidden;
        width:20%;
        &:last-child{margin-right:0;}
        .item-bg{
            position:absolute;top:0;left:0;width:100%;height:100%;background-size:200%;background-position:center;
            -webkit-filter: blur(20px) contrast(1.3) brightness(.7);
            -moz-filter: blur(20px);
            -o-filter: blur(20px);
            -ms-filter: blur(20px);
            filter: blur(20px) contrast(1.3) brightness(.7);
        }
        .item-content{position:relative;z-index:3;}
        .image{display:block;width:90px;height:90px;margin:0 auto;border-radius:100%;border: solid 2px rgba(255, 255, 255, 0.5);margin-bottom:20px;flex-shrink:0;}
        .title{font-size:16px;color:#fff;line-height:1.2;text-align:center;
            &:before,
            &:after{content:"";display:block;width:18px;height:2px;background:#7f8992;margin:0 auto;}
            &:before{margin-bottom:20px;}
            &:after{display:none;margin-top:20px;}
        }
        .desc{font-size:13px;color:#f3f3f3;line-height:1.8;text-align:justify;height:7.2em;margin-bottom:25px;margin-top:20px;}
        .tag-group{
            display:flex;align-items:center;justify-content:center;
        }
        .tag,
        .author{padding:4px 7px;color:#fff;font-size:12px;display:inline-block;}
        .author{background:rgba(255,255,255,0.2);margin-right:10px;}
        .tag{background:#0d6efd;}

    }

}
@media screen and (min-width: 751px) {
    .xg-plug{
        &>.xg-box{padding-top:70px;padding-bottom:85px;}
        .item{
            // 动画
            .item-content{transform: translateY(80px);transition:all .4s;}
            .desc,
            .tag-group{visibility: hidden;transition:all .4s;opacity:0;}
            &:hover{
                .item-content{transform: translateY(0px);}
                .title:before{display:none;}
                .title:after{display:block;}
                .image{margin-bottom:20px;}
                .desc,
                .tag-group{visibility:visible;opacity:1;}
            }
        }
    }
}
@media screen and (max-width: 751px) {
    .xg-plug .itembox{flex-wrap:wrap;}
    .xg-plug .item{width:100%;margin-right:0;flex-flow:row;height:auto;padding:30px 15px;}
    .xg-plug .item .item-content{display:flex;align-items:center;}
    .xg-plug .item .image{margin-bottom:0;width:60px;height:60px;margin-right:10px;}
    .xg-plug .item .title{font-size:13px;text-align:left;}
    .xg-plug .item .title:before{display:none;}
    .xg-plug .item .desc{margin-top:10px;margin-bottom:0;-webkit-line-clamp: 2;line-height:1.6;height:3.2em;font-size:12px;}
    .xg-plug .item .tag-group{display:none;}
}
/* ==================== 插件 end ==================== */
</style>

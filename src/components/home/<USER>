<template>
  <!-- 课程 -->
  <section class="xg-course">
    <div class="xg-box">
      <xgTitle
        title="素 材"
        text1="掏个不停，总有一个适用得到"
      ></xgTitle>
      <xgTabs :tabsData="peripheryData" :tabsRoute="peripheryRoute" :key="key">
        <template #items="{ first }">
          <div class="itembox xg-row-4">
            <router-link
                :to="{ name: 'peripheryDetails', params: { id: goods.id } }"
                class="xg-col item"
                v-for="goods in first.list"
                :key="goods.id"
            >
              <!-- 封面图 -->
              <div class="image" :style="`background-image:url('${goods.cover}')`">
                <!-- <div class="xg-image">
                  <img
                    :src="goods.cover"
                    :alt="goods.name"
                    :title="goods.name"
                  />
                </div> -->
                <div class="info">{{ goods.name }}</div>
              </div>
              <!-- 标题 -->
              <div class="xg-line-2 title">
                {{ goods.name }}
              </div>
              <div class="bottom">
                <!-- 价格 -->
                <div class="price" v-if="goods.price>0">
                  <span v-if="goods.has_discount && goods.discount_price">
                    {{ goods.discount_price }} 光子
                    <span style="text-decoration: line-through; color: #999; margin-left: 5px; font-size: 12px;">
                      {{ goods.price }} 光子
                    </span>
                  </span>
                  <span v-else>
                    {{ goods.price }} 光子
                  </span>
                </div>
                <div class="price" style="color: green" v-else>免费</div>
                <!-- 数量 -->
                <div class="count">浏览量 {{ goods.sales_num }}</div>
              </div>
            </router-link>
          </div>
        </template>
      </xgTabs>
    </div>
  </section>
</template>

<script>
// 引入通用 标题 组件
import xgTitle from "./title";
// 引入通用 选项卡 组件
import xgTabs from "./tabs";
export default {
  name: "xg-periphery",
  data () {
    return {
      // 周边数据
      peripheryRoute:'periphery',
      peripheryData: [],
      key:0,

    };
  },
  props: {

  },
  beforeMount () {
    this.getDatas();
  },
  methods: {
    getDatas () {
      this.$axios.get(this.url + '/api/home/<USER>', {}).then(res => {
        if (res.data.code === 200) {
          this.$nextTick(() => {
            this.peripheryData = res.data.data;
            this.key = this.peripheryData[0].name
          });
        }
      }).catch(err => {
      })
    },
  },
  components: {
    xgTitle,
    xgTabs
  },
};

</script>

<style lang="scss" scoped>
/* ==================== 课程 start ==================== */
.xg-course {
  background: url(../../assets/img/img01.jpg) no-repeat center top #fff;
  .item {
    display: block;
    &:hover {
      .info {
        opacity: 1;
      }
    }
    .image{height:180px;background-size:cover;background-position:center;}
  }
  .image {
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 10px;
    position: relative;
    .info {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #0d6efd;
      color: #fff;
      font-size: 18px;
      line-height: 1.6;
      padding: 20px;
      transition: all 0.4s;
      opacity: 0;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .title {
    font-size: 16px;
    line-height: 1.8;
    color: #000;
    text-align: justify;
  }
  .bottom {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
  }
  .tag {
    background: #a3acb6;
    padding: 3px 5px;
    color: #fff;
    font-size: 12px;
    border-radius: 2px;
  }
  .cate {
    font-size: 14px;
    color: #a3acb6;
    line-height: 1.2;
  }
  .price{
    color: red;
    font-size: 14px;
    line-height: 1.2;
    font-weight: bold;
  }
  // 打折前的价格
  .last-price{
    font-size: 14px;
    color: #a9b2bb;
    text-decoration: line-through;
  }
  .count{
    color: #a9b2bb;
    font-size: 14px;
    line-height:1.2;
  }
}
@media screen and (min-width: 751px) {
  .xg-course {
    & > .xg-box {
      padding-top: 100px;
      padding-bottom: 80px;
    }
    .item:nth-child(4) ~ .item {
      margin-top: 45px;
    }
  }
}
@media screen and (max-width: 751px) {
  .xg-course {
    .title {
      font-size: 14px;
      line-height: 1.4;
      height: 2.8em;
    }
    .image {
      .info {
        display: none;
        font-size: 14px;
      }
    }
  }
}
/* ==================== 课程 end ==================== */
</style>

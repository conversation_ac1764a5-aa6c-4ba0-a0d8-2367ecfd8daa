<template>
  <div>
    <!-- 通用 选项卡 -->
    <div class="xg-tabs" :class="[styleName]">
      <!-- 选项 -->
      <div class="xg-tabs-header">
        <div class="xg-tabs-list">
          <div class="xg-tabs-wrap">
            <div
              class="xg-tabs-item"
              :class="{ active: active === i }"
              v-for="(item, i) in tabsData"
              :key="item.id"
              @click="tabsClick(i)"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
        <router-link :to="{ path: tabsRoute }" class="xg-tabs-more">更多 +</router-link>
      </div>
      <!-- 选项内容 -->
      <swiper
        ref="tabs_swiper"
        class="xg-tabs-swiper"
        @slideChange="tabChange"
        :options="swiperOption"
      >
        <swiper-slide v-for="first in tabsData" :key="first.id">
          <slot name="items" :first="first"></slot>
        </swiper-slide>
      </swiper>
    </div>
    <!-- 移动端查看更多按钮（pc不展示） -->
    <router-link :to="{ path: tabsRoute }" class="xg-btn pc-none">查看更多</router-link>
  </div>
</template>

<script>
export default {
  name: "",
  data () {
    return {
      active: 0,
      swiperOption: {
        clickable: true,
        spaceBetween: 10, // swiper-solid 间距
        autoHeight: true, //高度随内容变化
        observer: true,//修改swiper自己或子元素时，自动初始化swiper
        observeParents: true,//修改swiper的父元素时，自动初始化swiper
        // allowTouchMove: false,// 禁止滑动
        preventClicks: false,//默认true
      }
    };
  },
  props: {
    // 选项卡的数据
    tabsData: {
      type: Array,
      required: true,
    },
    // 选项卡样式
    styleName: {
      type: String,
    },
    // 选项卡样式
    tabsRoute: {
      type: String,
      required: true,
    }
  },
  methods: {
    tabChange () {
      this.active = this.$refs.tabs_swiper.swiper.realIndex
    },
    tabsClick (index) {
      this.$refs.tabs_swiper.swiper.slideTo(index)
    }
  },
  components: {
  },
};

</script>

<style lang="scss">
/* ==================== 通用标签页 start ==================== */
.xg-tabs {
  font-size: 0;
  padding-left: 0;
  padding-right: 0;
  .xg-tabs-header {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    margin-bottom: 25px;
  }
  .xg-tabs-wrap {
    position: relative;
    display: flex;
    overflow-x: scroll;
    max-width: 100%;
    align-items: center;
    justify-content: flex-start;
    // 隐藏滚动条
    &::-webkit-scrollbar {
      display: none;
    }
    // 兼容 Firefox
    scrollbar-width: none;
    // 兼容 IE 10 +
    -ms-overflow-style: none;
  }
  .xg-tabs-item {
    cursor: pointer;
    outline: none;
    flex-shrink: 0;
    margin-right: 60px;
    &:last-child {
      margin-right: 0;
    }
    font-size: 16px;
    color: #000;
    line-height: 1.2;
    padding-top: 15px;
    position: relative;
    transition: all 0.4s;
    &:before {
      content: '';
      display: block;
      width: 0%;
      height: 2px;
      background: #0d6efd;
      position: absolute;
      top: 0;
      left: 0;
      transition: all 0.4s;
    }
    &:hover,
    &.active {
      color: #0d6efd;
      font-weight: bold;
      &::before {
        width: 100%;
      }
    }
  }
  .xg-tabs-list {
    min-width: 0;
    flex-grow: 1;
  }
  .xg-tabs-more {
    color: #818e9d;
    font-size: 14px;
    line-height: 1.2;
    margin-left: 100px;
    flex-shrink: 0;
    &:hover {
      color: #0d6efd;
    }
  }
  // 样式一
  &.style01 {
    .xg-tabs-item,
    .xg-tabs-item:hover,
    .xg-tabs-item.active {
      color: #fff;
    }
    .xg-tabs-item:before {
      background: #fff;
    }
  }
}
@media screen and (max-width: 751px) {
  .xg-tabs {
    .xg-tabs-header {
      margin-bottom: 20px;
    }
    .xg-tabs-item {
      margin-right: 20px;
      font-size: 14px;
      padding-top: 10px;
      &:last-child {
        margin-right: 0;
      }
    }
    .xg-tabs-more {
      display: none;
    }
    &+.xg-btn{background:#0d6efd;padding:8px 20px;color:#fff;border-radius:5px;display:block;margin:0 auto;font-size:12px;margin-top:20px;}
  }
}
/* ==================== 通用标签页 end ==================== */
</style>

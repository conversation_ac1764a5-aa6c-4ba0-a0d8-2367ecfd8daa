<template>
  <!-- 课程 -->
  <section class="xg-news">
    <div class="xg-box">
      <xgTitle
        title="新闻资讯"
        text1="最新资讯、知识就在犀光RadiRhino"
      ></xgTitle>

      <div class="body">
        <!-- 推荐新闻 -->
        <div class="xg-left">
          <swiper
            class="news-swiper"
            ref="news-swiper"
            :options="swiperOption"
            :key="key"
          >
            <swiper-slide v-for="item in news" :key="item.id">
              <router-link
                :to="{ name: 'newsdetail', params: { id: item.id } }"
                class="item"
                :title="item.title"
              >
                <div class="xg-image image">
                  <img
                    :src="item.images"
                    :alt="item.title"
                    :title="item.title"
                  />
                </div>
                <div class="xg-flex bottom">
                  <div class="xg-line-1 title">
                    {{ item.title }}
                  </div>
                  <div class="time">{{ item.time }}</div>
                </div>
              </router-link>
            </swiper-slide>
            <!-- 前进后退 -->
            <div class="swiper-button-prev" slot="button-prev"></div>
            <div class="swiper-button-next" slot="button-next"></div>
          </swiper>
        </div>
        <!-- 置顶 -->
        <div class="xg-right">
          <router-link
            :to="{ name: 'newsdetail', params: { id: item.id } }"
            class="item"
            v-for="item in news"
            :key="item.id"
          >
            <!-- 时间 -->
            <div class="time">
              <!-- 日 -->
              <div class="day">{{ item.day }}</div>
              <!-- 年月 -->
              <div class="year">{{ item.year }}</div>
            </div>
            <div class="info">
              <!-- 标题 -->
              <div class="xg-line-1 title">
                {{ item.title }}
              </div>
              <!-- 描述 -->
              <div class="xg-line-2 desc">
                {{ item.content }}
              </div>
            </div>
          </router-link>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
// 引入通用 标题 组件
import xgTitle from "./title";
// 引入通用 选项卡 组件
import xgTabs from "./tabs";
export default {
  name: "xg-news",
  data () {
    return {
      news: [],
      // swiper配置
      swiperOption: {
        autoHeight: true,
        preventClicks: false,//默认true
        observer: true,//修改swiper自己或子元素时，自动初始化swiper
        observeParents: true,//修改swiper的父元素时，自动初始化swiper
        watchSlidesProgress: true,
        watchSlidesVisibility: true,
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        }
      },
      key:0
    };
  },
  props: {

  },
  methods: {
    getNews () {
      this.$axios.get(this.url + '/api/news', {}).then(res => {
        if (res.data.code === 200) {
          this.news = res.data.data.news
          this.key = this.news[0].id
        }
      }).catch(err => {
      })
    },

  },
  components: {
    xgTitle,
    xgTabs
  },
  beforeMount () {
    this.getNews();
  },
};

</script>

<style lang="scss">
/* ==================== 新闻资讯 start ==================== */
.xg-news {
  background: url(../../assets/img/img27.jpg) no-repeat center;
  background-size: cover;
  .xg-left {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    .item {
      display: block;
      position: relative;
    }
    .bottom {
      position: absolute;
      bottom: 0;
      left: 0;
      height: 60px;
      display: flex;
      align-items: center;
      padding-left: 20px;
      padding-right: 20px;
      background: rgba(0, 0, 0, 0.6);
      width: 100%;
    }
    .title {
      font-size: 16px;
      color: #fff;
      line-height: 1.2;
      min-width: 0;
      flex-grow: 1;
      text-align: left;
    }
    .time {
      margin-left: 30px;
      font-size: 14px;
      color: #9c9c9c;
      line-height: 1.2;
      flex-shrink: 0;
    }
    .swiper-button-prev,
    .swiper-button-next {
      position: absolute;
      top: 50%;
      border-radius: 4px;
      background: rgba(0, 0, 0, 0.5);
      cursor: pointer;
      outline: none;
      z-index: 99;
      width: 50px;
      height: 70px;
      display: flex;
      align-items: center;
      justify-content: center;
      transform: translateY(-50%);
      opacity: 1 !important;
      &:after {
        display: none;
      }
      &::before {
        content: '';
        display: block;
        width: 17px;
        height: 17px;
        border-top: 2px solid #fff;
        border-left: 2px solid #fff;
      }
    }
    .swiper-button-prev {
      left: 25px;
      &:before {
        transform: translateX(5px) rotate(-45deg);
      }
    }
    .swiper-button-next {
      right: 25px;
      &:before {
        transform: translateX(-5px) rotate(135deg);
      }
    }
  }
  .xg-right {
    min-width: 0;
    flex-grow: 1;
    .item {
      display: flex;
      align-items: center;
      border-radius: 14px;
      transition: all 0.4s;
      padding: 20px;
      margin-bottom: 10px;
      &:last-child {
        margin-bottom: 0;
      }
    }
    .time {
      border-radius: 8px;
      background: #a3acb6;
      display: flex;
      flex-flow: column;
      justify-content: center;
      align-items: center;
      color: #fff;
      text-align: center;
      flex-shrink: 0;
    }
    .day {
      font-size: 42px;
      font-weight: bold;
      line-height: 1;
      margin-bottom: 5px;
    }
    .year {
      font-size: 12px;
      line-height: 1.2;
    }
    .info {
      min-width: 0;
      flex-grow: 1;
    }
    .title {
      font-size: 16px;
      color: #000;
      line-height: 1.2;
      margin-bottom: 10px;
    }
    .desc {
      font-size: 14px;
      color: #818e9d;
      line-height: 1.6;
      height: 3.2em;
      text-align:left;
    }
  }
}
@media screen and (min-width: 751px) {
  .xg-news {
    & > .xg-box {
      padding-top: 70px;
      padding-bottom: 90px;
    }
    .body {
      display: flex;
      align-items: center;
    }
    .xg-left {
      width: 560px;
      flex-shrink: 0;
      margin-right: 30px;
    }
    .xg-right {
      .item {
        &:hover {
          background: #fff;
          box-shadow: 0px 5px 22px 0px rgba(4, 0, 0, 0.1);
          .time {
            background: #0d6efd;
          }
        }
        .time {
          width: 80px;
          height: 80px;
          margin-right: 20px;
        }
      }
    }
  }
}
@media screen and (max-width: 751px) {
  .xg-news .xg-left {
    margin-bottom: 10px;
  }
  .xg-news .xg-left .time {
    display: none;
  }
  .xg-news .xg-left .bottom {
    height: 45px;
    line-height: 45px;
  }
  .xg-news .xg-left .title {
    font-size: 14px;
  }
  .xg-news .xg-left .swiper-button-prev,
  .xg-news .xg-left .swiper-button-next {
    width: 40px;
    height: 50px;
  }
  .xg-news .xg-left .swiper-button-prev {
    left: 10px;
  }
  .xg-news .xg-left .swiper-button-next {
    right: 10px;
  }
  .xg-news .xg-right .item {
    padding: 15px 0;
    margin-bottom: 0;
  }
  .xg-news .xg-right .time {
    padding: 5px;
    margin-right: 10px;
  }
  .xg-news .xg-right .day {
    font-size: 30px;
  }
  .xg-news .xg-right .title {
    font-size: 14px;
    margin-bottom: 5px;
  }
  .xg-news .xg-right .desc {
    font-size: 12px;
  }
}
/* ==================== 新闻资讯 end ==================== */
</style>

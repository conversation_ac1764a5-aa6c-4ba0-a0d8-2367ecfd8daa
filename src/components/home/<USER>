<template>
  <!-- 周边 -->
  <section class="xg-periphery">
    <div class="xg-box">
      <xgTitle
        title="周 边"
        text1="周边商品，掏个不停，总有一个适用得到"
      ></xgTitle>
      <xgTabs :tabsData="peripheryData" :tabsRoute="peripheryRoute" :key="key">
        <template #items="{ first }">
          <div class="xg-row-4 itembox">
            <router-link
                :to="{ name: 'peripheryDetails', params: { id: goods.id } }"
                class="xg-col item"
                v-for="goods in first.list"
                :key="goods.id"
            >
              <div class="xg-image image">
                <img :src="goods.cover" :alt="goods.name" :title="goods.name" />
              </div>
              <div class="info">
                <!-- 标题 -->
                <div class="title">{{ goods.name }}</div>
                <div class="other">
                  <!-- 价格 -->
                  <div class="price">
                    ￥<strong>{{ goods.price }}</strong>
                  </div>
                  <!-- 数量 -->
                  <div class="count">已成交{{ goods.sales_num }}</div>
                </div>
              </div>
            </router-link>
          </div>
        </template>
      </xgTabs>
    </div>
  </section>
</template>

<script>
// 引入通用 标题 组件
import xgTitle from "./title";
// 引入通用 选项卡 组件
import xgTabs from "./tabs";
export default {
  name: "xg-periphery",
  data () {
    return {
      // 周边数据
      peripheryRoute:'periphery',
      peripheryData: [],
      key:0,

    };
  },
  props: {

  },
  beforeMount () {
    this.getDatas();
  },
  methods: {
    getDatas () {
      this.$axios.get(this.url + '/api/home/<USER>', {}).then(res => {
        if (res.data.code === 200) {
          this.$nextTick(() => {
            this.peripheryData = res.data.data;
            this.key = this.peripheryData[0].name
          });
        }
      }).catch(err => {
      })
    },
  },
  components: {
    xgTitle,
    xgTabs
  },
};

</script>

<style lang="scss">
/* ==================== 周边 start ==================== */
.xg-periphery {
  background: url(../../assets/img/img17.jpg) no-repeat center;
  background-size: cover;
  .item {
    position: relative;
    border-radius: 6px;
    overflow: hidden;
    .info {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      padding: 20px;
      background: rgba(0, 0, 0, 0.5);
      color: #fff;
      display: flex;
      flex-flow: column;
      justify-content: space-between;
      transition: all 0.4s;
    }
    .title {
      font-size: 18px;
      line-height: 1.6;
      width: 100%;
      text-align: left;
    }
    .other {
      align-self: flex-end;
    }
    .price {
      font-size: 14px;
      margin-bottom: 5px;
      strong {
        font-weight: bold;
        font-size: 26px;
        line-height: 1;
      }
    }
    .count {
      font-size: 12px;
      color: #929ba5;
      line-height: 1.2;
      text-align: right;
    }
  }
}
@media screen and (min-width: 751px) {
  .xg-periphery {
    & > .xg-box {
      padding-top: 70px;
      padding-bottom: 80px;
    }
    .item {
      &:hover {
        .info {
          background: #0d6efd;
          align-items: center;
          justify-content: center;
        }
        .title {
          text-align: center;
          margin-bottom: 10px;
        }
        .other {
          align-self: center;
        }
        .count {
          display: none;
        }
      }
    }
  }
}
@media screen and (max-width: 751px) {
  .xg-periphery {
    .item .info {
      padding: 10px;
    }
    .item .title {
      font-size: 12px;
    }
    .item .price strong {
      font-size: 18px;
    }
  }
}
/* ==================== 周边 end ==================== */
</style>

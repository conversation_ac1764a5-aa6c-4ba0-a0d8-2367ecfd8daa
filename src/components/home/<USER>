<template>
  <!-- Ai作品集 -->
  <section class="xg-course">
    <div class="xg-box">
      <xgTitle
        title="AI作品集展示"
        text1="AI建筑作品，AI幕墙作品集合就在犀光"
      ></xgTitle>

        <xgTabs :tabsData="galleryData" :tabsRoute="galleryRoute" :key="key">
        <template #items="{ first }">
          <div class="itembox xg-row-4">
            <div
                class="xg-col item"
                v-for="goods in first.list"
                :key="goods.id"
                @click="goto"
            >
              <!-- 封面图 -->
              <div class="image" :style="`background-image:url('${goods.cover}')`">
                <div class="info">{{ goods.name }}</div>
              </div>
              <div class="xg-line-2 title">
                {{ goods.name }}
              </div>
            </div>
          </div>
        </template>
      </xgTabs>
    </div>
  </section>
</template>

<script>
// 引入通用 标题 组件
import xgTitle from "./title";
// 引入通用 选项卡 组件
import xgTabs from "./tabs";
export default {
  name: "ai-gallery",
  data () {
    return {
      galleryRoute:'ai/gallery',
      galleryData: [],
      key:0,
    };
  },
  props: {

  },
  beforeMount () {
    this.getDatas();
  },
  methods: {
    getDatas () {
      this.$axios.get(this.url + '/api/home/<USER>', {}).then(res => {
        if (res.data.code === 200) {
          this.$nextTick(() => {
            this.galleryData = res.data.data;
            this.key = this.galleryData[0].name
          });
        }
      }).catch(err => {
      })
    },
    goto() {
      this.$router.push("/ai/gallery");
    }
  },
  components: {
    xgTitle,
    xgTabs
  },
};

</script>

<style lang="scss" scoped>
/deep/ .xg-tabs-item{
  color:#fff
}
.xg-course {
    background:url(../../assets/img/img10.jpg) no-repeat center;
    background-size:cover;
  .item {
    display: block;
    &:hover {
      .info {
        opacity: 1;
      }
    }
    .image{height:180px;background-size:cover;background-position:center;}
  }
  .image {
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 10px;
    position: relative;
    .info {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background:rgba(0,0,0,.6);
      color: #fff;
      font-size: 14px;
      line-height: 1.6;
      padding: 20px;
      transition: all 0.4s;
      opacity: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
  }
  .title {
    font-size: 16px;
    line-height: 1.8;
    color: #fff;
    text-align: justify;
  }
  .bottom {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
  }
  .tag {
    background: #a3acb6;
    padding: 3px 5px;
    color: #fff;
    font-size: 12px;
    border-radius: 2px;
  }
  .cate {
    font-size: 14px;
    color: #a3acb6;
    line-height: 1.2;
  }
  .price{
    color: red;
    font-size: 14px;
    line-height: 1.2;
    font-weight: bold;
  }
  // 打折前的价格
  .last-price{
    font-size: 14px;
    color: #a9b2bb;
    text-decoration: line-through;
  }
  .count{
    color: #a9b2bb;
    font-size: 14px;
    line-height:1.2;
  }
}
@media screen and (min-width: 751px) {
  .xg-course {
    & > .xg-box {
      padding-top: 100px;
      padding-bottom: 80px;
    }
    .item:nth-child(4) ~ .item {
      margin-top: 45px;
    }
  }
}
@media screen and (max-width: 751px) {
  .xg-course {
    .title {
      font-size: 14px;
      line-height: 1.4;
      height: 2.8em;
    }
    .image {
      .info {
        display: none;
        font-size: 14px;
      }
    }
  }
}
</style>

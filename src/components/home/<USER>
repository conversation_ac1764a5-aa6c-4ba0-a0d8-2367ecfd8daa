<template>
  <!-- 通用标题 -->
  <div class="xg-title" :class="[styleName]">
    <div class="cn">{{ title }}</div>
    <div class="desc">{{ text1 }}</div>
  </div>
</template>

<script>
export default {
  name: "xg-title",
  data() {
    return {};
  },
  props: {
    //  主标题
    title: { type: String},
    // 副标题
    text1: { type: String},
    // 标题样式
    styleName: { type: String },
  },
};
</script>

<style lang="scss" scoped>
/* ====================通用标题 start ==================== */
.xg-title {
  text-align: center;
  overflow: hidden;
  margin-bottom: 65px;
  .cn {
    font-size: 38px;
    line-height: 1.2;
    color: #0d6efd;
    margin-bottom: 20px;
  }
  .desc {
    font-size: 16px;
    color: #818e9d;
    line-height: 1.6;
  }
  &.style01 {
    .cn {
      color: #fff;
    }
    .desc {
      color: #97a5b1;
    }
  }
}
@media screen and (min-width: 751px) {
  .xg-title {
  }
}
@media screen and (max-width: 751px) {
  .xg-title {
    margin-bottom: 30px;
    .cn {
      font-size: 22px;
      margin-bottom: 10px;
    }
    .desc {
      font-size: 14px;
    }
  }
}
/* ====================通用标题 end ==================== */
</style>

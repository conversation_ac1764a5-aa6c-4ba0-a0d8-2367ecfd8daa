<template>
  <div class="information">
    <!-- 欢迎 -->
    <div class="welcome">
      <!--      <span>今日登录 +5 光子</span>-->
    </div>
    <div class="main" v-loading="loadings">
      <div
        class="head"
        v-for="item in items"
        :key="item.id"
        @click="go(item.fans_info)"
      >
        <!-- 头像 -->
        <div class="avatar-box">
          <img
            v-if="item.fans_info.avatar"
            :src="item.fans_info.avatar"
            class="avatar"
          />
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </div>
        <div class="userinfo-box">
          <div class="item item1">
            {{ item.fans_info.name }}
          </div>
        </div>
      </div>

      <el-pagination
        class="xg-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="page"
        :page-size="limit"
        layout="prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { mapMutations } from "vuex";

export default {
  name: "userfans",
  data() {
    return {
      loadings: false,
      isfans: false,
      isfansShow: false,
      fansNum: 0,
      attentionNum: 0,
      uid: "",
      items: [],
      activeName: "course",
      user: {},
      value: 0,

      sexOptions: [],

      // 表单验证
      rules: {},
      limit: 12,
      page:1,
      total: 0,
    };
  },

  mounted() {
    this.uid = this.$route.params.id;
    this.getOss();
    this.getUserFansList();
  },
  methods: {
    ...mapMutations(["changeLogin"]),

    handleSizeChange(val) {
      this.limit = val;
      console.log(val);
    },
    handleCurrentChange(val) {
      console.log(val);
      this.page = val;
      this.getUserFansList();
    },
    go(user) {
      this.$router.push("/userfans/" + user.id);
    },
    getOss() {
      this.$axios
        .get(this.url + "/api/oss", {})
        .then((res) => {
          console.log(res);
          if (res.data.code == 200) {
            this.domain = res.data.data.oss.DoMain;
            localStorage.setItem("oss", JSON.stringify(res.data.data.oss));
          }
        })
        .catch((err) => {});
    },

    getUserFansList() {
      this.loadings = true;
      this.$axios
        .get(this.url + "/api/getUserFansList?uid=" + this.uid+'&page='+this.page+'&limit='+this.limit, {})
        .then((res) => {
      this.loadings = false;
          console.log(res, "res--------");
          if (res.data.code !== 200) {
          } else {
            console.log(res);
            this.items = [...res.data.data.list];
            this.total = res.data.data.total
          }
        })
        .catch((err) => {});
    },
  },
};
</script>

<style scoped lang="scss">
.information {
  text-align: left;
  .welcome {
    background: #a3acb6;
    height: 46px;
    display: flex;
    align-items: center;
    padding-left: 20px;
    padding-right: 20px;
    .text {
      color: #fff;
      font-size: 13px;
      line-height: 1.2;
      margin-right: 14px;
    }
    span {
      background: #f8e715;
      font-size: 12px;
      color: #000;
      padding-left: 10px;
      padding-right: 10px;
      border-radius: 100px;
      font-weight: bold;
    }
  }
  .main {
    padding: 40px;
  }
  .head {
    border-bottom: 1px solid #dbdddf;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    .avatar-box {
      position: relative;
      width: 54px;
      height: 54px;
      border-radius: 100%;
      background-color: #dddddd;
      overflow: hidden;
      .avatar-uploader {
        width: 100%;
        height: 100%;
        /deep/ .el-upload {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .name {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 30px;
        line-height: 30px;
        text-align: center;
        color: #fff;
        text-align: center;
        font-size: 12px;
        background: rgba(0, 0, 0, 0.7);
      }
    }
    .userinfo-box {
      box-sizing: border-box;
      font-size: 21px;
      color: #000;
      .item {
        margin-left: 80px;
        &.item1 > .number {
          color: #0d6efd;
        }
      }
      .number {
        font-size: 21px;
        color: #000;
        font-weight: bold;
        line-height: 1.2;
        margin-bottom: 20px;
      }
      .text {
        font-size: 14px;
        color: #818e9d;
        line-height: 1.2;
      }
    }
    .userinfo-desc {
      margin-left: 50px;
      height: 60px;
      overflow: auto;
      width: 50%;
      color: #999999;
      font-size: 14px;
      line-height: 20px;
    }
    .info {
      text-align: left;
      display: flex;
      align-items: center;
      .item {
        margin-left: 80px;
        &.item1 > .number {
          color: #0d6efd;
        }
      }
      .number {
        font-size: 21px;
        color: #000;
        font-weight: bold;
        line-height: 1.2;
        margin-bottom: 20px;
      }
      .text {
        font-size: 14px;
        color: #818e9d;
        line-height: 1.2;
      }
    }
  }
  .basic-information {
    >>> .userdesc-home {
      margin: 50px;
      border: 1px dashed #dddddd;
      padding: 30px;
      box-sizing: border-box;
    }
    >>> .list {
      min-height: 300px;
      .itembox {
        padding-top: 40px;
        box-sizing: border-box;
        .image {
          width: 100%;
          min-height: 180px;
          background-color: #eeeeee;
          .bg-image {
            width: 100%;
            max-height: 180px;
            overflow: hidden;
            img {
              width: 100%;
            max-height: 180px;
              height: auto;
            }
          }
        }
      }
      .list-item {
        display: flex;
        align-items: center;
        margin-bottom: 30px;
        padding: 20px 20px;
        &:last-child {
          margin-bottom: 0;
        }
        .image {
          width: 100px;
          height: auto;
          border-radius: 5px;
          overflow: hidden;
          flex-shrink: 0;
          margin-right: 20px;
        }
        .info {
          min-width: 0;
          flex-grow: 1;
        }
        .title {
          font-size: 16px;
          color: #000;
          line-height: 1.6;
          height: 3.2em;
          margin-bottom: 15px;
        }
        .progress-rate {
        }
        .text {
          justify-content: space-between;
          width: 100%;
          margin-bottom: 10px;
          font-size: 14px;
          line-height: 1.2;
          .text1 {
            color: #687d93;
          }
          .text2 {
            color: #67c23a;
          }
        }
      }
    }
    .title {
      font-size: 14px;
      color: #000;
      line-height: 1.2;
      margin-bottom: 20px;
      font-weight: bold;
    }
    .tip {
      background: #f4f4f4;
      height: 40px;
      line-height: 40px;
      padding-left: 20px;
      padding-right: 20px;
      font-size: 12px;
      color: #818e9d;
      margin-bottom: 40px;
      text-align: left;
    }
    .xg-form /deep/ {
      .el-input__inner {
        height: 44px;
        line-height: 44px;
        border-radius: 4px;
        padding: 0 15px;
      }
      .el-select {
        width: 100%;
      }
      .el-form-item__label {
        color: #818e9d;
      }
      .el-button {
        width: 120px;
        height: 46px;
        line-height: 46px;
        background-color: #f4f4f4;
        border-radius: 4px;
        text-align: center;
        font-size: 14px;
        color: #818e9d;
        border: 0;
        padding: 0;
        &:hover {
          background: #0d6efd;
          color: #fff;
        }
      }
      .intro textarea {
        height: 140px !important;
      }
    }
  }
}

@media screen and (max-width: 751px) {
  .information .welcome {
    padding: 8px 10px;
    font-size: 12px;
    height: auto;
    flex-wrap: wrap;
    .text {
      margin-right: 0;
      line-height: 1.6;
    }
    span {
      display: block;
      margin-top: 5px;
    }
  }
  .information .main {
    padding: 15px;
  }
  .information .head {
    flex-wrap: wrap;
    padding-bottom: 20px;
    margin-bottom: 20px;
  }
  .information .head .avatar-box {
    width: 120px;
    height: 120px;
    display: block;
    margin: 0 auto 20px;
  }
  .information .head .info {
    width: 100%;
  }
  .information .head .info .item {
    margin-left: 0;
    width: 32%;
  }
  .information .head .info .item:nth-child(3n-1) {
    margin-left: 2%;
    margin-right: 2%;
  }
  .information .head .info .number {
    font-size: 18px;
    margin-bottom: 5px;
  }
  .information .basic-information .tip {
    height: auto;
    line-height: 1.6;
    padding: 8px 10px;
    font-size: 12px;
    margin-bottom: 20px;
  }
  .information .xg-form /deep/ .el-form-item {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    margin-bottom: 5px;
    &:before,
    &:after {
      display: none;
    }
    .el-form-item__label {
      width: 100% !important;
      text-align: left;
      font-size: 12px;
    }
    .el-form-item__content {
      margin-left: 0 !important;
      width: 100%;
    }
    .el-input__inner {
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      padding-right: 10px;
    }
    .el-button {
      width: 100%;
      height: 36px;
      line-height: 36px;
      padding: 0;
      font-size: 12px;
      margin-top: 10px;
    }
  }
}
</style>

<template>
  <div class="xg-ny user-index">
    <div class="xg-box">
      <el-container class="user-body">
        <!-- 左侧选择 -->

        <!--      右边内容-->
        <el-main class="xg-main">
          <router-view></router-view>
        </el-main>
      </el-container>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      pathActive: "/user/info",
      isCollapse: false,
      windowWidth: document.documentElement.clientWidth, //实时屏幕宽度
      windowHeight: document.documentElement.clientHeight, //实时屏幕高度
    };
  },
  mounted() {
    if (this.windowWidth < 900) {
      this.isCollapse = true;
    }
    if (this.windowWidth > 900) {
      this.isCollapse = false;
    }

    this.pathActive = this.$route.path;
  },
  methods: {
    handleOpen(key, keyPath) {
      // console.log(key, keyPath);
    },
    handleClose(key, keyPath) {
      // console.log(key, keyPath);
    },
  },
  watch: {
    $route: {
      handler(route) {
        this.pathActive = route.path;
      },
    },
  },
};
</script>

<style scoped lang="scss">
.user-index{
  .aside{background:#fff;margin-right:20px;width:auto!important;padding-top:15px;padding-bottom:15px;overflow:visible;}
  .openBtn{
    display:block;width:100%;text-align:right;
    cursor: pointer;font-size: 24px;color:#0d6efd;margin-bottom:20px;
    &.el-icon-s-unfold{text-align:center;}
    &.el-icon-close{font-weight:bold;padding-right:18px;}
  }
  .el-menu{
    border-right:0;text-align:left;width:200px;
    .el-menu-item{
      position:relative;
      padding-left:50px!important;padding-right:50px;transition:all .4s;
      &.is-active{
        background:#ecf5ff;
        .text{color:#000;}
      }
      .text{transition:all .4s;opacity:1;display:inline-block;z-index:9;color:#687d93;}
    }
    &.el-menu--collapse{
      width:80px;
      .el-menu-item{
        padding-left:0!important;padding-right:0;width:100%;text-align:center;
        &.is-active{
          .text{color:#000;}
        }
        .xg-icon{position:relative;z-index:9;margin-right:0;}
        .text{opacity:0;background:#ecf5ff;transform:translateX(-40px);visibility:hidden;position: absolute;left:0;top:0;padding-left:35px;padding-right:35px;}
        &:hover{
          .xg-icon{background-color:#ecf5ff;}
          .text{opacity:1;transform:translateX(80px);visibility:visible;}
        }
      }
    }
  }
  .xg-icon{
    display:inline-block;width:20px;height:20px;
    background:url(../../assets/img/user-menu-icon.png) no-repeat;background-position-x:-2px;margin-right:10px;transition:all .4s;
    &.xg-icon1{background-position-y: 2px;}
    &.xg-icon2{background-position-y: -50px;}
    &.xg-icon3{background-position-y: -104px;}
    &.xg-icon4{background-position-y: -158px;}
    &.xg-icon5{background-position-y: -212px;}
    &.xg-icon6{background-position-y: -266px;}
    &.xg-icon7{background-position-y: -320px;}
    &.xg-icon8{background-position-y: -374px;}
  }
  .xg-main{background:#fff;padding:0;}

}


@media screen and (max-width: 751px) {
  .user-index{
    .aside{padding:0;margin-right:10px;}
    .openBtn{display:none;}
    .el-menu.el-menu--collapse{width:50px;}
    .el-menu.el-menu--collapse .el-menu-item:hover .text{transform:translateX(50px);padding-left:20px;padding-right:20px;font-size:12px;}
  }
}
</style>
<style lang="scss">
.user-index{
  // 通用选项卡样式
  .user-head {
    display: flex;
    align-items: center;
    border-bottom: 20px solid #f4f4f4;
    padding-left: 25px;
    padding-right: 25px;
    justify-content: space-between;
    .title {
      height: 60px;
      display: flex;
      align-items: center;
      .icon {
        width: 20px;
        height:20px;
        background-image:url(../../assets/img/user-menu-icon.png);
        background-repeat:no-repeat;
        flex-shrink: 0;
      }
      .text {
        font-size: 14px;
        color: #0d6efd;
        padding-left: 8px;
        font-weight:bold;
      }
    }
    .search /deep/ {
      .el-input__inner {
        width: 260px;
      }
    }
  }
  .user-tabs {
    .el-tabs__header {
      margin-bottom:0;
      background: #f4f4f4;
    }
    .el-tabs__item {
      padding-left: 20px!important;
      padding-right: 20px!important;
      color: #000 !important;
      position: relative;
      height: 50px;
      line-height: 50px;
      &::after {
        content: "";
        display: block;
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        height: 2px;
        background: #0d6efd;
        transition: all 0.4s;
        width: 0;
      }
      &.is-active {
        color: #0d6efd !important;
      }
      &.is-active:after {
        width: 100%;
      }
    }
    .el-tabs__nav-wrap::after {
      background-color: #f4f4f4;
    }
    .el-tabs__active-bar {
      display: none;
    }
  }
  // 对话框样式
  .el-dialog{
    text-align:left;
    padding:25px;background:#fff;width:560px;height:450px;box-shadow: 0px 0px 30px 0px
		rgba(4, 0, 0, 0.3);border-radius: 6px;
    .el-dialog__header{padding:0;position:relative;margin-bottom:20px;}
    .el-dialog__title{font-size:18px;color:#0084ff;font-weight:bold;}
    .el-dialog__headerbtn{position:absolute;font-size:24px;right:0;top:-10px;}
    .el-dialog__body{padding:0;}
    .el-button{width:80px;height:32px;line-height:32px;border:1px solid #bdbdbd;background:#fff;padding:0;border-radius:0;color:#bdbdbd;font-size:14px;text-align:center;}
    .el-button--primary{background-color:#0d6efd;border-color:#0d6efd;color:#fff;}
  }
}

@media screen and (max-width: 751px) {
  .user-index{
    .user-head{padding:0;flex-wrap:wrap;height:auto;line-height:inherit;border-bottom:0;margin-bottom:20px;
      .title{height:auto;line-height:inherit;width:100%;}
      .search{margin-top:10px;}
      .search /deep/ .el-input__inner{width:170px;}
    }
    .el-tabs .el-tabs__header{margin-bottom:15px;}
    .el-tabs__nav{width:100%;}
    .el-tabs .el-tabs__item{height:40px;line-height:40px;font-size:12px;width:50%;padding-left:10px!important;padding-right:10px!important;}
    //
    .el-dialog{
      width:90%;height:410px;
    }
  }
}
</style>

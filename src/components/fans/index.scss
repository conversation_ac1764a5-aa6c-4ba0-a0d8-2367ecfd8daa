$color-main: #409EFF;
$color-success: #67C23A;
$color-warning: #E6A23C;
$color-danger: #F56C6C;
$color-info: #909399;
$color-yanse: #24416b;

$text-main: #303133;
$text-normal: #606266;
$text-minor: #909399;  //次要文字
$text-placeholder: #C0C4CC;
$text-333: #333;

$border-first: #DCDFE6;
$border-second: #E4E7ED;
$border-third: #EBEEF5;
$border-fourth: #F2F6FC;

$content-bg-color: #fff;

.btn-success{
  background: none;
  border: none;
  color: #333333;
}





/* ==================== 内页 - 课程列表 start ==================== */
.xg-ny-course {
  // 选项卡内容
  .item {
    cursor: pointer;
    &:hover {
      .info {
        transform: scale(1);
        opacity: 1;
      }
    }
  }
  .image {
    border-radius: 6px;
    overflow: hidden;
    position: relative;
    margin-bottom: 10px;
    .info {
      background: #0d6efd;
      color: #fff;
      font-size: 14px;
      line-height: 1.8;
      text-align: justify;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-left: 20px;
      padding-right: 20px;
      transition: all 0.4s;
      transform: scale(1.2);
      opacity: 0;
      text-align: center;
    }
  }
  .name {
    font-size: 16px;
    color: #000;
    line-height: 1.6;
    text-align: justify;
    margin-bottom: 10px;
    height: 3.2em;
  }
  .type {
    font-size: 14px;
    color: #008000;
    line-height: 1.2;
    text-align: left;
    &.type1 {
      color: #ff0000;
      font-weight: bold;
    }
  }
}
@media screen and (min-width: 751px) {
  .xg-ny-course {
    .itembox > .item:nth-child(4) ~ .item {
      margin-top: 45px;
    }
  }
}
@media screen and (max-width: 751px) {
  .xg-ny-course .name {
    font-size: 14px;
    line-height: 1.4;
    height: 2.8em;
  }
  .xg-ny-course .image .info {
    padding-left: 15px;
    padding-right: 15px;
    font-size: 12px;
    line-height: 1.6;
  }
}
/* ==================== 内页 - 课程列表 end ==================== */

<template>
  <div class="information">
    <!-- 欢迎 -->
    <div class="welcome">
      <!--      <span>今日登录 +5 光子</span>-->
    </div>
    <div class="main">
      <div class="head">
        <!-- 头像 -->
        <div class="avatar-box">
          <img v-if="user.avatar" :src="user.avatar" class="avatar" />
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </div>
        <!--  -->

        <div class="userinfo-box">
          <div class="item item1">
            {{ user.name }}
          </div>
          <div class="item item1">
            <span class="text">{{ fansNum }}</span>
            <span class="text">粉丝</span>
            <span class="text"> | </span>
            <span class="text">{{ attentionNum }}</span>
            <span class="text">关注</span>
          </div>
          <div class="item item1">
            <el-button
              v-if="!isfans && isfansShow"
              @click="userGuanzhu(1)"
              type="primary"
              size="mini"
              >关注</el-button
            >
            <el-button
              v-if="isfans && isfansShow"
              @click="userGuanzhu(2)"
              type="danger"
              size="mini"
              >取消关注</el-button
            >
          </div>
        </div>
        <div class="userinfo-desc">
          <div v-html="user.intro"></div>
        </div>
      </div>
      <!-- 基本信息 -->
      <div class="basic-information">
        <el-tabs
          v-model="activeName"
          class="user-tabs"
          @tab-click="handleClick"
        >
          <!-- 个人介绍 -->

          <!-- 我的收藏 -->
          <el-tab-pane label="教学课程" name="course" class="tab-pane2">
            <div class="list" v-loading="loadings">
              <!-- <div
                class="list-item"
                v-for="(item, index) in items"
                :key="index"
              >
                <div class="xg-image image">
                  <img
                    v-if="item.cover"
                    :src="domain + item.cover"
                    fit="cover"
                    alt=""
                  />
                  <img v-else :src="domain + item.images" fit="cover" alt="" />
                </div>
                <div class="info">
                  <el-row>
                    <el-col :span="24"
                      ><div class="title" style="text-align: left">
                        {{ item.name }}
                      </div></el-col
                    >
                  </el-row>
                  <el-row>
                    
                  </el-row>
                </div>
              </div> -->
              <div class="xg-row-4 itembox">
                <router-link
                  class="xg-col item"
                  tag="a"
                  target="_blank"
                  :to="{ name: 'coursepage', params: { id: course.id } }"
                  v-for="(course, index) in items"
                  :key="index"
                >
                  <div class="image">
                    <div class="bg-image">
                      <img :src="domain + course.images" alt="" />
                    </div>
                  </div>
                  <div class="xg-line-2 name">
                    {{ course.name }}
                  </div>
                </router-link>
              </div>
            </div>
            <!-- 分页 -->
            <el-pagination
              background
              class="xg-pagination"
              layout="prev, pager, next,jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="page"
              :page-size="pagesize"
              :total="total"
            >
            </el-pagination>
          </el-tab-pane>
          <el-tab-pane label="插件作品" name="plug" class="tab-pane2">
            <div class="list" v-loading="loadings">
              <div class="xg-row-4 itembox">
                <router-link
                  class="xg-col item"
                  tag="a"
                  target="_blank"
                  :to="{ name: 'plug', params: { id: plug.id } }"
                  v-for="(plug, itemId) in items"
                  :key="itemId"
                >
                  <div class="image">
                    <div class="bg-image">
                      <img :src="domain + plug.images" alt="" />
                    </div>
                  </div>
                  <div class="xg-line-2 name">
                    {{ plug.name }}
                  </div>
                </router-link>
              </div>
            </div>
            <!-- 分页 -->
            <el-pagination
              background
              class="xg-pagination"
              layout="prev, pager, next,jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="page"
              :page-size="pagesize"
              :total="total"
            >
            </el-pagination>
          </el-tab-pane>

          <el-tab-pane label="素材作品" name="goods" class="tab-pane2">
            <div class="list" v-loading="loadings">
              <div class="xg-row-4 itembox">
                <router-link
                  class="xg-col item"
                  tag="a"
                  target="_blank"
                  :to="{ name: 'peripheryDetails', params: { id: goods.id } }"
                  :key="index"
                  v-for="(goods, index) in items"
                >
                  <div class="image">
                    <div class="bg-image">
                      <img :src="domain + goods.images" alt="" />
                    </div>
                  </div>
                  <div class="xg-line-2 name">
                    {{ goods.name }}
                  </div>
                </router-link>
              </div>
            </div>
            <!-- 分页 -->
            <el-pagination
              background
              class="xg-pagination"
              layout="prev, pager, next,jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="page"
              :page-size="pagesize"
              :total="total"
            >
            </el-pagination>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import { mapMutations } from "vuex";

export default {
  name: "userfans",
  data() {
    return {
      loadings: false,
      isfans: false,
      isfansShow: false,
      fansNum: 0,
      attentionNum: 0,
      uid: "",
      page: 1,
      pagesize: 10,
      total: 0,
      items: [],
      activeName: "course",
      user: {},
      value: 0,

      sexOptions: [],

      // 表单验证
      rules: {},
    };
  },

  mounted() {
    this.uid = this.$route.params.id;
    this.getOss();
    this.getUserInfo();
    this.getUserFansInfo();
    this.getData();
  },
  methods: {
    ...mapMutations(["changeLogin"]),

    getOss() {
      this.$axios
        .get(this.url + "/api/oss", {})
        .then((res) => {
          console.log(res);
          if (res.data.code == 200) {
            this.domain = res.data.data.oss.DoMain;
            localStorage.setItem("oss", JSON.stringify(res.data.data.oss));
          }
        })
        .catch((err) => {});
    },
    getUserInfo() {},
    handleClick() {
      this.page = 1;
      console.log(this.activeName);
      if (this.activeName != "userdesc") {
        this.getData();
      }
    },
    handleSizeChange(val) {
      this.pagesize = val;
    },
    handleCurrentChange(val) {
      this.page = val;
      this.getData();
    },
    userGuanzhu(type) {
      this.$axios
        .get(this.url + "/api/userGuanzhu/" + this.uid + "?type=" + type)
        .then((res) => {
          console.log(res, "res--------");
          if (res.data.code !== 200) {
          } else {
            this.$message.success(res.data.msg);
            this.getUserFansInfo();
          }
        })
        .catch((err) => {});
    },
    getUserFansInfo() {
      this.$axios
        .get(this.url + "/api/getUserFansInfo/" + this.uid, {})
        .then((res) => {
          console.log(res, "res--------");
          if (res.data.code !== 200) {
          } else {
            this.isfans = res.data.data.isfans;
            this.fansNum = res.data.data.fansNum;
            this.attentionNum = res.data.data.attentionNum;
            this.isfansShow = res.data.data.isfansShow;
            this.user = res.data.data.userInfo;
          }
        })
        .catch((err) => {});
    },
    getData() {
      this.loadings = true;
      console.log("data----------");
      var that = this;
      this.$axios
        .post(this.url + "/api/getUserFansListInfo/" + this.activeName, {
          page: this.page,
          uid: this.uid,
        })
        .then((res) => {
          this.loadings = false;
          if (res.data.code === 200) {
            console.log(res);
            that.items = res.data.data.list.data;
            that.pagesize = res.data.data.list.per_page;
            that.total = res.data.data.list.total;
            // this.page = res.data.data.current_page;
            console.log(that.items, that.pagesize, that.total);
          }
        })
        .catch((err) => {
          this.loadings = false;
        });
    },

    handleAvatarSuccess(res, file) {
      let avatar = this.url + "/" + res.data.filename;
      let user = JSON.parse(localStorage.getItem("UserInfo"));
      this.user.avatar = avatar;
      if (res.data.filename) {
        this.$axios
          .post(this.url + "/api/user/update", {
            type: "avatar",
            value: avatar,
          })
          .then((res) => {
            if (res.data.code === 200) {
              user["avatar"] = avatar;
              localStorage.setItem("UserInfo", JSON.stringify(user));
            }
          })
          .catch((err) => {});
      }
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === "image/jpeg";
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        alert("上传头像图片只能是 JPG 格式!");
        // this.$message.error('上传头像图片只能是 JPG 格式!');
      }
      if (!isLt2M) {
        alert("上传头像图片大小不能超过 2MB!");
        // this.$message.error('上传头像图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },
  },
};
</script>

<style scoped lang="scss">
.information {
  text-align: left;
  .welcome {
    background: #a3acb6;
    height: 46px;
    display: flex;
    align-items: center;
    padding-left: 20px;
    padding-right: 20px;
    .text {
      color: #fff;
      font-size: 13px;
      line-height: 1.2;
      margin-right: 14px;
    }
    span {
      background: #f8e715;
      font-size: 12px;
      color: #000;
      padding-left: 10px;
      padding-right: 10px;
      border-radius: 100px;
      font-weight: bold;
    }
  }
  .main {
    padding: 40px;
  }
  .head {
    padding-bottom: 40px;
    border-bottom: 1px solid #dbdddf;
    margin-bottom: 40px;
    display: flex;
    align-items: center;
    .avatar-box {
      position: relative;
      width: 124px;
      height: 124px;
      border-radius: 100%;
      background-color: #dddddd;
      overflow: hidden;
      .avatar {
        width: 124px;
        height: 124px;
        border-radius: 100%;
      }
      .avatar-uploader {
        width: 100%;
        height: 100%;
        /deep/ .el-upload {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .name {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 30px;
        line-height: 30px;
        text-align: center;
        color: #fff;
        text-align: center;
        font-size: 12px;
        background: rgba(0, 0, 0, 0.7);
      }
    }
    .userinfo-box {
      box-sizing: border-box;
      font-size: 21px;
      color: #000;
      .item {
        margin-left: 80px;
        &.item1 > .number {
          color: #0d6efd;
        }
      }
      .number {
        font-size: 21px;
        color: #000;
        font-weight: bold;
        line-height: 1.2;
        margin-bottom: 20px;
      }
      .text {
        font-size: 14px;
        color: #818e9d;
        line-height: 1.2;
      }
    }
    .userinfo-desc {
      margin-left: 50px;
      height: 60px;
      overflow: auto;
      width: 50%;
      color: #999999;
      font-size: 14px;
      line-height: 20px;
    }
    .info {
      text-align: left;
      display: flex;
      align-items: center;
      .item {
        margin-left: 80px;
        &.item1 > .number {
          color: #0d6efd;
        }
      }
      .number {
        font-size: 21px;
        color: #000;
        font-weight: bold;
        line-height: 1.2;
        margin-bottom: 20px;
      }
      .text {
        font-size: 14px;
        color: #818e9d;
        line-height: 1.2;
      }
    }
  }
  .basic-information {
    >>> .userdesc-home {
      margin: 50px;
      border: 1px dashed #dddddd;
      padding: 30px;
      box-sizing: border-box;
    }
    >>> .list {
      min-height: 300px;
      .itembox {
        padding-top: 40px;
        box-sizing: border-box;
        .image {
          width: 100%;
          min-height: 180px;
          background-color: #eeeeee;
          .bg-image {
            width: 100%;
            max-height: 180px;
            overflow: hidden;
            img {
              width: 100%;
              min-height: 180px;
              height: auto;
            }
          }
        }
      }
      .list-item {
        display: flex;
        align-items: center;
        margin-bottom: 30px;
        padding: 20px 20px;
        &:last-child {
          margin-bottom: 0;
        }
        .image {
          width: 100px;
          height: auto;
          border-radius: 5px;
          overflow: hidden;
          flex-shrink: 0;
          margin-right: 20px;
        }
        .info {
          min-width: 0;
          flex-grow: 1;
        }
        .title {
          font-size: 16px;
          color: #000;
          line-height: 1.6;
          height: 3.2em;
          margin-bottom: 15px;
        }
        .progress-rate {
        }
        .text {
          justify-content: space-between;
          width: 100%;
          margin-bottom: 10px;
          font-size: 14px;
          line-height: 1.2;
          .text1 {
            color: #687d93;
          }
          .text2 {
            color: #67c23a;
          }
        }
      }
    }
    .title {
      font-size: 14px;
      color: #000;
      line-height: 1.2;
      margin-bottom: 20px;
      font-weight: bold;
    }
    .tip {
      background: #f4f4f4;
      height: 40px;
      line-height: 40px;
      padding-left: 20px;
      padding-right: 20px;
      font-size: 12px;
      color: #818e9d;
      margin-bottom: 40px;
      text-align: left;
    }
    .xg-form /deep/ {
      .el-input__inner {
        height: 44px;
        line-height: 44px;
        border-radius: 4px;
        padding: 0 15px;
      }
      .el-select {
        width: 100%;
      }
      .el-form-item__label {
        color: #818e9d;
      }
      .el-button {
        width: 120px;
        height: 46px;
        line-height: 46px;
        background-color: #f4f4f4;
        border-radius: 4px;
        text-align: center;
        font-size: 14px;
        color: #818e9d;
        border: 0;
        padding: 0;
        &:hover {
          background: #0d6efd;
          color: #fff;
        }
      }
      .intro textarea {
        height: 140px !important;
      }
    }
  }
}

@media screen and (max-width: 751px) {
  .information .welcome {
    padding: 8px 10px;
    font-size: 12px;
    height: auto;
    flex-wrap: wrap;
    .text {
      margin-right: 0;
      line-height: 1.6;
    }
    span {
      display: block;
      margin-top: 5px;
    }
  }
  .information .main {
    padding: 15px;
  }
  .information .head {
    flex-wrap: wrap;
    padding-bottom: 20px;
    margin-bottom: 20px;
  }
  .information .head .avatar-box {
    width: 120px;
    height: 120px;
    display: block;
    margin: 0 auto 20px;
  }
  .information .head .info {
    width: 100%;
  }
  .information .head .info .item {
    margin-left: 0;
    width: 32%;
  }
  .information .head .info .item:nth-child(3n-1) {
    margin-left: 2%;
    margin-right: 2%;
  }
  .information .head .info .number {
    font-size: 18px;
    margin-bottom: 5px;
  }
  .information .basic-information .tip {
    height: auto;
    line-height: 1.6;
    padding: 8px 10px;
    font-size: 12px;
    margin-bottom: 20px;
  }
  .information .xg-form /deep/ .el-form-item {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    margin-bottom: 5px;
    &:before,
    &:after {
      display: none;
    }
    .el-form-item__label {
      width: 100% !important;
      text-align: left;
      font-size: 12px;
    }
    .el-form-item__content {
      margin-left: 0 !important;
      width: 100%;
    }
    .el-input__inner {
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      padding-right: 10px;
    }
    .el-button {
      width: 100%;
      height: 36px;
      line-height: 36px;
      padding: 0;
      font-size: 12px;
      margin-top: 10px;
    }
  }
}
</style>

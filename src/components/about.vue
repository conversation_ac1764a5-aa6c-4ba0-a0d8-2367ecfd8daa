<template>
  <div class="xg-ny xg-ny-about">
    <div class="xg-box">
      <el-tabs v-model="activeName" tab-position="left">
        <el-tab-pane label="关于我们" name="about">
          <div class="head">关于我们</div>
          <div class="xg-content" v-html="about"></div>
        </el-tab-pane>
        <el-tab-pane label="常见问题" name="question">
          <div class="head">常见问题</div>
          <div class="xg-content" v-html="question"></div>
<!--          <div class="xg-content">-->
<!--            <div-->
<!--              class="item"-->
<!--              v-for="(item, index) in 16"-->
<!--              :key="index"-->
<!--              @click="details(4)"-->
<!--            >-->
<!--              <div class="xg-line-1 title">-->
<!--                我想引进或参考其他高校的慕课资源，要怎样申请？-->
<!--              </div>-->
<!--              <div class="xg-line-2 desc">-->
<!--                为规范管理，由教学单位审核、汇总教师用课信息后报送教学促进部。管理员（联系信息详见文末）会根据各单位报送的意向表为老师赋权、指定课程并-->
<!--                设定开课学期。-->
<!--              </div>-->
<!--            </div>-->
<!--          </div>-->
        </el-tab-pane>
        <el-tab-pane label="服务条款" name="service">
          <div class="head">服务条款</div>
          <div class="xg-content" v-html="service"></div>
        </el-tab-pane>
        <el-tab-pane label="隐私协议" name="private">
          <div class="head">隐私协议</div>
          <div class="xg-content" v-html="private"></div>
        </el-tab-pane>
        <el-tab-pane label="上传声明" name="upload_statement">
          <div class="head">上传声明</div>
          <div class="xg-content" v-html="upload_statement"></div>
        </el-tab-pane>
        <el-tab-pane label="版权声明" name="copyright_statement">
          <div class="head">版权声明</div>
          <div class="xg-content" v-html="copyright_statement"></div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
export default {
  name: "about",
  components: {},
  data() {
    return {
      private: "",
      service: "",
      question: "",
      about: "",
      upload_statement: "",
      copyright_statement: "",
      activeName: "about",
    };
  },
  beforeMount() {
    this.getInfo();
    let type = this.$route.query.type;
    if (type) {
      this.$nextTick(function () {
        this.activeName = type+'';
      });
    }
  },
  methods: {
    getInfo() {
      this.$axios
        .get(this.url + "/api/config/info", {
          params: {},
        })
        .then((res) => {
          if (res.data.code === 200) {
            this.about = res.data.data.about;
            this.question = res.data.data.question;
            this.service = res.data.data.service;
            this.private = res.data.data.private;
            this.upload_statement = res.data.data.upload_statement;
            this.copyright_statement = res.data.data.copyright_statement;
          }
        })
        .catch((err) => {});
    },
  },
  watch: {
    $route: {
      handler(route) {
        let type = this.$route.query.type;
        this.$nextTick(function () {
          this.activeName = type+'';
        });
      },
    },
  },
};
</script>

<style scoped lang="scss">
.aboutus {
  img {
    max-width: 100%;
  }
  .mainp {
    text-align: left;
    text-indent: 2em !important;
  }
}

.aboutus {
  margin: 0 20%;
  padding: 40px 20px;
  min-height: 600px;
  margin-top: 80px;
  margin-bottom: 80px;
  background: white;
  h1 {
    font-size: 36px;
  }
  p {
    margin-top: 20px;
  }
}

@media only screen and (max-width: 900px) {
  .aboutus {
    margin: 0 5%;
    padding: 40px 20px;
    margin-top: 40px;
    margin-bottom: 40px;
    background: white;
    h1 {
      font-size: 36px;
    }
    p {
      margin-top: 20px;
    }
  }
}

/* ==================== 内页 - 关于我们 start ==================== */
.xg-ny-about {
  .el-tabs >>> .el-tabs__header {
    margin-right: 27px;
    min-height: 600px;
    background: #fff;
    border: 1px solid #dbdddf;
    width: 144px;
  }
  .el-tabs >>> .el-tabs__active-bar {
    display: none;
  }
  .el-tabs >>> .el-tabs__item {
    height: 55px;
    line-height: 55px;
    text-align: center;
  }
  .el-tabs >>> .el-tabs__item.is-active {
    background: #0d6efd;
    color: #fff !important;
  }
  .el-tabs >>> .el-tabs__content {
    background: #fff;
    padding: 45px;
    border: 1px solid #dbdddf;
    min-height:600px;
  }

  //
  .head {
    font-size: 26px;
    color: #000;
    line-height: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid #dbdddf;
    width: 100%;
    background: url(../assets/img/ny-bg.png) no-repeat right top;
    background-size: 40px;
    margin-bottom: 40px;
    text-align: left;
    font-weight: bold;
  }
  .item {
    margin-bottom: 60px;
    width: 100%;
    text-align: left;
    cursor: pointer;
    .title {
      font-size: 14px;
      color: #000;
      line-height: 1.2;
      margin-bottom: 10px;
    }
    .desc {
      font-size: 14px;
      color: #a3acb6;
      line-height: 1.6;
      height: 3.2em;
      text-align: justify;
    }
  }
}
@media screen and (min-width: 751px) {
  .xg-ny-about {
  }
}
@media screen and (max-width: 751px) {
  .xg-ny-about {
    &>>>.el-tabs{
      display:flex;flex-wrap:wrap;
      .el-tabs__header{width:100%;margin-right:0;min-height:inherit;margin-bottom:15px;}
      .el-tabs__nav{display:flex;flex-wrap:wrap;}
      .el-tabs__item{width:50%;height:40px;line-height:40px;}
      .el-tabs__content{padding:30px 20px;padding-top:20px;width:100%;min-height:inherit;}
    }
    .head{font-size:20px;line-height:20px;padding-bottom:20px;margin-bottom:20px;padding-top:10px;}
  }
}
/* ==================== 内页 - 关于我们 end ==================== */
</style>

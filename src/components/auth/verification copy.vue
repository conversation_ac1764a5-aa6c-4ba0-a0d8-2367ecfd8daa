<template>
  <div>
    <div class="register-wrapper">
      <div id="register">
        <p class="title">手机号登录</p>
        <el-form
          ref="form"
          :model="form"
          status-icon
          :rules="rules"
          label-width="0"
          class="demo-ruleForm">
          <el-form-item prop="tel">
            <el-input v-model="form.tel" auto-complete="off" placeholder="请输入手机号"></el-input>
          </el-form-item>
          <el-form-item prop="smscode" class="code">
            <el-input v-model="form.smscode" placeholder="验证码"></el-input>
            <el-button type="primary" :disabled='isDisabled' @click="sendCode">{{buttonText}}</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm()" style="width:100%;">登录</el-button>
            <p class="login" @click="gotoLogin">账号密码登录</p>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
  import {mapMutations} from "vuex";
  import {checkMobile} from "../../utils/common";
  import {isPhone} from "../../utils/validate";

  export default {
    name: "verification",
    components:{
    },
    data() {
      return {
        form: {
          tel: "",
          smscode: "",
        },
        rules: {
          tel: [{ required: true, message: '请填写手机号', trigger: 'change' },{ validator: isPhone, trigger: 'change' }],
          smscode: [{ required: true, message: '请填写验证码', trigger: 'change' }],
        },
        buttonText: '发送验证码',
        isDisabled: false, // 是否禁止点击发送验证码按钮
        flag: true
      }
    },
    methods: {
      ...mapMutations(['changeLogin']),

      // <!--发送验证码-->
      sendCode () {
        let tel = this.form.tel
        if (checkMobile(tel)) {
          let time = 60
          this.buttonText = '已发送'
          this.isDisabled = true
          if (this.flag) {
            this.flag = false;
            let timer = setInterval(() => {
              time--;
              this.buttonText = time + ' 秒'
              if (time === 0) {
                clearInterval(timer);
                this.buttonText = '重新获取'
                this.isDisabled = false
                this.flag = true;
              }
            }, 1000);

            this.$axios.post(this.url + '/api/verification-codes',{
              phone: tel,
              scene: 'login'
            }).then(res => {
            }).catch(err => {
            });
          }
        }
      },
      // <!--手机验证码登陆-->
      submitForm() {
        this.$refs.form.validate(valid => {
          let _this = this;
          if (valid) {
            this.$axios.post(this.url + '/api/user/login',{
              phone: this.form.tel,
              code: this.form.smscode,
              type: 'code'
            }).then(res => {
              if (res.data.code !== 200) {
                // 提示
                this.$message.error(res.data.msg);
              } else {
                _this.userToken = 'Bearer ' + res.data.data.token;
                _this.userInfo = res.data.data.user;
                _this.changeLogin({
                  Authorization: _this.userToken,
                  UserInfo: _this.userInfo
                });
                _this.$router.push('/');
              }
            }).catch(err => {
            });
          } else {
            return false;
          }
        })
      },
      // <!--进入登录页-->
      gotoLogin() {
        this.$router.push({
          path: "/login"
        });
      },
    }
  };
</script>

<style scoped >
  .loading-wrapper {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    background: #aedff8;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .register-wrapper img {
    position: absolute;
    z-index: 1;
  }
  .register-wrapper {

  }
  #register {
    max-width: 340px;
    margin: 60px auto;
    margin-top: 80px;
    background: #fff;
    padding: 20px 40px;
    border-radius: 10px;
    position: relative;
    z-index: 99;
  }
  .title {
    font-size: 26px;
    line-height: 50px;
    font-weight: bold;
    margin: 10px;
    text-align: center;
  }
  .el-form-item {
    text-align: center;
  }
  .login {
    margin-top: 10px;
    font-size: 14px;
    line-height: 22px;
    color: #1ab2ff;
    cursor: pointer;
    text-align: left;
    text-indent: 8px;
    width: 160px;
  }
  .login:hover {
    color: #2c2fd6;
  }
  .code >>> .el-form-item__content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .code button {
    margin-left: 20px;
    width: 140px;
    text-align: center;
  }
  .el-button--primary:focus {
    background: #409EFF;
    border-color: #409EFF;
    color: #fff;
  }

  .navbar-expand-lg .navbar-collapse{
    display:flex;
    justify-content:space-between;
  }
  .navbar{
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    width: 100%;
    /*position: relative;*/
    /*top: 0;*/
    z-index: 9999999999;
    /*position: absolute;*/
    /*top:0;*/
    /*left: 0;*/
  }
  button{
    outline: none;
  }
  .bg-info{
    /*background-color: rgba(0,0,0,0)!important;*/
    background-color: rgb(17, 19, 42)!important;

  }
  .navbar-dark .navbar-brand,.navbar-dark .navbar-nav .nav-link{
    color: white;
  }
  .btn-success{
    color: white!important;
    background: none!important;
    border: none!important;
    margin-top: 5px;
  }



  @media only screen and (max-width: 900px){
    .navbar-expand-lg .navbar-collapse{
      display:block;
    }
    .navbar-dark .navbar-toggler{
      color: rgba(0,0,0,0.5);
      border-color: rgba(0,0,0,0.5);
    }
    /deep/.navbar-dark .navbar-toggler-icon{
      background-image: url("../../assets/img/nav.png");
    }
  }
</style>

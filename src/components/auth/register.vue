<template>
  <!-- 注册页面 -->
  <div class="register-view">

    <div class="body">
      <!-- logo -->
      <div class="logo"><img src="../../assets/img/logo3.png" alt=""></div>
      <!--  -->
      <div class="title">会员注册</div>
      <el-form
        ref="ruleForm2"
        :model="ruleForm2"
        status-icon
        :rules="rules2"
      >
        <el-form-item prop="name">
          <el-input v-model="ruleForm2.name" auto-complete="off" placeholder="输入用户名"></el-input>
        </el-form-item>
        <el-form-item prop="tel">
          <el-input v-model="ruleForm2.tel" auto-complete="off" placeholder="手机号码/邮箱"></el-input>
        </el-form-item>
        <el-form-item prop="smscode" class="code-item">
          <div class="code">
            <el-input v-model="ruleForm2.smscode" autocomplete="off" placeholder="验证码"></el-input>
            <el-button type="primary" :class="{'active':isDisabled}" @click="sendCode">{{buttonText}}</el-button>
          </div>
        </el-form-item>
        <el-form-item prop="pass">
          <el-input type="password" v-model="ruleForm2.pass" autocomplete="off" auto-complete="off" placeholder="输入密码"></el-input>
        </el-form-item>
        <el-form-item prop="checkPass">
          <el-input type="password" v-model="ruleForm2.checkPass" auto-complete="off" placeholder="确认密码"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" class="register-btn" @click="submitForm()">注册</el-button>
          <div class="protocol">
            <el-checkbox v-model="checked" @change="check"></el-checkbox>
            <span>同意犀光RadiRhino注册协议内容</span>
          </div>
          <div class="login" @click="gotoLogin">使用已有账号登录</div>
        </el-form-item>
      </el-form>
      <!-- 底部导航 -->
      <div class="itembox">
        <router-link
            :to="{ name: 'help',query:{type:'question'} }"
            class="item"
        >常见问题</router-link>
        <router-link
            :to="{ name: 'help',query:{type:'service'} }"
            class="item"
        >服务条款</router-link>
        <router-link
            :to="{ name: 'help',query:{type:'private'} }"
            class="item"
        >隐私协议</router-link>
      </div>
      <!-- 版权信息 -->
      <div class="xg-copryright2">
        © 2022 Copyright Radirhino.com QQ:453395205
      </div>
    </div>

    <el-dialog
        title="注册协议"
        :visible.sync="centerDialogVisible"
        center>
        <p v-html="registerContent" style="height: 60%"></p>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="centerDialogVisible = false">关 闭</el-button>
        </span>
    </el-dialog>
  </div>
</template>

<script>
import headback from "../headback";
import {checkEmail, checkMobile} from "../../utils/common";
import {isPhone} from "../../utils/validate";
export default {
  name: "register",
  components: {
    headback
  },
  data() {
    // <!--验证密码-->
    var validatePass = (rule, value, callback) => {
      if (this.ruleForm2.checkPass !== '') {
        this.$refs.ruleForm2.validateField('checkPass');
      }
      callback();
    };
    // <!--二次验证密码-->
    var validateCheckPass = (rule, value, callback) => {
      if (value !== this.ruleForm2.pass) {
        callback(new Error('两次输入密码不一致!'));
      }
      callback();
    };
    return {
      ruleForm2: {
        pass: "",
        checkPass: "",
        tel: "",
        smscode: "",
        name:""
      },
      rules2: {
        pass: [{ required: true, message: '请填写新密码', trigger: 'change' }, { validator: validatePass, trigger: 'blur' }],
        checkPass: [{required:true, message:'请填写确认新密码', trigger:'change'}, { validator: validateCheckPass, trigger: 'blur' }],
        // tel: [{ required: true, message: '请填写手机号', trigger: 'change' }, { validator: isPhone, trigger: 'change' }],// 绑定手机号验证
        tel: [{ required: true, message: '请填写手机号/邮箱', trigger: 'change' }],// 绑定手机号/邮箱验证
        smscode: [{ required: true, message: '请填写验证码', trigger: 'change' }],
        name: [{ required: true, message: '请填写用户名', trigger: 'change' }],
      },
      buttonText: '发送验证码',
      isDisabled: false, // 是否禁止点击发送验证码按钮
      flag: true,

      centerDialogVisible: false,
      checked: false,
      registerContent: ''
    }
  },
  beforeMount() {
    this.getRegister()
  },
  mounted() {
    let redirect = this.$route.query.redirect;
    if (redirect === 'bind') {
      this.$message.warning({
        duration: 20000,
        showClose: true,
        message: '请先注册账号，然后前往个人中心绑定第三方授权登陆！'
      });
    }
  },
  methods: {
    check(event){
      if (event) {
        this.centerDialogVisible = true
      } else {
        this.centerDialogVisible = false
      }
    },

    // <!--发送验证码-->
    sendCode() {
      let tel = this.ruleForm2.tel
      if (checkMobile(tel) || checkEmail(tel)) {
        let time = 60
        this.buttonText = '已发送'
        this.isDisabled = true
        if (this.flag) {
          this.flag = false;
          let timer = setInterval(() => {
            time--;
            this.buttonText = time + ' 秒'
            if (time === 0) {
              clearInterval(timer);
              this.buttonText = '重新获取'
              this.isDisabled = false
              this.flag = true;
            }
          }, 1000)

          this.$axios.post(this.url + '/api/verification-codes',{
            phone: tel,
            scene: 'register'
          }).then(res => {
          }).catch(err => {
          });
        }
      } else {
        this.$message.warning({
          showClose: true,
          message: '请填写正确手机号'
        });
      }
    },
    // <!--提交注册-->
    submitForm() {
      this.$refs.ruleForm2.validate(valid => {
        if (valid) {
          if (this.checked === false) {
            this.$message.warning({
              showClose: true,
              message: '同意注册协议内容后才能开启账号注册！'
            });
            return false;
          }
          this.$axios.post(this.url + '/api/user/register',{
            phone: this.ruleForm2.tel,
            password: this.ruleForm2.pass,
            password_confirmation: this.ruleForm2.checkPass,
            code: this.ruleForm2.smscode,
            name: this.ruleForm2.name
          }).then(res => {
            if (res.data.code !== 200) {
              // 注册错误
              this.$message.warning({
                showClose: true,
                message: res.data.msg
              });
            } else {
              // 提示 注册成功 让他自己去登陆
              this.$router.push('/login');
            }
          }).catch(err => {
          });
        } else {
          return false;
        }
      })
    },
    getRegister(){
      this.$axios.get(this.url + '/api/register',{
        params:{}
      }).then(res => {
        this.registerContent = res.data.data;
      }).catch(err => {
      })
    },
    // <!--进入登录页-->
    gotoLogin() {
      this.$router.push({
        path: "/login"
      });
    },
  }
};
</script>

<style scoped lang="scss">
.register-view{
  padding-top:90px;padding-bottom:75px;
  background:url(../../assets/img/img37.jpg) no-repeat center;background-size:cover;
  .body{max-width:330px;margin:0 auto;}
  .logo{width:230px;display:block;margin:0 auto 80px;}
  .title{font-size:16px;color:#242b33;line-height:1.2;margin-bottom:20px;text-align:left;font-weight:bold;}
  .el-form{
    /deep/ .el-input__inner,
    .el-button{height:40px;line-height:40px;}
    /deep/ .el-input__inner{background:#fff;border-radius:4px;border:1px solid #c0c4cc;height:40px;line-height:40px;}
    .code{display:flex;align-items:center;width:100%;
      /deep/ .el-input__inner{min-width:0;flex-grow:1;margin-right:15px;border:solid 1px #c0c4cc;}
      .el-button{background-color:#dfdfdf!important;border-radius:4px;padding:0;color:#687d93;font-size:12px;text-align:center;border:0;padding-left:15px;padding-right:15px;margin-left:15px;
        &.active{background:#00be06;color:#fff;}
      }
    }
    .register-btn{background-color:#0d6efd;border:0;border-radius:4px;font-size:14px;color:#fff;letter-spacing:2px;width:100%;padding:0;}
    .protocol{display:flex;align-items:center;font-size:12px;color:#687d93;margin-top:20px;
      &>span{padding-left:10px;}
    }
    .login{font-size:14px;color:#0d6efd;line-height:1.2;margin-top:36px;cursor:pointer;}
  }
  .itembox{
    margin-top:110px;
    display:flex;align-items:center;justify-content:center;margin-bottom:20px;
    .item{font-size:12px;color:#687d93;line-height:1.2;margin-right:20px;
      &:last-child{margin-right:0;}
    }
  }
  .xg-copryright2{font-size:12px;color:#687d93;line-height:1.6;text-align:center;}
  /deep/ .el-dialog{
    .el-dialog__body{height:400px;overflow:auto;}
  }
}


@media only screen and (max-width: 751px){
  .register-view{padding:30px 20px;
    .body{max-width:1000%;}
    .logo{width:200px;margin-bottom:40px;}
    .el-form{
      .login{margin-top:20px;}
    }
    .itembox{margin-top:30px;}
  }
}
</style>

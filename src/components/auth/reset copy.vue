<template>
  <div class="reset">
    <!--导航-->
    <!-- <headback></headback> -->

    <div class="register-wrapper">
      <p class="title">密码重置</p>
      <el-form :model="ruleForm2" ref="ruleForm2" class="demo-dynamic" :rules="rules2">
        <div class="questions">
          <div class="questions-buttom">
            <div class="one">
              <label>问题1：你的父母名字</label>
              <input type="text" placeholder="请填写答案" v-model="ruleForm2.answer1"/>
            </div>
            <div class="one">
              <label>问题2：你的老师名字</label>
              <input type="text" placeholder="请填写答案" v-model="ruleForm2.answer2"/>
            </div>
            <div class="one">
              <label>问题3：你的爱人的名字</label>
              <input type="text" placeholder="请填写答案" v-model="ruleForm2.answer3"/>
            </div>
          </div>
        </div>
        <el-form-item prop="pass">
          <el-input type="password" v-model="ruleForm2.pass" auto-complete="off" placeholder="输入密码"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm()" style="width:100%;">提交</el-button>
          <p class="login" @click="gotoLogin">去登录</p>
        </el-form-item>
      </el-form>
    </div>

  </div>
</template>

<script>
  import headback from "../headback";

  export default {
    name: "reset",
    components:{
      headback
    },
    data() {
      return {
        ruleForm2: {
          answer1: "",
          answer2: "",
          answer3: "",
          pass: "",
        },
        rules2: {
          pass:[{required: true, message: '请填写密码', trigger: 'change'}],
        },
      }
    },
    mounted () {

    },
    methods: {
      //提交
      submitForm(){
        let email = this.$route.query.email;

        if (this.ruleForm2.answer1 == '' || this.ruleForm2.answer2 == '' || this.ruleForm2.answer3 == '') {
          this.$message.warning({
            showClose: true,
            message: '请填写问题答案'
          });
          return false;
        }

        if (email == '' || email == undefined) {
          this.$message.warning({
            showClose: true,
            message: '异常操作'
          });
          return false;
        }

        this.$axios.post(this.url + '/api/user/reset-password',{
          email: email,
          answer1: this.ruleForm2.answer1,
          answer2: this.ruleForm2.answer2,
          answer3: this.ruleForm2.answer3,
          password: this.ruleForm2.pass,
        }).then(res => {
          if (res.data.code !== 200) {
            this.$message.warning({
              showClose: true,
              message: res.data.msg
            });
          } else {
            this.$message.success({
              showClose: true,
              message: res.data.msg
            });
            // 提示成功 并 跳转登陆
            this.$router.push('/login');
          }
        }).catch(err => {

        });
      },
      //忘记密码
      gotoLogin(){
        this.$router.push({
          path: "/login"
        });
      }
    }
  };
</script>

<style scoped lang="scss">
  .register-wrapper {
    max-width: 340px;
    margin: 0 auto;
    margin-top: 50px;
    margin-bottom: 60px;
    background: #fff;
    padding: 20px 40px;
    border-radius: 10px;
    overflow: hidden;
  }
  .title {
    font-size: 26px;
    line-height: 50px;
    font-weight: bold;
    margin: 10px;
    /*text-align: center;*/
    display: block;
  }
  >>> .el-input__inner{
    height: 42px;
  }
  >>> .el-input-group__append{
    padding: 0;
  }
  .el-button--small.is-circle{
    padding: 3px;
  }
  .login {
    margin-top: 20px;
    font-size: 14px;
    line-height: 22px;
    color: #1ab2ff;
    cursor: pointer;
    width: 160px;
    text-align: left;
    p{
      margin-top: 10px!important;
    }
  }
  .login:hover {
    color: #2c2fd6;
  }
  .resbottom{
    width: 100%;
    display: flex;
    justify-content: space-between;
    p:last-child{
      text-align: right;
    }
  }

  .navbar-expand-lg .navbar-collapse{
    display:flex;
    justify-content:space-between;
  }
  .navbar{
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    width: 100%;
    /*position: relative;*/
    /*top: 0;*/
    z-index: 99;
    /*position: absolute;*/
    /*top:0;*/
    /*left: 0;*/
  }
  button{
    outline: none;
  }
  .bg-info{
    /*background-color: rgba(0,0,0,0)!important;*/
    background-color: rgb(17, 19, 42)!important;

  }
  .navbar-dark .navbar-brand,.navbar-dark .navbar-nav .nav-link{
    color: white;
  }
  .btn-success{
    color: white!important;
    background: none!important;
    border: none!important;
    margin-top: 5px;
  }

  .questions{
    margin-bottom: 20px;
    h6{
      background: rgba(252,144,0,0.1);
      color: rgba(252,144,0,1);
      line-height: 40px;
      font-size: 14px;
      padding-left: 10px;
    }
    .questions-buttom{
      .one{
        margin-top: 15px;
        label{
          text-align: left!important;
          width: 100%;
          padding: 0 15px;
        }
        p{
          line-height: 20px;
          white-space: nowrap;
          overflow: hidden;
          font-size: 14px;
          color: rgba(0,0,0,.88);
          border-bottom: 1px solid rgba(0,0,0,.16);
          padding: 5px 15px;
          text-align: left;
        }
        input{
          width: 100%;
          border: none;
          border-bottom: 1px solid rgba(0,0,0,.16);
          outline: none;
          color: rgba(0,0,0,.88);
          font-size: 12px;
          padding: 5px 15px;
        }
        input:focus{
          border-bottom: 1px solid #1ab2ff;
        }
      }
    }
  }


  @media only screen and (max-width: 900px){
    .navbar-expand-lg .navbar-collapse{
      display:block;
    }
    .navbar-dark .navbar-toggler{
      color: rgba(0,0,0,0.5);
      border-color: rgba(0,0,0,0.5);
    }
    /deep/.navbar-dark .navbar-toggler-icon{
      background-image: url("../../assets/img/nav.png");
    }
  }
</style>

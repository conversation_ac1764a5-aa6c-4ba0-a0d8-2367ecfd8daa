<template>
  <div class="loginwap">
    <!--导航-->
    <!-- <headback></headback> -->

    <div class="register-wrapper">
      <p class="title">忘记密码</p>
      <el-form :model="dynamicValidateForm" ref="dynamicValidateForm"  class="demo-dynamic">
        <el-form-item
          prop="email"
          :rules="[
      { required: true, message: '请输入邮箱地址', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
    ]"
        >
          <el-input v-model="dynamicValidateForm.email" placeholder="请输入你的邮箱"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submit()">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

  </div>
</template>

<script>
  import headback from "../headback";

  export default {
    name: "forget",
    components:{
      headback
    },
    data() {
      return {
        dynamicValidateForm: {
          email: '',
        }
      }
    },
    mounted () {

    },
    methods: {
      //忘记密码
      submit(){
        this.$axios.post(this.url + '/api/user/forget-password',{
          email: this.dynamicValidateForm.email
        }).then(res => {
          if (res.data.code !== 200) {
            // 注册错误
            alert(res.data.msg);
          } else {
            // 提示成功 并 跳转登陆
            this.$router.push('/login');
          }
        }).catch(err => {

        });
      }
    }
  };
</script>

<style scoped lang="scss">
  .register-wrapper {
    max-width: 340px;
    margin: 0 auto;
    margin-top: 80px;
    margin-bottom: 60px;
    background: #fff;
    padding: 20px 40px;
    border-radius: 10px;
    overflow: hidden;
  }
  .title {
    font-size: 26px;
    line-height: 50px;
    font-weight: bold;
    margin: 10px;
    text-align: center;
    display: block;
  }
  >>> .el-input__inner{
    height: 42px;
  }
  >>> .el-input-group__append{
    padding: 0;
  }
  .el-button--small.is-circle{
    padding: 3px;
  }
  .login {
    margin-top: 20px;
    font-size: 14px;
    line-height: 22px;
    color: #1ab2ff;
    cursor: pointer;
    width: 160px;
    text-align: left;
    p{
      margin-top: 10px!important;
    }
  }
  .login:hover {
    color: #2c2fd6;
  }
  .resbottom{
    width: 100%;
    display: flex;
    justify-content: space-between;
    p:last-child{
      text-align: right;
    }
  }

  .navbar-expand-lg .navbar-collapse{
    display:flex;
    justify-content:space-between;
  }
  .navbar{
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    width: 100%;
    /*position: relative;*/
    /*top: 0;*/
    z-index: 99;
    /*position: absolute;*/
    /*top:0;*/
    /*left: 0;*/
  }
  button{
    outline: none;
  }
  .bg-info{
    /*background-color: rgba(0,0,0,0)!important;*/
    background-color: rgb(17, 19, 42)!important;

  }
  .navbar-dark .navbar-brand,.navbar-dark .navbar-nav .nav-link{
    color: white;
  }
  .btn-success{
    color: white!important;
    background: none!important;
    border: none!important;
    margin-top: 5px;
  }



  @media only screen and (max-width: 900px){
    .navbar-expand-lg .navbar-collapse{
      display:block;
    }
    .navbar-dark .navbar-toggler{
      color: rgba(0,0,0,0.5);
      border-color: rgba(0,0,0,0.5);
    }
    /deep/.navbar-dark .navbar-toggler-icon{
      background-image: url("../../assets/img/nav.png");
    }
  }
</style>

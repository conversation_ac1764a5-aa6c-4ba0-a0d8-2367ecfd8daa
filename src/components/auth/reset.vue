<template>
  <div class="reset-view">
    <div class="body">
      <!--  -->
      <div class="title">密码重置</div>
      <el-form :model="ruleForm2" :rules="rules2" ref="ruleForm2">
        <el-form-item class="answer1">
          <label for="">问题1：你的父母名字</label>
          <el-input v-model="ruleForm2.answer1" auto-complete="off" placeholder="请填写答案">
          </el-input>
        </el-form-item>
        <el-form-item class="answer2">
          <label for="">问题2：你的老师名字</label>
          <el-input v-model="ruleForm2.answer2" auto-complete="off" placeholder="请填写答案">
          </el-input>
        </el-form-item>
        <el-form-item class="answer3">
          <label for="">问题3：你的爱人的名字</label>
          <el-input v-model="ruleForm2.answer3" auto-complete="off" placeholder="请填写答案">
          </el-input>
        </el-form-item>
        <el-form-item class="answer4">
          <label for="">输入新密码</label>
          <el-input
              type="password"
              v-model="ruleForm2.pass"
              auto-complete="off"
              placeholder="输入密码"
            ></el-input>
        </el-form-item>
        <el-button type="primary" @click="submitForm()" class="reset-btn">提交</el-button>
        <!-- 去登录 -->
        <!-- <div class="login" @click="gotoLogin">去登录</div> -->
      </el-form>

      <!-- <div class="register-wrapper">
        <el-form
          :model="ruleForm2"
          ref="ruleForm2"
          class="demo-dynamic"
          :rules="rules2"
        >
          <div class="questions">
            <div class="questions-buttom">
              <div class="one">
                <label>问题1：你的父母名字</label>
                <input
                  type="text"
                  placeholder="请填写答案"
                  v-model="ruleForm2.answer1"
                />
              </div>
              <div class="one">
                <label>问题2：你的老师名字</label>
                <input
                  type="text"
                  placeholder="请填写答案"
                  v-model="ruleForm2.answer2"
                />
              </div>
              <div class="one">
                <label>问题3：你的爱人的名字</label>
                <input
                  type="text"
                  placeholder="请填写答案"
                  v-model="ruleForm2.answer3"
                />
              </div>
            </div>
          </div>
          <el-form-item prop="pass">
            <el-input
              type="password"
              v-model="ruleForm2.pass"
              auto-complete="off"
              placeholder="输入密码"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm()" style="width: 100%"
              >提交</el-button
            >
            <p class="login" @click="gotoLogin">去登录</p>
          </el-form-item>
        </el-form>
      </div> -->
    </div>
  </div>
</template>

<script>
import headback from "../headback";

export default {
  name: "reset",
  components: {
    headback,
  },
  data() {
    return {
      ruleForm2: {
        answer1: "",
        answer2: "",
        answer3: "",
        pass: "",
      },
      rules2: {
        pass: [{ required: true, message: "请填写密码", trigger: "change" }],
      },
    };
  },
  mounted() {},
  methods: {
    //提交
    submitForm() {
      let email = this.$route.query.email;

      if (
        this.ruleForm2.answer1 == "" ||
        this.ruleForm2.answer2 == "" ||
        this.ruleForm2.answer3 == ""
      ) {
        this.$message.warning({
          showClose: true,
          message: "请填写问题答案",
        });
        return false;
      }

      if (email == "" || email == undefined) {
        this.$message.warning({
          showClose: true,
          message: "异常操作",
        });
        return false;
      }

      this.$axios
        .post(this.url + "/api/user/reset-password", {
          email: email,
          answer1: this.ruleForm2.answer1,
          answer2: this.ruleForm2.answer2,
          answer3: this.ruleForm2.answer3,
          password: this.ruleForm2.pass,
        })
        .then((res) => {
          if (res.data.code !== 200) {
            this.$message.warning({
              showClose: true,
              message: res.data.msg,
            });
          } else {
            this.$message.success({
              showClose: true,
              message: res.data.msg,
            });
            // 提示成功 并 跳转登陆
            this.$router.push("/login");
          }
        })
        .catch((err) => {});
    },
    //忘记密码
    gotoLogin() {
      this.$router.push({
        path: "/login",
      });
    },
  },
};
</script>

<style scoped lang="scss">

.reset-view{
  text-align:left;
  padding-top:90px;padding-bottom:75px;
  background:#f4f4f4;
  .body{max-width:330px;margin:0 auto;}
  .title{font-size:16px;color:#242b33;line-height:1.2;margin-bottom:20px;text-align:left;font-weight:bold;}
  .el-form{
    /deep/ .el-input__inner,
    .el-button{height:40px;line-height:40px;}
    /deep/ .el-input__inner{background:#fff;border-radius:4px;border:1px solid #c0c4cc;height:40px;line-height:40px;}
    .reset-btn{background-color:#0d6efd;border:0;border-radius:4px;font-size:14px;color:#fff;letter-spacing:2px;width:100%;padding:0;}
    .login{font-size:14px;color:#0d6efd;line-height:1.2;margin-top:36px;cursor:pointer;}
  }
}

@media only screen and (min-width: 751px){
  .reset-view{min-height: 647px;}

}
@media only screen and (max-width: 751px){
  .reset-view{
    padding:30px 20px;
    min-height:450px;
    .body{max-width:1000%;}
    .el-form{
      .login{margin-top:20px;}
    }
  }
}
</style>

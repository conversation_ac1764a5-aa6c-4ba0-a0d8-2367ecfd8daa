<template>
  <div class="verification-view">
    <div class="body">
      <div class="title">手机号登录</div>
      <el-form :model="form" :rules="rules" ref="form">
        <el-form-item prop="tel" class="tel">
          <el-input v-model="form.tel" auto-complete="off" placeholder="输入手机号">
          </el-input>
        </el-form-item>
        <el-form-item prop="smscode" class="smscode">
          <el-input placeholder="验证码" v-model="form.smscode">
          </el-input>
          <el-button type="primary" :class="{'active':isDisabled}" class="send" :disabled='isDisabled' @click="sendCode">{{buttonText}}</el-button>
        </el-form-item>
        <el-button type="primary" @click="submitForm()">登录</el-button>
        <!-- 用账号密码登录 -->
        <div class="other">
          <div class="text1" @click="gotoLogin">用账号密码登录</div>
        </div>
      </el-form>
    </div>

  </div>
</template>

<script>
  import {mapMutations} from "vuex";
  import {checkMobile} from "../../utils/common";
  import {isPhone} from "../../utils/validate";

  export default {
    name: "verification",
    components:{
    },
    data() {
      return {
        form: {
          tel: "",
          smscode: "",
        },
        rules: {
          tel: [{ required: true, message: '请填写手机号', trigger: 'change' },{ validator: isPhone, trigger: 'change' }],
          smscode: [{ required: true, message: '请填写验证码', trigger: 'change' }],
        },
        buttonText: '发送验证码',
        isDisabled: false, // 是否禁止点击发送验证码按钮
        flag: true,
      }
    },
    methods: {
      ...mapMutations(['changeLogin']),

      // <!--发送验证码-->
      sendCode () {
        let tel = this.form.tel
        if (checkMobile(tel)) {
          let time = 60
          this.buttonText = '已发送'
          this.isDisabled = true
          if (this.flag) {
            this.flag = false;
            let timer = setInterval(() => {
              time--;
              this.buttonText = time + ' 秒'
              if (time === 0) {
                clearInterval(timer);
                this.buttonText = '重新获取'
                this.isDisabled = false
                this.flag = true;
              }
            }, 1000);

            this.$axios.post(this.url + '/api/verification-codes',{
              phone: tel,
              scene: 'login'
            }).then(res => {
            }).catch(err => {
            });
          }
        }
      },
      // <!--手机验证码登陆-->
      submitForm() {
        this.$refs.form.validate(valid => {
          let _this = this;
          if (valid) {
            this.$axios.post(this.url + '/api/user/login',{
              phone: this.form.tel,
              code: this.form.smscode,
              type: 'code'
            }).then(res => {
              if (res.data.code !== 200) {
                // 提示
                this.$message.error(res.data.msg);
              } else {
                _this.userToken = 'Bearer ' + res.data.data.token;
                _this.userInfo = res.data.data.user;
                _this.changeLogin({
                  Authorization: _this.userToken,
                  UserInfo: _this.userInfo
                });
                _this.$router.push('/');
              }
            }).catch(err => {
            });
          } else {
            return false;
          }
        })
      },
      // <!--进入登录页-->
      gotoLogin() {
        this.$router.push({
          path: "/login"
        });
      },
    }
  };
</script>

<style lang="scss" scoped>
  
.verification-view{
  padding:140px 0;
  background:url(../../assets/img/img38.jpg) no-repeat center;background-size:cover;
  .body{
    margin-right:375px;margin-left:auto;
    width:340px;border-radius: 4px;background:rgba(255,255,255,0.85);padding:30px;
    .title{font-size:16px;color:#242b33;text-align:center;line-height:1.2;margin-bottom:25px;font-weight:bold;}
    .el-form{
      .el-input,.el-input__inner,
      .el-button{height:40px;line-height:40px;}
      .el-input{border:1px solid #c0c4cc;border-radius:4px;background-color:#fff;}
      /deep/ .el-input__inner{border:0!important;}
      .el-button{width:100%;border-radius:4px;text-align:center;padding:0;font-size:14px;color:#fff;letter-spacing:2px;}
      /deep/ .smscode{
        .el-form-item__content{display:flex;align-items:center;}
        .send{width:90px;margin-left:20px;letter-spacing:0;flex-shrink:0;font-size:12px;
           &.active{background-color:#00be06!important;border-color:#00be06!important;color:#fff;}
        }
      }
    }
    .other{
      margin-top:20px;
      .text1{font-size:12px;color:#687d93;text-align:left;cursor: pointer;
        &:hover{color:#0d6efd;}
      }
    }
  } 
}
@media only screen and (min-width: 751px){
  .verification-view{min-height:650px;}
}
@media only screen and (max-width: 751px){
  .verification-view{padding:80px 20px;
    min-height:450px;
    .body{width:100%;}
  }
}
</style>

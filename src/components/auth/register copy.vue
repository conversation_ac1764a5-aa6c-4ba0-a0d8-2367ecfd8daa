<template>
  <div>
    <!--导航-->
    <!-- <headback></headback> -->

    <div class="register-wrapper">
      <div id="register">
        <p class="title">注册</p>
        <el-form
          ref="ruleForm2"
          :model="ruleForm2"
          status-icon
          :rules="rules2"
          label-width="0"
          class="demo-ruleForm"
        >
          <el-form-item prop="name">
            <el-input v-model="ruleForm2.name" auto-complete="off" placeholder="请输入用户名"></el-input>
          </el-form-item>
          <el-form-item prop="tel">
            <el-input v-model="ruleForm2.tel" auto-complete="off" placeholder="请输入手机号"></el-input>
          </el-form-item>
          <el-form-item prop="smscode" class="code">
            <el-input v-model="ruleForm2.smscode" autocomplete="off" placeholder="验证码"></el-input>
            <el-button type="primary" @click="sendCode">{{buttonText}}</el-button>
          </el-form-item>
          <el-form-item prop="pass">
            <el-input type="password" v-model="ruleForm2.pass" autocomplete="off" auto-complete="off" placeholder="输入密码"></el-input>
          </el-form-item>
          <el-form-item prop="checkPass">
            <el-input type="password" v-model="ruleForm2.checkPass" auto-complete="off" placeholder="确认密码"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm()" style="width:100%;">注册</el-button>
            <div style="padding-right: 5%">
              <el-checkbox v-model="checked" style="text-align: left" @change="check"></el-checkbox>
              <span style="font-size: 10px">同意犀光RadiRhino注册协议内容</span>
            </div>

            <p class="login" @click="gotoLogin">已有账号？立即登录</p>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <el-dialog
      title="注册协议"
        :visible.sync="centerDialogVisible"
        width="60%"
        height="60%"
        center>
        <p v-html="registerContent" style="height: 60%"></p>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="centerDialogVisible = false">关 闭</el-button>
        </span>
    </el-dialog>
  </div>
</template>

<script>
import headback from "../headback";
import {checkMobile} from "../../utils/common";
import {isPhone} from "../../utils/validate";
export default {
  name: "register",
  components: {
    headback
  },
  data() {
    // <!--验证密码-->
    var validatePass = (rule, value, callback) => {
      if (this.ruleForm2.checkPass !== '') {
        this.$refs.ruleForm2.validateField('checkPass');
      }
      callback();
    };
    // <!--二次验证密码-->
    var validateCheckPass = (rule, value, callback) => {
      if (value !== this.ruleForm2.pass) {
        callback(new Error('两次输入密码不一致!'));
      }
      callback();
    };
    return {
      ruleForm2: {
        pass: "",
        checkPass: "",
        tel: "",
        smscode: "",
        name:""
      },
      rules2: {
        pass: [{ required: true, message: '请填写新密码', trigger: 'change' }, { validator: validatePass, trigger: 'blur' }],
        checkPass: [{required:true, message:'请填写确认新密码', trigger:'change'}, { validator: validateCheckPass, trigger: 'blur' }],
        tel: [{ required: true, message: '请填写手机号', trigger: 'change' }, { validator: isPhone, trigger: 'change' }],// 绑定手机号验证
        smscode: [{ required: true, message: '请填写验证码', trigger: 'change' }],
        name: [{ required: true, message: '请填写用户名', trigger: 'change' }],
      },
      buttonText: '发送验证码',
      isDisabled: false, // 是否禁止点击发送验证码按钮
      flag: true,

      centerDialogVisible: false,
      checked: false,
      registerContent: ''
    }
  },
  beforeMount() {
    this.getRegister()
  },
  mounted() {
    let redirect = this.$route.query.redirect;
    if (redirect === 'bind') {
      this.$message.warning({
        duration: 20000,
        showClose: true,
        message: '请先注册账号，然后前往个人中心绑定第三方授权登陆！'
      });
    }
  },
  methods: {
    check(event){
      if (event) {
        this.centerDialogVisible = true
      } else {
        this.centerDialogVisible = false
      }
    },

    // <!--发送验证码-->
    sendCode() {
      let tel = this.ruleForm2.tel
      if (checkMobile(tel)) {
        let time = 60
        this.buttonText = '已发送'
        this.isDisabled = true
        if (this.flag) {
          this.flag = false;
          let timer = setInterval(() => {
            time--;
            this.buttonText = time + ' 秒'
            if (time === 0) {
              clearInterval(timer);
              this.buttonText = '重新获取'
              this.isDisabled = false
              this.flag = true;
            }
          }, 1000)

          this.$axios.post(this.url + '/api/verification-codes',{
            phone: tel,
            scene: 'register'
          }).then(res => {
          }).catch(err => {
          });
        }
      } else {
        this.$message.warning({
          showClose: true,
          message: '请填写正确手机号'
        });
      }
    },
    // <!--提交注册-->
    submitForm() {
      this.$refs.ruleForm2.validate(valid => {
        if (valid) {
          if (this.checked === false) {
            this.$message.warning({
              showClose: true,
              message: '同意注册协议内容后才能开启账号注册！'
            });
            return false;
          }
          this.$axios.post(this.url + '/api/user/register',{
            phone: this.ruleForm2.tel,
            password: this.ruleForm2.pass,
            password_confirmation: this.ruleForm2.checkPass,
            code: this.ruleForm2.smscode,
            name: this.ruleForm2.name
          }).then(res => {
            if (res.data.code !== 200) {
              // 注册错误
              this.$message.warning({
                showClose: true,
                message: res.data.msg
              });
            } else {
              // 提示 注册成功 让他自己去登陆
              this.$router.push('/login');
            }
          }).catch(err => {
          });
        } else {
          return false;
        }
      })
    },
    getRegister(){
      this.$axios.get(this.url + '/api/register',{
        params:{}
      }).then(res => {
        this.registerContent = res.data.data;
      }).catch(err => {
      })
    },
    // <!--进入登录页-->
    gotoLogin() {
      this.$router.push({
        path: "/login"
      });
    },
  }
};
</script>

<style scoped >


.loading-wrapper {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: #aedff8;
  display: flex;
  align-items: center;
  justify-content: center;
}
.register-wrapper img {
  position: absolute;
  z-index: 1;
}
.register-wrapper {

}
#register {
  max-width: 340px;
  margin: 60px auto;
  margin-top: 80px;
  background: #fff;
  padding: 20px 40px;
  border-radius: 10px;
  position: relative;
  z-index: 99;
}
.title {
  font-size: 26px;
  line-height: 50px;
  font-weight: bold;
  margin: 10px;
  text-align: center;
}
.el-form-item {
  text-align: center;
}
.login {
  margin-top: 10px;
  font-size: 14px;
  line-height: 22px;
  color: #1ab2ff;
  cursor: pointer;
  text-align: left;
  text-indent: 8px;
  width: 160px;
}
.login:hover {
  color: #2c2fd6;
}
.code >>> .el-form-item__content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.code button {
  margin-left: 20px;
  width: 140px;
  text-align: center;
}
.el-button--primary:focus {
  background: #409EFF;
  border-color: #409EFF;
  color: #fff;
}

.navbar-expand-lg .navbar-collapse{
  display:flex;
  justify-content:space-between;
}
.navbar{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  width: 100%;
  /*position: relative;*/
  /*top: 0;*/
  z-index: 99;
  /*position: absolute;*/
  /*top:0;*/
  /*left: 0;*/
}
button{
  outline: none;
}
.bg-info{
  /*background-color: rgba(0,0,0,0)!important;*/
  background-color: rgb(17, 19, 42)!important;

}
.navbar-dark .navbar-brand,.navbar-dark .navbar-nav .nav-link{
  color: white;
}
.btn-success{
  color: white!important;
  background: none!important;
  border: none!important;
  margin-top: 5px;
}



@media only screen and (max-width: 900px){
  .navbar-expand-lg .navbar-collapse{
    display:block;
  }
  .navbar-dark .navbar-toggler{
    color: rgba(0,0,0,0.5);
    border-color: rgba(0,0,0,0.5);
  }
  /deep/.navbar-dark .navbar-toggler-icon{
    background-image: url("../../assets/img/nav.png");
  }
}
</style>

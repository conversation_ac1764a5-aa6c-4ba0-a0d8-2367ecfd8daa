<template>
  <div class="loginwap">
    <!--导航-->
    <!-- <headback></headback> -->

    <div class="register-wrapper">
      <p class="title">登录</p>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="0px" class="demo-ruleForm">
        <el-form-item prop="username">
          <el-input v-model="ruleForm.username" placeholder="手机号">
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input type="password" placeholder="密码" v-model="ruleForm.password">
          </el-input>
        </el-form-item>
        <el-button-group>
          <el-button style="width:100%" @click="submit" type="primary">登录</el-button>
          <div class="resbottom">
            <p class="login" @click="gotoForget">忘记密码</p>
            <p class="login" @click="gotoLogin">注册</p>
          </div>
        </el-button-group>
      </el-form>
      <el-row>
        <el-divider style="font-size: 13px;color: #999">其他登录方式</el-divider>
        <el-button circle  @click="gotoverification">
          <svg t="1633938750859" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9572" width="20" height="20"><path d="M736 64H288a71.76 71.76 0 0 0-71.68 71.68v752.64A71.76 71.76 0 0 0 288 960h448a71.76 71.76 0 0 0 71.68-71.68V135.68A71.76 71.76 0 0 0 736 64zM512 879.36a35.84 35.84 0 1 1 35.84-35.84A35.84 35.84 0 0 1 512 879.36z m233-116.48H279V189.44h466z" fill="#22B573" p-id="9573"></path></svg>
        </el-button>
        <el-button circle>
          <a href="" @click.prevent="wechatLogin" target="_blank">
            <svg t="1633663557712" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5369" width="20" height="20"><path d="M704 397.92c-15.04-140.96-151.04-251.36-316.48-251.36-176 0-317.92 124.16-317.92 277.28a267.36 267.36 0 0 0 129.12 224c8.8 5.6-29.12 85.28-19.68 90.08s28.48-11.2 48.8-26.56 39.52-29.76 48-26.88a362.56 362.56 0 0 0 112 17.44 376.16 376.16 0 0 0 57.44-4.48c36.96 84.8 133.44 145.44 246.56 145.44a305.12 305.12 0 0 0 88.16-12.96c4.48-1.28 21.76 11.36 39.2 24.16s35.36 25.76 39.68 24c13.76-5.76-25.44-69.92-12.96-77.44 65.76-40.48 108.48-105.92 108.48-180-0.16-120-111.04-217.12-250.4-222.72z m-109.12 167.2a28 28 0 1 1 27.68-28 27.84 27.84 0 0 1-27.68 28z m-165.76 54.72a204.64 204.64 0 0 0 1.44 24.32 314.72 314.72 0 0 1-42.88 2.88 302.08 302.08 0 0 1-103.36-17.76c-3.2-1.12-14.4-4.64-20.48 0a265.28 265.28 0 0 0-32 32 142.4 142.4 0 0 0 8.96-38.4c1.12-10.24-14.56-17.6-17.76-19.68a220 220 0 0 1-98.08-178.4c0-122.88 117.6-222.4 262.72-222.4 135.36 0 246.88 86.72 260.96 198.24-124.48 17.44-219.52 108.96-219.52 219.2z m331.68-54.72a28 28 0 1 1 27.68-28 27.68 27.68 0 0 1-27.68 28z" fill="#8DC81B" p-id="5370"></path><path d="M498.24 286.08a41.92 41.92 0 1 0 41.44 41.92 41.76 41.76 0 0 0-41.44-41.92zM276.96 286.08a41.92 41.92 0 1 0 41.6 41.92 41.6 41.6 0 0 0-41.6-41.92z" fill="#8DC81B" p-id="5371"></path></svg>
          </a>
        </el-button>
        <el-button circle>
          <a href="" @click.prevent="qqLogin" target="_blank">
              <img src="../../assets/img/qq.png" alt="" width="20px" height="20px">
<!--            <svg t="1633663460453" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4543" width="20" height="20"><path d="M146.08 742.08c16 11.68 32-8.96 43.2-29.6s18.56-38.4 20.48-32 3.68 12.64 6.08 19.36A275.04 275.04 0 0 0 266.56 784c4.96 5.6-29.92 16-50.72 51.36s5.92 100 109.6 100c133.76 0 163.68-47.04 166.4-46.88a188.8 188.8 0 0 0 21.28 0 113.44 113.44 0 0 0 19.52 0c3.04 0 18.56 11.84 43.52 24a216.96 216.96 0 0 0 117.92 22.88c153.28-16 129.92-67.52 117.92-100s-57.12-49.6-55.04-51.84a230.72 230.72 0 0 0 55.04-102.72c2.4-7.36 12 12 24.32 32s29.6 41.6 43.68 29.6c11.36-9.76 34.24-50.08 11.36-135.36s-66.88-104.96-65.92-120.96a305.6 305.6 0 0 0-0.8-62.88C816 393.6 800 400 800 393.6 800 224 672 87.36 515.36 87.36S231.36 224 231.36 393.6c0 13.44-11.84 3.52-21.6 29.28-4.48 11.52 1.92 18.88 0 62.88 0 11.84-51.68 65.28-71.84 120.96s-22.88 113.28 8.16 135.36z" fill="#68A5E1" p-id="4544"></path></svg>-->
          </a>
        </el-button>
      </el-row>
    </div>
  </div>
</template>

<script>
  import { mapMutations } from 'vuex'
  import headback from "../headback";
  import {isWeiXin} from "../../utils/common";
  export default {
    inject: ['reload'],
    name: "login",
    components:{
      headback
    },
    data() {
      return {
        ruleForm: {
          username: "",
          password: "",
        },

        // 校验
        rules: {
          username: [{ required: true, message: "请输入手机号", trigger: "blur" }],
          password: [{ required: true, message: "请输入密码", trigger: "blur" }],
        }
      }
    },
    mounted () {

    },
    methods: {
      ...mapMutations(['changeLogin']),
      //微信
      wechatLogin(){
        if (isWeiXin()) {
          // window.open('http://main.radirhino.com/auth/weixin', '_blank');
          window.open('https://main.radirhino.com/auth/weixinweb', '_blank');
        } else {
          window.open('https://main.radirhino.com/auth/weixinweb', '_blank');
        }
        // 监听
        this.timer = setInterval(() => {
          let isLogin = localStorage.getItem('IsLogin');
          if (isLogin === 'true') {
            clearInterval(this.timer)
            localStorage.removeItem('IsLogin');
            // window.close();
            this.$router.go(-1);
          }
          this.reload()
        }, 2000);
      },
      // QQ登录
      qqLogin(){
        window.open('https://main.radirhino.com/auth/qq', '_blank');
        // 监听
        this.timer = setInterval(() => {
          let isLogin = localStorage.getItem('IsLogin');
          if (isLogin === 'true') {
            clearInterval(this.timer)
            localStorage.removeItem('IsLogin');
            // window.close();
            this.$router.go(-1);
          }
          this.reload()
        }, 2000);
      },

      // /**
      //  * @description 提交表单
      //  */
      // 提交登录信息
      submit() {
        if (this.ruleForm.username == '') {
          this.$message.error('请填写手机号')
          return
        } else if (this.ruleForm.password == ''){
          this.$message.error('请填写密码')
          return
        }
        let _this = this;
        this.$refs.ruleForm.validate(valid => {
          if (valid) {
            this.$axios.post(this.url + '/api/user/login',{
              phone: this.ruleForm.username,
              password: this.ruleForm.password,
              type: 'pass'
            }).then(res => {
              if (res.data.code !== 200) {
                // 提示
                this.$message.error(res.data.msg);
              } else {
                _this.userToken = 'Bearer ' + res.data.data.token;
                _this.userInfo = res.data.data.user;
                _this.changeLogin({
                  Authorization: _this.userToken,
                  UserInfo: _this.userInfo
                });
                // _this.$router.push('/');
                _this.$router.go(-1);
              }
            }).catch(err => {
            });
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      },
      // <!--进入注册页-->
      gotoLogin() {
        this.$router.push({
          path: "/register"
        });
      },
      gotoverification(){
        this.$router.push({
          path: "/verification"
        });
      },
      //忘记密码
      gotoForget(){
        this.$router.push({
          path: "/forget"
        });
      },
    }
  };
</script>

<style scoped lang="scss">
  .register-wrapper {
    max-width: 340px;
    margin: 0 auto;
    margin-top: 80px;
    margin-bottom: 60px;
    background: #fff;
    padding: 20px 40px;
    border-radius: 10px;
    overflow: hidden;
  }
  .title {
    font-size: 26px;
    line-height: 50px;
    font-weight: bold;
    margin: 10px;
    text-align: center;
    display: block;
  }
  >>> .el-input__inner{
    height: 42px;
  }
  >>> .el-input-group__append{
    padding: 0;
  }
  .el-button--small.is-circle{
    padding:9px;
  }
  .login {
    margin-top: 10px;
    font-size: 14px;
    line-height: 22px;
    color: #1ab2ff;
    cursor: pointer;
    width: 160px;
    text-align: left;
    p{
      margin-top: 10px!important;
    }
  }
  .login:hover {
    color: #2c2fd6;
  }
  .resbottom{
    width: 100%;
    display: flex;
    justify-content: space-between;
    p:last-child{
      text-align: right;
    }
  }

  .navbar-expand-lg .navbar-collapse{
    display:flex;
    justify-content:space-between;
  }
  .navbar{
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    width: 100%;
    z-index: 99;
    /*position: relative;*/
    /*top: 0;*/
    /*position: absolute;*/
    /*top:0;*/
    /*left: 0;*/
  }
  button{
    outline: none;
  }
  .bg-info{
    /*background-color: rgba(0,0,0,0)!important;*/
    background-color: rgb(17, 19, 42)!important;

  }
  .navbar-dark .navbar-brand,.navbar-dark .navbar-nav .nav-link{
    color: white;
  }
  .btn-success{
    color: white!important;
    background: none!important;
    border: none!important;
    margin-top: 5px;
  }



  @media only screen and (max-width: 900px){
    .navbar-expand-lg .navbar-collapse{
      display:block;
    }
    .navbar-dark .navbar-toggler{
      color: rgba(0,0,0,0.5);
      border-color: rgba(0,0,0,0.5);
    }
    /deep/.navbar-dark .navbar-toggler-icon{
      background-image: url("../../assets/img/nav.png");
    }
  }
</style>

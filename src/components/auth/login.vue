<template>
  <div class="login-view">
    <div class="body">
      <div class="title">会员登录</div>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
        <el-form-item prop="username" class="username">
          <el-input v-model="ruleForm.username" placeholder="输入手机号/邮箱">
          </el-input>
        </el-form-item>
        <el-form-item prop="password" class="password">
          <el-input type="password" placeholder="输入登录密码" v-model="ruleForm.password">
          </el-input>
        </el-form-item>
        <el-button type="primary" @click="submit">登录</el-button>
        <!-- 其他登录方式 -->
        <div class="other">
          <div class="text1">其他登录方式</div>
          <div class="itembox">
            <div class="item" @click="gotoverification">
              <img src="../../assets/img/img39.png" class="icon">
              <div class="text">手机登录</div>
            </div>
            <div class="item" @click.prevent="wechatLogin">
              <img src="../../assets/img/img40.png" class="icon">
              <div class="text">微信登录</div>
            </div>
            <div class="item" @click.prevent="qqLogin">
              <img src="../../assets/img/img41.png" class="icon">
              <div class="text">QQ登录</div>
            </div>
          </div>
        </div>
        <!-- 找回密码，注册 -->
        <div class="other02">
          <div class="xg-btn" @click="gotoForget">忘记密码</div>
          <div class="xg-btn" @click="gotoLogin">注册会员</div>
        </div>
      </el-form>
    </div>

  </div>
</template>

<script>
  import { mapMutations } from 'vuex'

  import {isWeiXin} from "../../utils/common";
  export default {
    inject: ['reload'],
    name: "login",
    components:{

    },
    data() {
      return {
        ruleForm: {
          username: "",
          password: "",
        },

        // 校验
        rules: {
          username: [{ required: true, message: "请输入手机号", trigger: "blur" }],
          password: [{ required: true, message: "请输入密码", trigger: "blur" }],
        }
      }
    },
    mounted () {

    },
    methods: {
      ...mapMutations(['changeLogin']),
      //微信
      wechatLogin(){
        if (isWeiXin()) {
          // window.open('http://main.radirhino.com/auth/weixin', '_blank');
          window.open('https://main.radirhino.com/auth/weixinweb', '_blank');
        } else {
          window.open('https://main.radirhino.com/auth/weixinweb', '_blank');
        }
        // 监听
        this.timer = setInterval(() => {
          let isLogin = localStorage.getItem('IsLogin');
          if (isLogin === 'true') {
            clearInterval(this.timer)
            localStorage.removeItem('IsLogin');
            // window.close();
            this.$router.go(-1);
          }
          this.reload()
        }, 2000);
      },
      // QQ登录
      qqLogin(){
        window.open('https://main.radirhino.com/auth/qq', '_blank');
        // 监听
        this.timer = setInterval(() => {
          let isLogin = localStorage.getItem('IsLogin');
          if (isLogin === 'true') {
            clearInterval(this.timer)
            localStorage.removeItem('IsLogin');
            // window.close();
            this.$router.go(-1);
          }
          this.reload()
        }, 2000);
      },

      // /**
      //  * @description 提交表单
      //  */
      // 提交登录信息
      submit() {
        if (this.ruleForm.username == '') {
          this.$message.error('请填写手机号')
          return
        } else if (this.ruleForm.password == ''){
          this.$message.error('请填写密码')
          return
        }
        let _this = this;
        this.$refs.ruleForm.validate(valid => {
          if (valid) {
            this.$axios.post(this.url + '/api/user/login',{
              phone: this.ruleForm.username,
              password: this.ruleForm.password,
              type: 'pass'
            }).then(res => {
              if (res.data.code !== 200) {
                // 提示
                this.$message.error(res.data.msg);
              } else {
                _this.userToken = 'Bearer ' + res.data.data.token;
                _this.userInfo = res.data.data.user;
                _this.changeLogin({
                  Authorization: _this.userToken,
                  UserInfo: _this.userInfo
                });
                // _this.$router.push('/');
                _this.$router.go(-1);
              }
            }).catch(err => {
            });
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      },
      // <!--进入注册页-->
      gotoLogin() {
        this.$router.push({
          path: "/register"
        });
      },
      gotoverification(){
        this.$router.push({
          path: "/verification"
        });
      },
      //忘记密码
      gotoForget(){
        this.$router.push({
          path: "/forget"
        });
      },
    }
  };
</script>

<style scoped lang="scss">
.login-view{
  padding:140px 0;
  background:url(../../assets/img/img38.jpg) no-repeat center;background-size:cover;
  .body{
    margin-right:375px;margin-left:auto;
    width:340px;border-radius: 4px;background:rgba(255,255,255,0.85);padding:30px;
    .title{font-size:16px;color:#242b33;text-align:center;line-height:1.2;margin-bottom:25px;font-weight:bold;}
    .el-form{
      .el-input,.el-input__inner,
      .el-button{height:40px;line-height:40px;}
      .username .el-input,
      .password .el-input{padding-left:20px;
        &::before{content:"";display:block;width:20px;height:20px;background:url(../../assets/img/img42.png) no-repeat center;background-size:100%;position:absolute;top:50%;transform:translateY(-50%);left:10px;}
      }
      .password .el-input:before{background-image:url(../../assets/img/img43.png)}
      .el-input{border:1px solid #c0c4cc;border-radius:4px;background-color:#fff;}
      /deep/ .el-input__inner{border:0!important;}
      .el-button{width:100%;border-radius:4px;text-align:center;padding:0;font-size:14px;color:#fff;letter-spacing:2px;}
    }
    .other{
      margin-top:20px;
      .text1{font-size:12px;color:#687d93;text-align:left;margin-bottom:20px;cursor: pointer;
        &:hover{color:#0d6efd;}
      }
    }
    .itembox{display:flex;align-items:center;justify-content:flex-start;padding-bottom:20px;border-bottom:1px solid #c0c4cc;margin-bottom:20px;}
    .item{
      font-size:12px;color:#687d93;margin-right:20px;display:flex;align-items:center;cursor: pointer;
      &:hover{color:#0d6efd;}
      .icon{width:20px;flex-shrink:0;margin-right:5px;}
    }
    .other02{
      text-align:right;display:flex;align-items:center;justify-content:flex-end;
      .xg-btn{font-size:12px;color:#687d93;line-height:1.2;margin-right:10px;cursor: pointer;outline:none;
        &:hover{color:#0d6efd;}
        &:last-child{margin-right:0;}
      }
    }
  }
}
@media only screen and (max-width: 751px){
  .login-view{padding:30px 20px;
    .body{width:100%;}
  }
}
</style>

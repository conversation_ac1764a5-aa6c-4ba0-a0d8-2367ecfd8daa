<template>
  <div class="forget-view">
    <div class="body">
      <div class="title">找回密码</div>
      <el-form :model="dynamicValidateForm" ref="dynamicValidateForm">
        <el-form-item
          class="email"
          :rules="[
            { required: true, message: '请输入邮箱地址', trigger: 'blur' },
            {
              type: 'email',
              message: '请输入正确的邮箱地址',
              trigger: ['blur', 'change'],
            },
          ]"
        >
          <el-input
            v-model="dynamicValidateForm.email"
            placeholder="输入您注册时的邮箱"
          >
          </el-input>
        </el-form-item>
        <el-button type="primary" @click="submit()">重置</el-button>
        <!-- 返回 -->
        <div class="other">
          <div class="text1" @click="goback">&lt; 返回</div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: "forget",
  components: {},
  data() {
    return {
      dynamicValidateForm: {
        email: "",
      },
    };
  },
  mounted() {},
  methods: {
    //忘记密码
    submit() {
      this.$axios
        .post(this.url + "/api/user/forget-password", {
          email: this.dynamicValidateForm.email,
        })
        .then((res) => {
          if (res.data.code !== 200) {
            // 注册错误
            alert(res.data.msg);
          } else {
            // 提示成功 并 跳转登陆
            this.$router.push("/login");
          }
        })
        .catch((err) => {});
    },
    // 返回方法
    goback() {
      this.$router.go(-1);
    },
  },
};
</script>

<style scoped lang="scss">
.forget-view {
  padding: 140px 0;
  background: url(../../assets/img/img38.jpg) no-repeat center;
  background-size: cover;
  .body {
    margin-right: 375px;
    margin-left: auto;
    width: 340px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.85);
    padding: 30px;
    .title {
      font-size: 16px;
      color: #242b33;
      text-align: center;
      line-height: 1.2;
      margin-bottom: 25px;
      font-weight: bold;
    }
    .el-form {
      .el-input,
      .el-input__inner,
      .el-button {
        height: 40px;
        line-height: 40px;
      }
      .el-input {
        border: 1px solid #c0c4cc;
        border-radius: 4px;
        background-color: #fff;
        font-size: 12px;
      }
      /deep/ .el-input__inner {
        border: 0 !important;
      }
      .el-button {
        width: 100%;
        border-radius: 4px;
        text-align: center;
        padding: 0;
        font-size: 14px;
        color: #fff;
        letter-spacing: 2px;
      }
    }
    .other {
      margin-top: 20px;
      .text1 {
        font-size: 12px;
        color: #687d93;
        text-align: left;
        cursor: pointer;
        &:hover {
          color: #0d6efd;
        }
      }
    }
  }
}
@media only screen and (min-width: 751px) {
  .forget-view {
    min-height: 650px;
  }
}
@media only screen and (max-width: 751px) {
  .forget-view {
    padding: 80px 20px;
    .body {
      width: 100%;
    }
  }
}
</style>

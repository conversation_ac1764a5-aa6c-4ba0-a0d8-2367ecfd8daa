<template>
  <div>
    <!-- 头部 -->
    <div class="xg-header" :class="{ ny: ny, bg: bg }">
      <!-- pc 端导航 -->
      <div class="xg-flex pc-nav">
        <!-- logo -->
        <router-link to="/" class="xg-flex logo">
          <img src="@/assets/img/logo.png" />
        </router-link>
        <!-- 导航 -->
        <ul class="pc-menu">
          <li class="sub">
            <router-link :to="{ name: 'course', query: { level: 1 } }"
              >课程</router-link
            >
            <ul class="pc-sub-menu">
              <div class="pc-nav-box">
                <li v-for="(cate, key) in courseCates" :key="key"   @mouseover.stop="catesrt(cate.id)">
                  <router-link
                    :to="{
                      name: 'course',
                      query: {
                        id: cate.id,
                        parent_name: '',
                        parent_id: cate.id,
                        level: cate.level,
                        cateStr: cate.id,
                      },
                    }"
                    >{{ cate.name }}</router-link
                  >

                  <!-- 【添加三级导航】三级下拉分类 start -->
                  <div
                    class="three-menu"
                    v-if="cate.children_category.length > 0"
                  >
                    <div class="three-menu-itembox">
                      <ul class="pc-sub-menu-child">
                        <div class="pc-nav-box">
                          <li
                            class="item1"
                            v-for="(
                              child_cate, child_index
                            ) in cate.children_category"
                            :key="child_index"
                             @mouseover.stop="catesrt(cate.id, child_cate.id)"
                          >
                            <router-link
                              :to="{
                                name: 'course',
                                query: {
                                  id: child_cate.id,
                                  parent_name: cate.name,
                                  parent_id: child_cate.parent_id,
                                  level: child_cate.level,
                                  cateStr: cate.id + ',' + child_cate.id,
                                },
                              }"
                              class="menu-item"
                              >{{ child_cate.name }}</router-link
                            >
                            <div
                              class="three-menu2"
                              v-if="child_cate.children_category.length > 0&&cateArr[1]==child_cate.id"
                            >
                              <div class="three-menu-itembox">
                                 <ul class="pc-sub-menu-child">
                                  <div class="pc-nav-box">
                                    <li
                                    class="item2"
                                      v-for="(
                                        child_cate2, child_index2
                                      ) in child_cate.children_category"
                                      :key="child_index2"
                                       @mouseover.stop="catesrt(cate.id, child_cate.id, child_cate2.id)"
                                    >
                                      <router-link
                                        :to="{
                                          name: 'course',
                                          query: {
                                            id: child_cate2.id,
                                            parent_name: child_cate.name,
                                            parent_id: child_cate2.parent_id,
                                            level: child_cate2.level,
                                            cateStr: cate.id + ',' + child_cate.id +',' + child_cate2.id,
                                          },
                                        }"
                                        class="menu-item" >{{ child_cate2.name }}</router-link>

                                          <div
                                              class="three-menu2"
                                              v-if="child_cate2.children_category.length > 0&&cateArr[2]==child_cate2.id"
                                            >
                                              <div class="three-menu-itembox">
                                                <ul class="pc-sub-menu-child">
                                                  <div class="pc-nav-box">
                                                    <li
                                                    class="item2"
                                                      v-for="(
                                                        child_cate3, child_index2
                                                      ) in child_cate2.children_category"
                                                      :key="child_index2"
                                                      @mouseover.stop="catesrt(cate.id, child_cate.id, child_cate2.id, child_cate3)"
                                                    >
                                                      <router-link
                                                        :to="{
                                                          name: 'course',
                                                          query: {
                                                            id: child_cate3.id,
                                                            parent_name: child_cate2.name,
                                                            parent_id: child_cate3.parent_id,
                                                            level: child_cate3.level,
                                                            cateStr: cate.id + ',' + child_cate.id +',' + child_cate2.id +',' + child_cate3.id,
                                                          },
                                                        }"
                                                        class="menu-item" >{{ child_cate3.name }}</router-link>
                                                        
                                                        
                                                    </li>
                                                  </div>
                                                </ul>
                                              </div>
                                            </div>

                                    </li>
                                  </div>
                                 </ul>
                              </div>
                            </div>
                          </li>
                        </div>
                      </ul>
                    </div>
                  </div>
                  <div
                    class="arrow"
                    v-if="cate.children_category.length > 0"
                  ></div>
                  <!-- 【添加三级导航】三级下拉分类 end -->
                </li>
              </div>
            </ul>
          </li>
          <li class="sub">
            <router-link :to="{ path: '/plug' }">插件</router-link>
            <ul class="pc-sub-menu">
              <div class="pc-nav-box">
                <li v-for="(cate, key) in plugCates" :key="key">
                  <router-link
                    :to="{
                      name: 'plugcate',
                      query: {
                        cate_id: cate.id,
                        parent_name: '',
                        parent_id: cate.id,
                        level: cate.level,
                      },
                    }"
                    >{{ cate.name }}</router-link
                  >

                  <!-- 【添加三级导航】三级下拉分类 start -->
                  <div
                    class="three-menu"
                    v-if="cate.children_category.length > 0"
                  >
                    <!-- <div class="three-menu-itembox">
                      <router-link
                        :to="{
                          name: 'plugcate',
                          query: {
                            cate_id: child_cate.id,
                            parent_name: child_cate.name,
                            parent_id: child_cate.parent_id,
                            level: child_cate.level,
                          },
                        }"
                        class="menu-item"
                        v-for="(
                          child_cate, child_index
                        ) in cate.children_category"
                        :key="child_index"
                        >{{ child_cate.name }}</router-link
                      >
                    </div> -->
                    
                    <div class="three-menu-itembox">
                      <ul class="pc-sub-menu-child">
                        <div class="pc-nav-box">
                          <li
                            class="item1"
                            v-for="(
                              child_cate, child_index
                            ) in cate.children_category"
                            :key="child_index"
                             @mouseover.stop="catesrt(cate.id, child_cate.id)"
                          >
                            <router-link
                              :to="{
                                name: 'plugcate',
                                query: {
                                  cate_id: child_cate.id,
                                  parent_name: cate.name,
                                  parent_id: child_cate.parent_id,
                                  level: child_cate.level,
                                  cateStr: cate.id + ',' + child_cate.id,
                                },
                              }"
                              class="menu-item"
                              >{{ child_cate.name }}</router-link
                            >
                            <div
                              class="three-menu2"
                              v-if="child_cate.children_category.length > 0&&cateArr[1]==child_cate.id"
                            >
                              <div class="three-menu-itembox">
                                 <ul class="pc-sub-menu-child">
                                  <div class="pc-nav-box">
                                    <li
                                    class="item2"
                                      v-for="(
                                        child_cate2, child_index2
                                      ) in child_cate.children_category"
                                      :key="child_index2"
                                       @mouseover.stop="catesrt(cate.id, child_cate.id, child_cate2.id)"
                                    >
                                      <router-link
                                        :to="{
                                          name: 'plugcate',
                                          query: {
                                            cate_id: child_cate2.id,
                                            parent_name: child_cate.name,
                                            parent_id: child_cate2.parent_id,
                                            level: child_cate2.level,
                                            cateStr: cate.id + ',' + child_cate.id +',' + child_cate2.id,
                                          },
                                        }"
                                        class="menu-item" >{{ child_cate2.name }}</router-link>

                                          <div
                                              class="three-menu2"
                                              v-if="child_cate2.children_category.length > 0&&cateArr[2]==child_cate2.id"
                                            >
                                              <div class="three-menu-itembox">
                                                <ul class="pc-sub-menu-child">
                                                  <div class="pc-nav-box">
                                                    <li
                                                    class="item2"
                                                      v-for="(
                                                        child_cate3, child_index2
                                                      ) in child_cate2.children_category"
                                                      :key="child_index2"
                                                      @mouseover.stop="catesrt(cate.id, child_cate.id, child_cate2.id, child_cate3)"
                                                    >
                                                      <router-link
                                                        :to="{
                                                          name: 'plugcate',
                                                          query: {
                                                            cate_id: child_cate3.id,
                                                            parent_name: child_cate2.name,
                                                            parent_id: child_cate3.parent_id,
                                                            level: child_cate3.level,
                                                            cateStr: cate.id + ',' + child_cate.id +',' + child_cate2.id +',' + child_cate3.id,
                                                          },
                                                        }"
                                                        class="menu-item" >{{ child_cate3.name }}</router-link>
                                                        
                                                        
                                                    </li>
                                                  </div>
                                                </ul>
                                              </div>
                                            </div>

                                    </li>
                                  </div>
                                 </ul>
                              </div>
                            </div>
                          </li>
                        </div>
                      </ul>
                    </div>
                  </div>
                  <div
                    class="arrow"
                    v-if="cate.children_category.length > 0"
                  ></div>
                  <!-- 【添加三级导航】三级下拉分类 end -->
                </li>
              </div>
            </ul>
          </li>
          <li>
            <router-link :to="{ path: '/periphery' }">素材</router-link>
            <ul class="pc-sub-menu">
              <div class="pc-nav-box">
                <li v-for="(cate, key) in goodsCates" :key="key">
                  <router-link
                    :to="{
                      name: 'periphery',
                      query: {
                        cate_id: cate.id,
                        parent_name: '',
                        parent_id: cate.id,
                        level: cate.level,
                      },
                    }"
                    >{{ cate.name }}</router-link
                  >

                  <!-- 【添加三级导航】三级下拉分类 start -->
                  <div
                    class="three-menu"
                    v-if="cate.children_category.length > 0"
                  >
                    <!-- <div class="three-menu-itembox">
                      <router-link
                        :to="{
                          name: 'periphery',
                          query: {
                            cate_id: child_cate.id,
                            parent_name: child_cate.name,
                            parent_id: child_cate.parent_id,
                            level: child_cate.level,
                          },
                        }"
                        class="menu-item"
                        v-for="(
                          child_cate, child_index
                        ) in cate.children_category"
                        :key="child_index"
                        >{{ child_cate.name }}</router-link
                      >
                    </div> -->
                    
                    <div class="three-menu-itembox">
                      <ul class="pc-sub-menu-child">
                        <div class="pc-nav-box">
                          <li
                            class="item1"
                            v-for="(
                              child_cate, child_index
                            ) in cate.children_category"
                            :key="child_index"
                             @mouseover.stop="catesrt(cate.id, child_cate.id)"
                          >
                            <router-link
                              :to="{
                                name: 'periphery',
                                query: {
                                  cate_id: child_cate.id,
                                  parent_name: cate.name,
                                  parent_id: child_cate.parent_id,
                                  level: child_cate.level,
                                  cateStr: cate.id + ',' + child_cate.id,
                                },
                              }"
                              class="menu-item"
                              >{{ child_cate.name }}</router-link
                            >
                            <div
                              class="three-menu2"
                              v-if="child_cate.children_category.length > 0&&cateArr[1]==child_cate.id"
                            >
                              <div class="three-menu-itembox">
                                 <ul class="pc-sub-menu-child">
                                  <div class="pc-nav-box">
                                    <li
                                    class="item2"
                                      v-for="(
                                        child_cate2, child_index2
                                      ) in child_cate.children_category"
                                      :key="child_index2"
                                       @mouseover.stop="catesrt(cate.id, child_cate.id, child_cate2.id)"
                                    >
                                      <router-link
                                        :to="{
                                          name: 'periphery',
                                          query: {
                                            cate_id: child_cate2.id,
                                            parent_name: child_cate.name,
                                            parent_id: child_cate2.parent_id,
                                            level: child_cate2.level,
                                            cateStr: cate.id + ',' + child_cate.id +',' + child_cate2.id,
                                          },
                                        }"
                                        class="menu-item" >{{ child_cate2.name }}</router-link>

                                          <div
                                              class="three-menu2"
                                              v-if="child_cate2.children_category.length > 0&&cateArr[2]==child_cate2.id"
                                            >
                                              <div class="three-menu-itembox">
                                                <ul class="pc-sub-menu-child">
                                                  <div class="pc-nav-box">
                                                    <li
                                                    class="item2"
                                                      v-for="(
                                                        child_cate3, child_index2
                                                      ) in child_cate2.children_category"
                                                      :key="child_index2"
                                                      @mouseover.stop="catesrt(cate.id, child_cate.id, child_cate2.id, child_cate3.id)"
                                                    >
                                                      <router-link
                                                        :to="{
                                                          name: 'periphery',
                                                          query: {
                                                            cate_id: child_cate3.id,
                                                            parent_name: child_cate2.name,
                                                            parent_id: child_cate3.parent_id,
                                                            level: child_cate3.level,
                                                            cateStr: cate.id + ',' + child_cate.id +',' + child_cate2.id +',' + child_cate3.id,
                                                          },
                                                        }"
                                                        class="menu-item" >{{ child_cate3.name }}</router-link>
                                                        
                                                        
                                                    </li>
                                                  </div>
                                                </ul>
                                              </div>
                                            </div>

                                    </li>
                                  </div>
                                 </ul>
                              </div>
                            </div>
                          </li>
                        </div>
                      </ul>
                    </div>
                  </div>
                  <div
                    class="arrow"
                    v-if="cate.children_category.length > 0"
                  ></div>
                  <!-- 【添加三级导航】三级下拉分类 end -->
                </li>
              </div>
            </ul>
          </li>
          <li><router-link to="/news">新闻</router-link></li>
          <li><router-link to="/ai/gallery">犀光AI</router-link></li>
          <li><router-link to="/help">关于</router-link></li>
          
        </ul>
        <!-- 搜索 -->
        <div class="xg-flex search">
          <input
            type="text"
            placeholder="输入关键词"
            v-model="keywords"
            @keyup.enter="seachEnter"
          />
          <div class="icon" @click="search()">
            <img src="@/assets/img/search.png" alt="" title="" />
          </div>
        </div>
        <!-- 登录注册按钮 -->
        <div class="xg-flex login">
          <!-- 已经登录的状态 -->
          <div class="top" v-if="user.name">
            <!-- 头像 -->
            <div class="avatar" @click="goToInfo">
              <img
                v-if="user.avatar"
                :src="user.avatar"
                :alt="user.name"
                :title="user.name"
              />
            </div>
            <!-- 下拉框 -->
            <div class="info">
              <div>
                <div class="name">{{ user.name }}</div>
                <div class="list">
                  <router-link to="/user/order" class="list-item"
                    >我的订单</router-link
                  >
                  <router-link to="/user/course" class="list-item"
                    >我的课程</router-link
                  >
                  <router-link to="/user/info" class="list-item"
                    >个人资料</router-link
                  >
                </div>
                <div class="xg-btn" v-if="user.bind">
                  <a
                    href="http://admin.radirhino.com/dist/#/"
                    target="_blank"
                    class="link-item"
                    style="color: black"
                    ><span>插件上传</span></a
                  >
                </div>
                <div class="xg-btn" @click="logout()"><span>退出</span></div>
              </div>
            </div>
          </div>
          <!-- 还未登录的状态 -->
          <div class="xg-flex bottom" v-else>
            <!-- 登录 -->
            <router-link to="/login" class="login-btn">登录</router-link>
            <!-- 注册 -->
            <router-link to="/register" class="register-btn">注册</router-link>
          </div>
        </div>
      </div>

      <!-- 移动端导航 -->
      <nav class="mob-nav" :class="{ active: isOpen }">
        <!-- 头部 -->
        <div class="mob-nav-head">
          <!-- logo -->
          <router-link to="/" class="nav-logo">
            <img src="@/assets/img/m-logo.png" />
          </router-link>
          <!-- 菜单按钮 -->
          <div class="nav-btn" @click="setOpen"><i></i><i></i><i></i></div>
        </div>
        <!-- 导航菜单 -->
        <div class="mob-nav-body">
          <!-- 用户信息（登录后展示） -->
          <div class="xg-flex user-nav" v-if="user.name">
            <!-- 用户头像 -->
            <div class="avatar">
              <img
                v-if="user.avatar"
                :src="user.avatar"
                :alt="user.name"
                :title="user.name"
              />
            </div>
            <div class="name">{{ user.name }}</div>
            <!-- 退出 -->
            <div class="text" @click="logout()">退出</div>
          </div>

          <!-- 手机导航 -->
          <div class="mob-nav-menu">
            <!--            <router-link class="first-menu" to="/"-->
            <!--              ><div class="text" @click="isOpen = false">课程</div></router-link-->
            <!--            >-->
            <div
              class="sub first-menu"
              @click="addClass(1)"
              :class="{ active: 1 === current }"
            >
              <div class="text">课程</div>
              <div class="mob-sub-menu">
                <div
                  class="sub-item"
                  v-for="(cate, key) in courseCates"
                  :key="key"
                >
                  <router-link
                    :to="{
                      name: 'course',
                      query: {
                        id: cate.id,
                        parent_name: '',
                        parent_id: cate.id,
                        level: cate.level,
                      },
                    }"
                    class="first-menu"
                  >
                    <div class="text" @click="isOpen = false">
                      {{ cate.name }}
                    </div>
                  </router-link>

                  <!-- 【添加三级导航】三级下拉分类 start -->
                  <div >
                      <div
                          class="sub-item"
                          v-for="(item1, key1) in cate.children_category"
                          :key="key1"
                        >
                        <router-link
                            :to="{
                                name: 'course',
                                query: {
                                  id: item1.id,
                                  parent_name: cate.name,
                                  parent_id: item1.parent_id,
                                  level: item1.level,
                                  cateStr: cate.id + ',' + item1.id,
                                },
                              }"
                            class="menu-item"
                            >&nbsp;&nbsp;{{ item1.name }}</router-link
                          >
                          
                          <div
                              class="sub-item"
                              v-for="(item2, key2) in item1.children_category"
                              :key="key2"
                            >
                            <router-link
                                :to="{
                                name: 'course',
                                query: {
                                  id: item2.id,
                                  parent_name: item1.name,
                                  parent_id: item2.parent_id,
                                  level: item2.level,
                                  cateStr: cate.id + ',' + item1.id + ',' + item2.id,
                                },
                              }"
                                class="menu-item"
                                >&nbsp;&nbsp;&nbsp;&nbsp;{{ item2.name }}</router-link
                              >
                              
                              <div
                                  class="sub-item"
                                  v-for="(item3, key3) in item2.children_category"
                                  :key="key3"
                                >
                                <router-link
                                    :to="{
                                      name: 'course',
                                      query: {
                                        id: item3.id,
                                        parent_name: item2.name,
                                        parent_id: item3.parent_id,
                                        level: item3.level,
                                        cateStr: cate.id + ',' + item1.id + ',' + item2.id+ ',' + item3.id,
                                      },
                                    }"
                                    class="menu-item"
                                    >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{ item3.name }}</router-link
                                  >
                              </div>
                          </div>
                      </div>
                    
                  </div>
                  <!-- 【添加三级导航】三级下拉分类 end -->
                </div>
              </div>
            </div>
            <div
              class="sub first-menu"
              @click="addClass(2)"
              :class="{ active: 2 === current }"
            >
              <div class="text">插件</div>
              <div class="mob-sub-menu">
                <div
                    v-for="(cate, key) in plugCates"
                    :key="key">
                  <router-link
                    class="first-menu"
                    :to="{
                      name: 'plugcate',
                      query: {
                        cate_id: cate.id,
                        parent_name: '',
                        parent_id: cate.id,
                        level: cate.level,
                      },
                    }"
                  >
                    <div class="text" @click="isOpen = false">
                      {{ cate.name }}
                    </div>
                  </router-link>
                  
                  <div >
                      <div
                          class="sub-item"
                          v-for="(item1, key1) in cate.children_category"
                          :key="key1"
                        >
                        <router-link
                            :to="{
                                name: 'plugcate',
                                query: {
                                  cate_id: item1.id,
                                  parent_name: cate.name,
                                  parent_id: item1.parent_id,
                                  level: item1.level,
                                  cateStr: cate.id + ',' + item1.id,
                                },
                              }"
                            class="menu-item"
                            >&nbsp;&nbsp;{{ item1.name }}</router-link
                          >
                          
                          <div
                              class="sub-item"
                              v-for="(item2, key2) in item1.children_category"
                              :key="key2"
                            >
                            <router-link
                                :to="{
                                name: 'plugcate',
                                query: {
                                  cate_id: item2.id,
                                  parent_name: item1.name,
                                  parent_id: item2.parent_id,
                                  level: item2.level,
                                  cateStr: cate.id + ',' + item1.id + ',' + item2.id,
                                },
                              }"
                                class="menu-item"
                                >&nbsp;&nbsp;&nbsp;&nbsp;{{ item2.name }}</router-link
                              >
                              
                              <div
                                  class="sub-item"
                                  v-for="(item3, key3) in item2.children_category"
                                  :key="key3"
                                >
                                <router-link
                                    :to="{
                                      name: 'plugcate',
                                      query: {
                                        cate_id: item3.id,
                                        parent_name: item2.name,
                                        parent_id: item3.parent_id,
                                        level: item3.level,
                                        cateStr: cate.id + ',' + item1.id + ',' + item2.id+ ',' + item3.id,
                                      },
                                    }"
                                    class="menu-item"
                                    >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{ item3.name }}</router-link
                                  >
                              </div>
                          </div>
                      </div>
                    
                  </div>
                </div>
             
              </div>
            </div>
            <div
              class="sub first-menu"
              @click="addClass(3)"
              :class="{ active: 3 === current }"
            >
              <div class="text">素材</div>
              <div class="mob-sub-menu">
                <div
                  v-for="(cate, key) in goodsCates"
                  :key="key">
                  <router-link
                    class="first-menu"
                    :to="{
                      name: 'periphery',
                      query: {
                        cate_id: cate.id,
                        parent_name: '',
                        parent_id: cate.id,
                        level: cate.level,
                      },
                    }"
                  >
                    <div class="text" @click="isOpen = false">
                      {{ cate.name }}
                    </div>
                  </router-link>
                  
                  <div >
                      <div
                          class="sub-item"
                          v-for="(item1, key1) in cate.children_category"
                          :key="key1"
                        >
                        <router-link
                            :to="{
                                name: 'periphery',
                                query: {
                                  cate_id: item1.id,
                                  parent_name: cate.name,
                                  parent_id: item1.parent_id,
                                  level: item1.level,
                                  cateStr: cate.id + ',' + item1.id,
                                },
                              }"
                            class="menu-item"
                            >&nbsp;&nbsp;{{ item1.name }}</router-link
                          >
                          
                          <div
                              class="sub-item"
                              v-for="(item2, key2) in item1.children_category"
                              :key="key2"
                            >
                            <router-link
                                :to="{
                                name: 'periphery',
                                query: {
                                  cate_id: item2.id,
                                  parent_name: item1.name,
                                  parent_id: item2.parent_id,
                                  level: item2.level,
                                  cateStr: cate.id + ',' + item1.id + ',' + item2.id,
                                },
                              }"
                                class="menu-item"
                                >&nbsp;&nbsp;&nbsp;&nbsp;{{ item2.name }}</router-link
                              >
                              
                              <div
                                  class="sub-item"
                                  v-for="(item3, key3) in item2.children_category"
                                  :key="key3"
                                >
                                <router-link
                                    :to="{
                                      name: 'periphery',
                                      query: {
                                        cate_id: item3.id,
                                        parent_name: item2.name,
                                        parent_id: item3.parent_id,
                                        level: item3.level,
                                        cateStr: cate.id + ',' + item1.id + ',' + item2.id+ ',' + item3.id,
                                      },
                                    }"
                                    class="menu-item"
                                    >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{ item3.name }}</router-link
                                  >
                              </div>
                          </div>
                      </div>
                    
                  </div>
                </div>
                
              </div>
            </div>
            <div class="first-menu">
              <div class="text">新闻</div>
            </div>
            <div class="first-menu">
              <div class="text">关于</div>
            </div>
          </div>

          <!-- 登录注册（未登录时展示） -->
          <div class="tool" v-if="!user.name">
            <router-link to="/login" class="xg-btn">登录</router-link>
            <router-link to="/register" class="xg-btn">注册</router-link>
          </div>
        </div>
      </nav>
    </div>
  </div>
</template>

<script>
export default {
  inject: ["reload"],
  name: "xg-header",
  data() {
    return {
      isOpen: false,
      // 页面滚动距离
      scroll: "",
      // 导航条添加 bg 类（首页下拉改变导航背景色的样式）
      bg: false,
      // 导航条添加 ny 类（判断导航栏是否处于内页）
      ny: false,
      // 课程分类 数据
      courseCates: [],
      // 插件分类 数据
      plugCates: [],
      // 周边分类 数据
      goodsCates: [],
      current: 0,
      //关键词
      keywords: "",
      //
      state: false,
      // 用户数据
      user: {
        avatar: "https://placekitten.com/g/30/30",
      },
      cateArr:[],
    };
  },
  watch: {
    $route: {
      handler(val, olval) {
        let user = JSON.parse(localStorage.getItem("UserInfo"));
        if (user) {
          this.state = true;
          this.user = user;
        }
        // 监听路由变化为内页时，导航设置内页样式
        val.path != "/" ? (this.ny = true) : (this.ny = false);
        // 路由变化，关闭移动端菜单
        this.isOpen = false;
      },
    },
  },
  methods: {
    catesrt(cate, cate2=null, cate3=null, cate4=null)
    {
      if(cate)this.cateArr=[cate];
      if(cate2)this.cateArr=[cate, cate2];
      if(cate3)this.cateArr=[cate,cate2, cate3];
      if(cate4)this.cateArr=[cate,cate2, cate3, cate4];
      console.log(cate, cate2 , cate3, cate4);
    },
    /**
     * 前往个人中心
     */
    goToInfo() {
      this.$router.push("/user/info");
    },
    setOpen() {
      this.isOpen = !this.isOpen;
    },
    // 退出登录
    logout() {
      this.$axios
        .post(this.url + "/api/user/logout", {
          params: {},
        })
        .then((res) => {
          localStorage.removeItem("UserInfo");
          localStorage.removeItem("Authorization");
          localStorage.removeItem("IsLogin");
          if (this.$route.name != "homeindex") {
            this.$router.push("/");
          }
          this.$nextTick(() => {
            this.state = false;
            this.user = {};
          });
          this.reload();

          this.$message({
            message: "退出登录成功！",
            type: "success",
          });
        })
        .catch((err) => {});
    },
    /**
     * 监听滚动，改变导航栏的颜色
     */
    isBg() {
      this.scroll =
        document.documentElement.scrollTop || document.body.scrollTop;
      if (this.scroll > 20) {
        this.bg = true;
      } else {
        this.bg = false;
      }
    },
    /**
     * 获取课程分类数据
     */
    getCourseCate() {
      this.$axios
        .get(this.url + "/api/courses/cate", {
          params: {},
        })
        .then((res) => {
          if (res.data.code === 200) {
            this.courseCates = res.data.data;
            // 【添加三级导航】添加移动端导航点击事件
            this.addMenuEvent();
          }
        })
        .catch((err) => {});
    },
    /**
     * 获取插件分类数据
     */
    getPlugCate() {
      this.$axios
        .get(this.url + "/api/plug/cate", {
          params: {},
        })
        .then((res) => {
          if (res.data.code === 200) {
            this.plugCates = res.data.data;
            // 【添加三级导航】添加移动端导航点击事件
            this.addMenuEvent();
          }
        })
        .catch((err) => {});
    },
    /**
     * 获取插件分类数据
     */
    getGoodsCate() {
      this.$axios
        .get(this.url + "/api/goods/cate", {
          params: {},
        })
        .then((res) => {
          if (res.data.code === 200) {
            this.goodsCates = res.data.data;
            // 【添加三级导航】添加移动端导航点击事件
            this.addMenuEvent();
          }
        })
        .catch((err) => {});
    },
    /**
     * 通过路由路径判断导航是否在内页
     */
    isNy() {
      if (this.$route.path != "/") {
        this.ny = true;
      } else {
        this.ny = false;
      }
    },

    /**
     * 关键词搜索
     */
    search() {
      if (!this.keywords) {
        return;
      }

      if (this.keywords != this.$route.query.keywords) {
        var k = this.keywords;
        this.keywords = '';
        this.$router.push({
          name: "search",
          query: { keywords: k },
        });
      }
    },
    /**
     *
     */
    addClass(index) {
      if (index === this.current) {
        this.current = 0;
      } else {
        this.current = index;
      }
    },
    /**
     * 回车搜索
     */
    seachEnter(e) {
      var keyCode = window.event ? e.keyCode : e.which;
      if (keyCode == 13) {
        this.search(); //搜索按钮的回调
      }
    },

    /**
     * 【添加三级导航】添加移动端菜单点击事件
     */
    addMenuEvent() {
      this.$nextTick(() => {
        document
          .querySelectorAll(".mob-nav .mob-sub-menu .sub-item .arrow")
          .forEach((item) => {
            item.addEventListener("click", function () {
              this.classList.toggle("active");
            });
          });
      });
    },
  },
  mounted() {
    window.addEventListener("scroll", this.isBg);
    this.isBg();
  },
  beforeMount() {
    //  获取本地存储中user数据
    let user = JSON.parse(localStorage.getItem("UserInfo"));
    if (user) {
      this.state = true;
      this.user = user;
    }
    // 获取导航 课程 分类数据
    this.getCourseCate();
    // 获取导航 插件 分类数据
    this.getPlugCate();
    // 获取导航 周边 分类数据
    this.getGoodsCate();
    // 判断是否添加内页导航样式
    this.isNy();
  },
  components: {},
};
</script>

<style lang="scss">
/* ==================== 头部 start ==================== */
.xg-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  font-size: 0;
  z-index: 99;
  transition: all 0.4s;

  ul {
    margin-top: 0;
    margin-bottom: 0;
    margin-left: 0;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0;
    padding-top: 0;
    padding-bottom: 0;
  }

  // 内页 导航样式
  &.ny {
    // position: static;
    background: #242b33;

    .search {
      border-color: #646e7a;

      input {
        &::-webkit-input-placeholder {
          /* WebKit browsers */
          color: #646e7a;
        }

        &:-moz-placeholder {
          /* Mozilla Firefox 4 to 18 */
          color: #646e7a;
        }

        &::-moz-placeholder {
          /* Mozilla Firefox 19+ */
          color: #646e7a;
        }

        &:-ms-input-placeholder {
          /* Internet Explorer 10+ */
          color: #646e7a;
        }
      }
    }
  }

  // 导航下拉样式
  &.bg {
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2);

    .logo {
      img {
        margin-top: -40px;
      }
    }

    // 导航
    .pc-menu {
      & > li {
        color: #818e9d;

        &:hover {
          a:after {
            border-top-color: #0d6efd !important;
          }
        }

        &.sub {
          a:after {
            border-top-color: #cbd3de;
          }
        }
      }

      .pc-sub-menu {
        top: 90%;
      }

      .pc-nav-box {
        box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2);
      }
    }

    // 搜索
    .search {
      border-color: #d8d8d8;

      input {
        color: #333;

        &::-webkit-input-placeholder {
          /* WebKit browsers */
          color: #d8d8d8;
        }

        &:-moz-placeholder {
          /* Mozilla Firefox 4 to 18 */
          color: #d8d8d8;
        }

        &::-moz-placeholder {
          /* Mozilla Firefox 19+ */
          color: #d8d8d8;
        }

        &:-ms-input-placeholder {
          /* Internet Explorer 10+ */
          color: #d8d8d8;
        }
      }

      .icon {
        img {
          margin-top: -20px;
        }
      }
    }

    // 登录注册
    .login {
      // 登录注册按钮
      .login-btn,
      .register-btn {
        color: #818e9d;

        &:hover {
          color: #0d6efd;
        }
      }

      .login-btn::after {
        background: #d8d8d8;
      }
    }
  }

  //
  .logo {
    width: 40px;
    height: 40px;
    overflow: hidden;
    display: block;
    margin-right: 25px;

    img {
      margin-top: 0;
    }
  }

  .pc-nav {
    padding-left: 30px;
    padding-right: 30px;
  }

  // 搜索
  .search {
    flex-shrink: 0;
    margin-right: 30px;
    border: 1px solid #646e7a;
    border-radius: 100px;
    width: 220px;
    height: 40px;
    padding: 10px 14px;

    input {
      height: 100%;
      min-width: 0;
      flex-grow: 1;
      color: #fff;
      font-size: 12px;

      &::-webkit-input-placeholder {
        /* WebKit browsers */
        color: #46515d;
      }

      &:-moz-placeholder {
        /* Mozilla Firefox 4 to 18 */
        color: #46515d;
      }

      &::-moz-placeholder {
        /* Mozilla Firefox 19+ */
        color: #46515d;
      }

      &:-ms-input-placeholder {
        /* Internet Explorer 10+ */
        color: #46515d;
      }
    }

    .icon {
      width: 20px;
      height: 20px;
      overflow: hidden;
      margin-left: 10px;
      flex-shrink: 0;
      cursor: pointer;

      img {
        margin-top: 0;
      }
    }
  }

  // 登录注册
  .login {
    flex-shrink: 0;

    .login-btn,
    .register-btn {
      display: block;
      font-size: 16px;
      color: #cbd3de;
      line-height: 1.2;

      &:hover {
        color: #0d6efd;
      }
    }

    .login-btn {
      margin-right: 20px;
      padding-right: 20px;
      position: relative;

      &::after {
        content: "";
        display: block;
        width: 1px;
        height: 100%;
        position: absolute;
        right: 0;
        top: 0;
        background: #fff;
      }
    }

    // 已经登录的状态
    .top {
      position: relative;
      flex-shrink: 0;

      &:hover {
        .info {
          opacity: 1;
          visibility: visible;
          transform: translateY(0px);
        }
      }
    }

    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 100%;
      overflow: hidden;
      cursor: pointer;
      outline: none;
      overflow: hidden;
      background: #fff;
      display: block;
      border: 1px solid #ddd;

      img {
        width: 100%;
      }
    }

    .info {
      padding-top: 10px;
      transform: translateY(20px);
      transition: all 0.4s;
      text-align: left;
      opacity: 0;
      visibility: hidden;
      position: absolute;
      top: 100%;
      width: 195px;
      right: 0;
      z-index: 99;
    }

    .info > div {
      padding: 20px 25px;
      background: #fff;
      border-radius: 4px;
      border-radius: 4px;
      box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
    }

    .name {
      font-size: 14px;
      color: #0d6efd;
      line-height: 1.2;
      margin-bottom: 20px;
    }

    .list {
    }

    .list-item {
      font-size: 12px;
      color: #5f6974;
      line-height: 1.2;
      padding-bottom: 10px;
      padding-top: 10px;
      border-bottom: 1px solid #e6e6e6;
      display: block;

      &:hover {
        color: #0d6efd;
      }
    }

    .xg-btn {
      margin-top: 15px;
      background: #e6e6e6;
      height: 30px;
      line-height: 30px;
      text-align: center;
      font-size: 12px;
      color: #000;
      display: block;
      cursor: pointer;
      outline: none;
      overflow: hidden;
      position: relative;
      transition: all 0.4s;

      &:hover {
        color: #fff;

        &:after {
          transform: translate(-50%, -50%) scale(1);
          opacity: 1;
        }
      }

      &:after {
        content: "";
        display: block;
        width: 150px;
        height: 150px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%) scale(0);
        transition: all 0.4s;
        background: #0d6efd;
        opacity: 0;
        border-radius: 100%;
        box-shadow: 0 0 20px 20px #0d6efd;
      }

      span {
        position: relative;
        z-index: 9;
      }
    }
  }

  // pc导航
  .pc-menu {
    flex-grow: 1;
    display: flex;
    align-items: center;
    position: relative;

    & > li {
      color: #fff;
      position: relative;
      display: flex;
      align-items: center;

      &.active,
      &:hover {
        color: #0d6efd;

        &.sub {
          & > a:after {
            transform: rotate(180deg);
          }
        }

        .pc-sub-menu {
          opacity: 1;
          visibility: visible;

          .pc-nav-box {
            transform: perspective(1200px) rotateX(0deg);
            -webkit-transform: perspective(1200px) rotateX(0deg);
            -moz-transform: perspective(1200px) rotateX(0deg);
            -ms-transform: perspective(1200px) rotateX(0deg);
            -o-transform: perspective(1200px) rotateX(0deg);
          }
        }
      }

      &.sub {
        & > a {
          &:after {
            content: "";
            display: inline-block;
            vertical-align: middle;
            width: 0;
            height: 0;
            border-top: 7px solid;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            margin-left: 10px;
            transition: all 0.4s;
          }
        }
      }

      & > a {
        font-size: 16px;
        text-align: center;
        display: inline-block;
        padding: 30px 25px;
        z-index: 9;
        color: inherit;
      }
    }

    .pc-sub-menu {
      width: 150px;
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-20%);
      z-index: 30;
      opacity: 0;
      visibility: hidden;
      transition: all 0.4s;
      padding-top: 0px;
      color: #333;
    }

    .pc-nav-box {
      position: relative;
      border-radius: 4px;
      background: #fff;
      padding: 10px 0;
      -webkit-transition: all 300ms cubic-bezier(0.7, 0, 0.185, 1) 0s;
      transition: all 300ms cubic-bezier(0.7, 0, 0.185, 1) 0s;
      -webkit-transform-origin: 50% 0%;
      -ms-transform-origin: 50% 0%;
      transform-origin: 50% 0%;
      -webkit-transform: perspective(1200px) rotateX(-90deg);
      transform: perspective(1200px) rotateX(-90deg);
      -webkit-backface-visibility: hidden;
      -webkit-transform-style: preserve-3d;

      &:before {
        content: "";
        display: block;
        width: 10px;
        height: 10px;
        transform: rotate(45deg);
        background: #fff;
        position: absolute;
        top: -5px;
        left: 25px;
        box-shadow: 0 0 0 #000;
      }

      & > li {
        &:hover {
          & > a {
            color: #0d6efd;
          }
        }

        & > a {
          display: block;
          text-align: left;
          font-size: 14px;
          color: #343d47;
          padding: 10px 20px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

/* ==================== 头部 end ==================== */
/* ==================== 移动端头部 start ==================== */

@media screen and (max-width: 751px) {
  .xg-header {
    border-top: 0;
    padding: 0;
  }

  .mob-nav {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    z-index: 99999;
    display: flex;
    flex-flow: column;
    align-items: center;
    height: 60px;
    transition: all 0.4s;

    &.active {
      height: 100%;

      .mob-nav-head {
        .nav-btn {
          width: 26px;
          height: 26px;

          i {
            position: absolute;
            top: 10px;
            left: 0;

            &:first-child ~ i {
              margin: 0;
            }

            &:nth-child(1) {
              display: none;
            }

            &:nth-child(2) {
              transform: rotate(45deg);
            }

            &:nth-child(3) {
              transform: rotate(-45deg);
            }
          }
        }
      }

      .mob-nav-body {
        opacity: 1;
        margin-top: 0;
        visibility: visible;
      }
    }

    .mob-nav-head {
      background: #fff;
      border-bottom: 1px solid #ddd;
      width: 100%;
      height: 60px;
      flex-shrink: 0;
      display: flex;
      justify-content: space-between;
      padding-left: 20px;
      padding-right: 20px;
      align-items: center;
      position: relative;

      .nav-btn {
        position: relative;

        i {
          display: block;
          background: #333;
          width: 26px;
          height: 3px;
          transition: all 0.4s;

          &:first-child ~ i {
            margin-top: 5px;
          }
        }
      }

      .nav-logo {
        display: block;

        img {
          height: 40px;
        }
      }
    }

    .mob-nav-body {
      transition: all 0.2s;
      opacity: 0;
      visibility: hidden;
      text-align: left;
      width: 100%;
      background: #151515;
      flex-grow: 1;
      padding: 10px 20px;
      overflow: auto;
    }

    // 用户信息
    .user-nav {
      border-bottom: 1px solid #525252;
      padding-bottom: 15px;

      // 头像
      .avatar {
        width: 40px;
        height: 40px;
        border: 1px solid #eee;
        border-radius: 100%;
        border-radius: 100%;
        overflow: hidden;
        flex-shrink: 0;
        margin-right: 20px;

        img {
          width: 100%;
        }
      }

      // 用户名
      .name {
        min-width: 0;
        flex-grow: 1;
        color: #0d6efd;
        font-size: 14px;
        padding-right: 20px;
      }

      .text {
        font-size: 14px;
        flex-shrink: 0;
        color: #fff;

        &:hover {
          color: #0d6efd;
        }
      }
    }

    // 登录注册
    .tool {
      .xg-btn {
        display: block;
        width: 100%;
        padding: 10px;
        font-size: 16px;
        color: #fff;
        border-top: 1px solid rgba(255, 255, 255, 0.1);

        &:hover {
          color: #0d6efd;
        }
      }
    }

    // 导航菜单
    .mob-nav-menu {
      & > .first-menu {
        display: block;
        position: relative;
        color: #fff;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        font-size: 16px;

        &:last-child {
          border-bottom: 0;
        }

        .text {
          padding: 10px;
        }

        //
        &.sub {
          & > .text {
            position: relative;
            padding-right: 30px;

            &:after {
              content: "";
              display: block;
              width: 10px;
              height: 10px;
              border-top: 1px solid #fff;
              border-right: 1px solid #fff;
              position: absolute;
              right: 20px;
              top: 50%;
              transform: rotate(45deg) translateY(-50%);
            }
          }
        }

        //
        &.active {
          .mob-sub-menu {
            display: block;
          }
        }
      }
    }

    .mob-sub-menu {
      display: none;
      background: rgba(255, 255, 255, 0.05);

      & > .first-menu {
        display: block;
        color: #fff;
        font-size: 14px;
        padding-left: 10px;
      }
    }
  }
}

/* ==================== 移动端头部 end ==================== */

/* ==================== 【添加三级导航】新增导航三级分类 start ==================== */
// PC

@media screen and (min-width: 750px) {
  .xg-header {
    .pc-menu {
      .pc-nav-box {
        & > li {
          position: relative;

          &:hover {
            .arrow {
              border-right-width: 0;
              border-left-width: 6px;
              border-left-color: #0d6efd;
              border-bottom: 4px solid transparent;
              border-top: 4px solid transparent;
            }
          }

          .arrow {
            display: block;
            position: absolute;
            top: 50%;
            right: 10px;
            -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
            border-top: 6px solid #b0b4b8;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
          }

          &:hover {
            .three-menu {
              opacity: 1;
              visibility: visible;
              .item1{
              }&:hover {
                .three-menu2 {
                  opacity: 1;
                  visibility: visible;
                }
              }
            }
            
          }
        }
      }
    }

    .three-menu ,.three-menu2,.three-menu3{
      position: absolute;
      left: 100%;
      top: -10px;
      color: #343d47;
      white-space: nowrap;
      transition: all 0.4s;
      opacity: 0;
      visibility: hidden;
      padding-left: 10px;

      .three-menu-itembox {
        background-color: #fff;
        border-radius: 5px;
        padding: 10px 0;
      }

      .menu-item {
        display: block;
        text-align: left;
        font-size: 14px;
        color: #343d47;
        padding: 10px 20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        transition: all 0.4s;

        &:hover {
          color: #0d6efd;
        }
      }
    }
  }
}

// 移动端
@media screen and (max-width: 751px) {
  .mob-nav {
    .mob-sub-menu {
      padding-left: 10px;

      .first-menu {
        font-size: 14px;
        color: #fff;
      }

      .sub-item {
        position: relative;

        .arrow {
          position: absolute;
          width: 30px;
          height: 40px;
          right: 0;
          top: 0;
          z-index: 10;

          &.active {
            & + .three-menu {
              display: block;

              &:hover {
                display: block;
              }
            }

            &:after {
              display: none;
            }
          }

          &:before,
          &:after {
            content: "";
            position: absolute;
            background-color: #fff;
            display: block;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }

          &:before {
            width: 14px;
            height: 1px;
          }

          &:after {
            width: 1px;
            height: 14px;
          }
        }
      }

      .three-menu {
        border-radius: 5px;
        padding: 10px 0;
        background-color: rgba(255, 255, 255, 0.1);
        display: none;
      }

      .menu-item {
        display: block;
        text-align: left;
        font-size: 12px;
        color: #ffffff;
        padding: 8px 20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-transition: all 0.4s;
        transition: all 0.4s;

        &:hover {
          color: #0d6efd;
        }
      }
    }
  }
}

/* ==================== 【添加三级导航】新增导航三级分类 end ==================== */
</style>

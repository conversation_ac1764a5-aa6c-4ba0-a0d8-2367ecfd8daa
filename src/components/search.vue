<template>
  <div class="xg-ny xg-ny-search">
    <div class="xg-box">
      <div class="xg-body">
        <div class="upload-box">
          <el-tabs v-model="activeName2" @tab-click="handleClick2">
            <el-tab-pane label="课程" name="course"> </el-tab-pane>
            <el-tab-pane label="插件" name="plug"></el-tab-pane>
            <el-tab-pane label="素材" name="goods"></el-tab-pane>
            <el-tab-pane label="用户" name="users"></el-tab-pane>
            <el-tab-pane label="新闻" name="news"></el-tab-pane>
          </el-tabs>
        </div>
        <div class="head">
          相关 <span>{{ keywords }}</span> 找到 <span>{{ total }}</span> 条 
          共<span>{{totalAll}}</span>条
        </div>

        <!-- 搜索结果列表 -->
        <div class="xg-result">
          <div
            class="item"
            
            v-for="(item, index) in items"
            :key="index"
          >
          <!-- @click="jump(item.id, item.type)" -->
            <router-link
              tag="a"
              target="_blank"
              :to="{ name: item.type=='course'?'coursepage':
                (item.type=='plug'?'plug':
                (item.type=='news'?'newsdetail':
                (item.type=='goods'?'peripheryDetails':
                (item.type=='users'?'userfans':'')))), 
                params: { id: item.id } }"
            >
           
           
              <div class="image" v-if="item.type=='users'" style="width:150px;height:150px;overflow: hidden;border-radius:150px">
                <img :src="item.images" alt="" title="" style=""/>
              </div>
              
              <div class="image" v-else>
                <img :src="item.images" alt="" title="" />
              </div>
              <div class="info">
                <div class="xg-line-1 title" v-html="item.name">
                  <!-- {{ item.name}} -->
                </div>
              </div>
            </router-link>
            
                <div class="xg-line-3 desc" v-html="item.intro">
                  <!-- {{ item.intro }} -->
                </div>
          </div>
        </div>
      </div>
      <!-- 分页 -->
      <el-pagination
        class="xg-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="offset"
        :page-size="limit"
        layout="prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: "search",
  components: {},
  data() {
    return {
      totalAll:0,
      activeName2: "course",
      offset: 0,
      tag: null,
      limit: 10,
      total: 0,
      items: [],

      // 关键词
      keywords: "",
    };
  },
  beforeMount() {
    if (this.$route.query.type) {
      this.activeName2 = this.$route.query.type;
    }
    if (this.$route.query.tag) {
      this.tag = this.$route.query.tag;
    }
    this.getData();
  },
  methods: {
    handleClick2() {
      console.log(this.activeName2);
      this.getData(10, 0);
    },
    handleSizeChange(val) {
      this.limit = val;
    },
    handleCurrentChange(val) {
      let page = val - 1;
      this.getData(this.limit, page);
    },

    getData(limit = 10, offset = 0) {
      let keywords = this.$route.query.keywords;
      let tag = this.tag;
      this.$nextTick(function () {
        this.keywords = this.$route.query.keywords;
      });
      if (keywords || tag) {
        this.$axios
          .get(this.url + "/api/search", {
            params: {
              limit: limit,
              offset: offset,
              keywords: keywords,
              tag: tag,
              type: this.activeName2,
            },
          })
          .then((res) => {
            if (res.data.code === 200) {
              this.items = res.data.data.lists;
              this.setKeywords();
              this.total = res.data.data.count;
              this.totalAll = res.data.data.totalAll;
            }
            this.loading = false;
          })
          .catch((err) => {
            this.loading = false;
          });
      } else {
        this.$nextTick(function () {
          this.items = [];
        });
      }
    },

    jump(id, type) {
      switch (type) {
        case "course":
          // window.open( '', 'black');
          this.$router.push({
            name: "coursepage",
            params: { id: id },
          });
          break;
        case "plug":
          this.$router.push({
            name: "plug",
            params: { id: id },
          });
          break;
        case "news":
          this.$router.push({
            name: "newsdetail",
            params: { id: id },
          });
          break;
        case "goods":
          this.$router.push({
            name: "peripheryDetails",
            params: { id: id },
          });
          break;
      }
    },
    /**
     * 设置关键词高亮
     */
    setKeywords() {
      this.items.map((v) => {
        if (this.keywords) {
          v.name = v.name.replace(
            new RegExp(this.keywords, "g"),
            '<span style="color:#0d6efd">' + this.keywords + "</span>"
          );
          v.intro = v.intro.replace(
            new RegExp(this.keywords, "g"),
            '<span style="color:#0d6efd">' + this.keywords + "</span>"
          );
        }

        return;
      });
    },
  },
  watch: {
    $route: {
      handler(route) {
        this.getData();
      },
    },
  },
};
</script>

<style scoped lang="scss">
/* ==================== 内页 - 搜索结果 start ==================== */
.xg-ny-search {
  .xg-body {
    background-color: #fff;
    border: solid 1px #dbdddf;
    padding: 40px;
    padding-bottom: 80px;
  }
  >>> .el-tabs__item {
    font-size: 20px;
    font-weight: 600;
  }
  .head {
    width: 100%;
    font-size: 18px;
    color: #a3acb6;
    padding-bottom: 25px;
    margin-bottom: 80px;
    font-weight: bold;
    text-align: left;
    border-bottom: 1px solid #dae2e9;
    span {
      color: #0d6efd;
    }
  }
  .xg-result {
    padding-left: 75px;
    padding-right: 75px;
  }
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 50px;
    align-items: flex-start;
    cursor: pointer;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .image {
    flex-shrink: 0;
    width: 210px;
    border-radius: 6px;
    overflow: hidden;
    margin-right: 20px;
  }
  .info {
    min-width: 0;
    flex-grow: 1;
  }
  .title {
    font-size: 16px;
    color: #4f5d6a;
    margin-bottom: 15px;
    font-weight: bold;
    span {
      color: #0d6efd;
    }
  }
  .desc {
    font-size: 14px;
    line-height: 1.8;
    color: #a3acb6;
    text-align: justify;
    padding: 30px 10px;
    box-sizing: border-box;
    span {
      color: #0d6efd;
    }
  }
}

@media screen and (max-width: 751px) {
  .xg-ny-search .xg-body {
    padding: 30px 20px;
    margin-bottom: 20px;
  }
  .xg-ny-search .head {
    font-size: 16px;
    padding-bottom: 15px;
    margin-bottom: 20px;
  }
  .xg-ny-search .xg-result {
    padding-left: 0;
    padding-right: 0;
  }
  .xg-ny-search .item {
    flex-wrap: wrap;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ddd;
    &:last-child {
      border-bottom: 0;
      padding-bottom: 0;
      margin-bottom: 0;
    }
  }
  .xg-ny-search .item .image {
    width: 100%;
    display: none;
  }
  .xg-ny-search .item .info {
    width: 100%;
  }
  .xg-ny-search .item .title {
    font-size: 14px;
    margin-bottom: 10px;
  }
  .xg-ny-search .item .desc {
    font-size: 12px;
    line-height: 1.8;
    height: 3.6em;
    -webkit-line-clamp: 2;
  }
}
/* ==================== 内页 - 搜索结果 end ==================== */
</style>

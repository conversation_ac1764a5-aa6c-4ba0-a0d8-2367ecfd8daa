<template>
  <div class="hello">
    <!--header-->
    <plugdetail></plugdetail>
  </div>
</template>

<script>
  import plugdetail from "./plugdetail";

  export default {
    name: 'plug',
    components:{
      plugdetail,
    },
    data () {
      return {
      }
    },
    created(){
    },
    computed: {
    },
    mounted() {
    },
    methods:{
    },
    watch: {
    }
  }
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
h1, h2 {
  font-weight: normal;
}
ul {
  list-style-type: none;
  padding: 0;
}
li {
  display: inline-block;
  margin: 0 10px;
}
a {
  color: #42b983;
}
.hello{
  overflow: hidden;
}
</style>

/* ==================== 内页 - 插件 start ==================== */
.xg-ny-plug {
    .xg-list-card .el-tabs__header {
      margin-bottom: 0;
    }
    .itembox {
      padding: 50px;
    }
    .item {
      background-color: #fff;
      box-shadow: 0px 0px 50px 0px rgba(0, 0, 0, 0.3);
      border-radius: 4px;
      padding: 40px 15px 20px;
      transition: all 0.4s;
      cursor: pointer;
      outline: none;
    }
    .image {
      display: block;
      margin: 0 auto;
      width: 90px;
      height: 90px;
      border-radius: 100%;
      overflow: hidden;
      margin-bottom: 20px;
      background: #fff;
      img {
        width: 100%;
      }
    }
    .name {
      font-size: 16px;
      line-height: 1.2;
      color: #000;
      &:after {
        content: "";
        display: block;
        width: 18px;
        height: 2px;
        background: #7f8992;
        margin: 20px auto;
      }
    }
    .desc {
      font-size: 13px;
      color: #818e9d;
      text-align: justify;
      line-height: 1.8;
      margin-bottom: 15px;
      height: 7.2em;
    }
    .info {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 20px;
      margin-top: 25px;
    }
    .author,
    .price {
      border-radius: 2px;
      color: #fff;
      font-size: 12px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      padding-left: 10px;
      padding-right: 10px;
    }
    .author {
      background: #a3acb6;
      margin-right: 10px;
    }
    .price {
      background: #00be06;
      &.style1 {
        background: #0d6efd;
      }
    }
    .xg-more {
      background: #579bff;
      border-radius: 4px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      display: none;
      font-size: 14px;
      color: #fff;
    }
  }
  @media screen and (min-width: 751px) {
    .xg-ny-plug {
      .item{
        .name,
        .name:after,
        .desc,
        .info {
          transition: all 0.4s;
        }
        &:hover {
          padding-bottom: 20px;
          background: #0d6efd;
          box-shadow:none;
          .name,
          .desc,
          .info {
            color: #fff;
          }
          .name:after{background:#fff;}
          .info {
            display: none;
          }
          .xg-more {
            display: block;
          }
        }
      }
    }
  }
  @media screen and (max-width: 751px) {
    .xg-ny-plug {
      .xg-list-card .el-tabs__header{margin-bottom:10px;}
      .xg-box{padding-left:10px;padding-right:10px;}
      .itembox{padding:10px;}
      .item{
        box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.3);
      }
      .item{padding:20px 15px 10px;}
      .info{margin-bottom: 15px;margin-top: 20px;}
      .image{width:60px;height:60px;margin-bottom: 15px;}
      .name{font-size:14px;
        &:after{margin:10px auto;}
      }
      .desc{font-size:12px;-webkit-line-clamp: 3;height:4.8em;line-height:1.6;margin-bottom:15px;}
      .xg-more{font-size:12px;height:30px;line-height:30px;}
    }
  }
  /* ==================== 内页 - 插件 end ==================== */
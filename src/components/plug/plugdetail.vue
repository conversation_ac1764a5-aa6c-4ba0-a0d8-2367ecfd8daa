<template>
  <div class="xg-ny plug-detail">
    <div class="xg-box">
      <!-- 内页 标题 -->
      <div class="xg-ny-title">插件</div>
      <!-- 提示 -->
      <div class="tip">
        插件所有者归作者所有，涉及版权或补充事宜请联系客服扣扣571940111(备注来意)
      </div>
      <!--  -->
      <div class="body" v-if="info">
        <!-- 插件内容 -->
        <div class="plug-swiper-box">
          <swiper ref="plugSwiper" :options="swiperOption2">
            <swiper-slide>
              <div class="plug-main">
                <!-- 封面图 -->
                <div class="cover"><img :src="info.images" alt="" /></div>
                <!-- 插件描述 -->
                <div class="info">
                  <!-- 插件名称 -->
                  <div class="title">{{ info.name }}</div>
                  <div class="">
                    <div class="tag-box" v-if="info.tag_list_info">
                      <el-tag
                        @click="go(item)"
                        class="tag-item"
                        v-for="(item, i) in info.tag_list_info"
                        :key="i"
                        >{{ item.tag }}</el-tag
                      >
                    </div>
                  </div>
                  <!-- 插件信息 -->
                  <!-- 付费 -->
                  <div
                    class="attribute"
                    v-if="info.price > 0 && info.purchase <= 0"
                  >
                    <div class="item item1">
                      <div class="text">售 价</div>
                      <span v-if="info.has_discount && info.discount_price">
                        <strong>{{ info.discount_price }} 光子</strong>
                        <span style="text-decoration: line-through; color: #999; margin-left: 5px; font-size: 12px;">
                          {{ info.price }} 光子
                        </span>
                      </span>
                      <span v-else>
                        <strong>{{ info.price }} 光子</strong>
                      </span>
                    </div>
                    <div class="item item2">
                      <div class="text">赠送光子</div>
                      <span>{{ info.give_integral }}</span>
                    </div>
                    <div class="item item3">
                      <div class="text">销量</div>
                      <span>{{ info.sales_num }}</span>
                    </div>
                    <div class="item item4">
                      <div class="text">类别</div>
                      <span>{{ info.cate ? info.cate.name : "" }}</span>
                    </div>
                  </div>
                  <!-- 免费 -->
                  <div class="attribute" v-else>
                    <div class="item item4">
                      <div class="text">类别</div>
                      <span>{{ info.cate ? info.cate.name : "" }}</span>
                    </div>
                  </div>
                  <div class="attribute">
                    <div class="item item4" v-if="info.suggest_user"   @click="gofans(info.suggest_user.id)">
                      <div class="text">推荐人</div>
                      <div class="avatar">
                        <div class="img-box">
                          <img :src="info.suggest_user.avatar" alt="" />
                        </div>
                        <i
                          class="el-icon-plus fansicon"
                          v-if="info.nofans"
                          @click.stop="userGuanzhu()"
                        ></i>
                      </div>
                      <span>{{ info.suggest_user.name }}</span>
                    </div>
                  </div>

                  <!-- 立即购买 -->
                  <div
                    class="buy"
                    type="button"
                    @click="getUrl()"
                    v-if="info.price > 0 && info.purchase <= 0"
                  >
                    立即购买
                  </div>

                  <!-- 插件简介 -->
                  <div class="detail" v-if="!textShow">
                    <p v-html="info.intro"></p>
                    <!--              {{info.intro|textSlice}}-->
                    <span class="xg-more" @click="textShow = true">更多</span>
                  </div>
                  <div class="detail" v-else>
                    <p v-html="info.intro"></p>
                    <!--              {{info.intro}}-->
                  </div>
                </div>
                <!-- 使用介绍 -->
                <div class="introduce">
                  <div class="title">使用介绍</div>
                  <!-- 封面图 -->
                  <div class="image" @click="introduceOpen = true">
                    <!--                    <img src="https://www.helloimg.com/images/2022/04/09/Rs6U0t.jpg" alt="" />-->
                    <img
                      :src="info.intros[0].images"
                      alt=""
                      v-if="info.intros && info.intros.length > 0"
                    />
                    <img
                      src="https://www.helloimg.com/images/2022/04/09/Rs6U0t.jpg"
                      alt=""
                      v-else
                    />
                    <i class="icon el-icon-search"></i>
                  </div>
                  <!-- 三个按钮 -->
                  <div class="xg-btn-group">
                    <div class="xg-btn" @click="dialogTableVisible = true">
                      下载
                    </div>
                    <div class="xg-btn" @click="dialogmanual = true">
                      使用手册
                    </div>
                    <div class="xg-btn" @click="dialogcase = true">
                      案例下载
                    </div>
                    <a
                      class="xg-btn"
                      :href="info.web_link"
                      target="_blank"
                      v-if="info.web_link"
                      >功能介绍</a
                    >
                  </div>
                </div>
              </div>
            </swiper-slide>
          </swiper>
          <!-- 上一个插件 -->
          <div
            class="plug-prev"
            v-if="info.prev_id > 0"
            @click="jump(info.prev_id)"
          ></div>
          <!-- 下一个插件 -->
          <div
            class="plug-next"
            v-if="info.next_id > 0"
            @click="jump(info.next_id)"
          ></div>
        </div>
      </div>
    </div>

    <!-- 下载 弹框 -->
    <div
      class="plug-open download-open"
      @click="dialogTableVisible = false"
      :class="{ active: dialogTableVisible }"
    >
      <div class="plug-box" @click.stop>
        <div
          class="close el-icon-close"
          @click="dialogTableVisible = false"
        ></div>
        <div class="open-title">下载</div>
        <el-table :data="gridData" class="list" height="280">
          <el-table-column prop="version_code" label="版本" width="140">
          </el-table-column>
          <el-table-column prop="platform" label="平台" width="120">
          </el-table-column>
          <el-table-column prop="time" label="时间" width="90">
          </el-table-column>
          <el-table-column prop="url" label="下载" width="50">
            <template slot-scope="scope">
              <a
                class="el-icon-download"
                @click="downloadCount('download_package')"
                :href="scope.row.download_link"
                target="_blank"
                v-if="info.download && scope.row.download_link"
              ></a>
              <a
                @click.prevent="tips"
                target="_blank"
                v-else-if="info.download"
                class="el-icon-download"
              ></a>
              <a
                @click.prevent="needLogin"
                target="_blank"
                v-else
                class="el-icon-download"
              ></a>
            </template>
          </el-table-column>
        </el-table>
        <!-- 累计数量 -->
        <div class="download-count" v-if="info.download_package > 0">
          累计下载数：<strong>{{ info.download_package || 0 }}</strong>
        </div>
      </div>
    </div>

    <!-- 使用手册 弹框 -->
    <div
      class="plug-open manual-open"
      @click="dialogmanual = false"
      :class="{ active: dialogmanual }"
    >
      <div class="plug-box" @click.stop>
        <div class="close el-icon-close" @click="dialogmanual = false"></div>
        <div class="open-title">使用手册</div>
        <el-table :data="manualData" class="list" height="280">
          <el-table-column prop="version_code" label="版本"> </el-table-column>
          <el-table-column prop="time" label="时间"> </el-table-column>
          <el-table-column prop="url" label="下载" width="50">
            <template slot-scope="scope">
              <a
                :href="scope.row.download_link"
                @click="downloadCount('download_manual')"
                target="_blank"
                v-if="info.download && scope.row.download_link"
                class="el-icon-download"
              ></a>
              <a
                @click.prevent="tips"
                target="_blank"
                v-else-if="info.download"
                class="el-icon-download"
              ></a>
              <a
                @click.prevent="needLogin"
                target="_blank"
                v-else
                class="el-icon-download"
              ></a>
            </template>
          </el-table-column>
        </el-table>
        <!-- 累计数量 -->
        <div class="download-count" v-if="info.download_manual > 0">
          累计下载数：<strong>{{ info.download_manual || 0 }}</strong>
        </div>
      </div>
    </div>

    <!-- 案例下载 弹框 -->
    <div
      class="plug-open cases-open"
      @click="dialogcase = false"
      :class="{ active: dialogcase }"
    >
      <div class="plug-box" @click.stop>
        <div class="close el-icon-close" @click="dialogcase = false"></div>
        <div class="open-title">案例</div>
        <el-table :data="caseData" class="list" height="280">
          <el-table-column prop="version_code" label="名称"> </el-table-column>
          <el-table-column prop="url" label="下载" width="50">
            <template slot-scope="scope"
              >>

              <a
                :href="scope.row.download_link"
                @click="downloadCount('download_case')"
                target="_blank"
                v-if="info.download && scope.row.download_link"
                class="el-icon-download"
              ></a>
              <a
                @click.prevent="tips"
                target="_blank"
                v-else-if="info.download"
                class="el-icon-download"
              ></a>
              <a
                @click.prevent="needLogin"
                target="_blank"
                v-else
                class="el-icon-download"
              ></a>
            </template>
          </el-table-column>
        </el-table>
        <!-- 累计数量 -->
        <div class="download-count" v-if="info.download_case > 0">
          累计下载数：<strong>{{ info.download_case || 0 }}</strong>
        </div>
      </div>
    </div>

    <!-- 使用介绍 弹框 -->
    <div
      class="introduce-open"
      @click="introduceOpen = false"
      v-show="introduceOpen"
    >
      <div class="introduce-box" @click.stop>
        <!-- 关闭按钮 -->
        <div class="close el-icon-close" @click="introduceOpen = false"></div>
        <swiper :options="swiperOption" class="introduce-swiper">
          <swiper-slide v-for="item in info.intros" :key="item.index">
            <img :src="item.images" ref="imgSize" @onload="imgload" alt="" />
          </swiper-slide>
        </swiper>
        <!-- 前进后退 -->
        <div class="swiper-button-prev" slot="button-prev"></div>
        <div class="swiper-button-next" slot="button-next"></div>
        <!-- 分页器 -->
        <div class="swiper-pagination" slot="pagination"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { WOW } from "wowjs";
export default {
  name: "plugdetail",
  filters: {
    textSlice: function (val) {
      if (!val) return "";
      let str = val.toString();
      return str.substring(0, 150);
    },
  },
  data() {
    return {
      // info
      info: {},

      //下载
      gridData: [],
      //使用手册
      manualData: [],
      //案例
      caseData: [],

      dialogTableVisible: false,
      dialogmanual: false,
      dialogcase: false,

      // 使用介绍 swiper 配置
      swiperOption: {
        spaceBetween: 20, // swiper-solid 间距
        autoHeight: true, //高度随内容变化
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
        // allowTouchMove: false,// 禁止滑动
        preventClicks: false, //默认true
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
        },
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
      },

      // 使用介绍 swiper 配置
      swiperOption2: {
        spaceBetween: 20, // swiper-solid 间距
        autoHeight: true, //高度随内容变化
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
        // allowTouchMove: false,// 禁止滑动
        grabCursor: true,
        preventClicks: false, //默认true
        watchSlidesProgress: true,
        on: {
          touchStart: (swiepr) => {
            // 记录拖动起始位置
            this.moveData.start =
              swiepr.clientX || swiepr.changedTouches[0].clientX;
          },
          touchEnd: (swiepr) => {
            // 记录拖动结束位置
            this.moveData.end =
              swiepr.clientX || swiepr.changedTouches[0].clientX;
            // 最后计算拖动的距离
            let distance = this.moveData.start - this.moveData.end;
            // 拖动距离大于150则加载下一条数据
            if (distance > 150) {
              this.jump(this.info.next_id);
            }
            // 拖动距离小于150则加载上一条数据
            else if (distance < -150) {
              this.jump(this.info.prev_id);
            }
          },
        },
      },
      // 使用介绍 弹框开启状态
      introduceOpen: false,
      // 文字查看更多是否显示
      textShow: false,
      // 记录swiper拖动距离
      moveData: {
        // 起始位置
        start: 0,
        // 结束位置
        end: 0,
      },
    };
  },
  mounted() {
    var options = {
      //默认为true
      live: false,
    };
    new WOW(options).init();

    this.getInfo();
    this.$nextTick(() => {
      this.imgload();
    });
  },
  methods: {
    gofans(uid) {
      this.$router.push("/userfans/" + uid);
    },
    userGuanzhu(type = 1) {
      this.$axios
        .get(
          this.url + "/api/userGuanzhu/" + this.info.user_id + "?type=" + type
        )
        .then((res) => {
          console.log(res, "res--------");
          if (res.data.code !== 200) {
            this.$message.error(res.data.msg);
          } else {
            this.info.nofans = false;
            this.$message.success(res.data.msg);
          }
        })
        .catch((err) => {});
    },
    go(item) {
      this.$router.push("/search?tag=" + item.id + "&type=plug");
    },
    //图片高度
    imgload() {
      let imgSize = this.$refs["imgSize"]
        ? this.$refs["imgSize"].offsetHeight
        : 0;
      // console.log(imgSize);
    },

    needLogin() {
      localStorage.removeItem("UserInfo");
      localStorage.removeItem("Authorization");
      this.$router.push({ name: "login" });
    },

    tips() {
      this.$message.warning({
        showClose: true,
        message: "暂无权限",
      });
    },
    downloadCount(type) {
      var id = this.$route.params.id;
      if (id) {
        this.$axios
          .post(this.url + "/api/plug/" + id + "/count", {
            field: type,
          })
          .then((res) => {})
          .catch((err) => {});
      }
    },

    getInfo() {
      var id = this.$route.params.id;
      if (id) {
        this.$axios
          .get(this.url + "/api/plug/" + id, {
            params: {},
          })
          .then((res) => {
            if (res.data.code === 200) {
              this.info = res.data.data;
              this.gridData = [];
              this.manualData = [];
              this.caseData = [];
              res.data.data.detail.forEach((item, i) => {
                if (item.type === "package") {
                  this.gridData.push(item);
                } else if (item.type === "manual") {
                  this.manualData.push(item);
                } else {
                  this.caseData.push(item);
                }
              });
            } else {
              this.$message.error({
                showClose: true,
                message: res.data.msg,
              });
              this.$router.go(-1);
            }
          })
          .catch((err) => {});
      }
    },

    //  跳转
    jump(id) {
      if (this.$route.params.id != id) {
        this.$router.push({ name: "plug", params: { id: id } });
      } else {
        this.$message({
          message: "已经是最后一条啦！",
          offset: 90,
        });
      }
    },

    //跳转购买链接
    getUrl() {
      // product_type：1-课程，2-插件，3素材
      this.$router.push({
        path: "/payment",
        query: { product_id: this.info.id, product_type: 2 },
      });
      // this.$axios.post(this.url + '/api/create-order', {
      //   type:2,
      //   id:this.info.id
      // }).then(res => {
      //   if (res.data.code === 200) {
      //     this.$router.push({path:"/payment", query:{order_no:res.data.data.order_no}})
      //   } else if (res.data.code === 4001) {
      //     localStorage.removeItem('UserInfo');
      //     localStorage.removeItem('Authorization');
      //     this.$router.push({name:"login"})
      //   } else {
      //     this.$message.warning({
      //       showClose: true,
      //       message: res.data.msg
      //     });
      //   }
      // }).catch(err => {});
    },
  },
  watch: {
    $route: {
      handler(route) {
        this.getInfo();
      },
    },
  },
};
</script>

<style scoped lang="scss">
//插件

.tag-box {
  text-align: left;
  .tag-item {
    margin: 5px 20px 5px 0;
    cursor: pointer;
  }
}
.plug-in {
  width: 200px;
  height: 200px;
  margin: 0 auto;
  border-radius: 200px;
  overflow: hidden;
  /*background: #E6A23C;*/
  img {
    width: 100%;
    height: 100%;
  }
}
.rightcont {
  text-align: left;
  height: 200px;
  p {
    text-align: center;
    line-height: 200px;
  }
}
.grid-content {
  width: 100%;
  .rightcont-o {
    width: 100%;
    vertical-align: middle;
    overflow: hidden;
    /*display:table-cell;*/
    /*position: relative;*/
    p {
      /*position: absolute;*/
      /*top: 20px;*/
      line-height: 24px;
      vertical-align: middle;
      text-align: left;
      text-indent: 2em;
    }
  }
}
.plug-intwo {
  vertical-align: middle;
  img {
    display: inline-block;
    vertical-align: middle;
  }
}
.brief_top {
  margin-top: 20px;
}
//功能
.func {
  margin-top: 40px;
  display: flex;
  .download {
    background: #24416b;
    padding: 0;
    margin: 0;
    color: white !important;
    flex: 1;
    margin-right: 100px;
    height: 40px;
    line-height: 40px;
    font-size: 18px;
    .download-conter {
      width: 100%;
      /*display: flex;*/
      position: relative;
      /*justify-content: space-between;*/
      span {
        a {
          color: white;
          width: 100%;
          height: 100%;
          display: inline-block;
        }
      }
      .downloadclick {
        margin: 0;
        padding: 0 20px;
        background: #284d7c;
        border: 0;
        height: 100%;
        position: absolute;
        top: -1px;
        right: 0;
      }
    }
  }
  button:last-child {
    margin: 0;
  }
}
.functionone {
  margin-top: 100px;
  vertical-align: middle;
  .functionone-content {
  }
  img {
    width: 100%;
  }
}

//图片
.pictype {
  width: 100%;
  margin-top: 100px;
  video {
    width: 100%;
    object-fit: fill;
  }
}

@media only screen and (max-width: 900px) {
  .homecontent {
    margin: 20px 10%;
    margin-bottom: 60px;
    .introduce {
      margin-top: 30px;
    }
  }
  .functionone {
    margin-top: 30px;
  }
  .pictype {
    margin-top: 30px;
  }
  .plug-in {
    width: 100px;
    height: 100px;
    img {
      width: 100%;
      /*vertical-align: text-top;*/
    }
  }
  .rightcont {
    height: 70px;
    p {
      line-height: 70px;
    }
  }
  .brief_introduction {
    p {
      font-size: 14px;
    }
  }
  .rightcont-o {
    padding: 0;
  }
  //功能
  .func {
    margin-top: 10px;
    .download {
      width: 100%;
      margin-right: 20px;
    }
  }
}
@media only screen and (max-width: 650px) {
  .func {
    display: block;
    .download {
      margin-right: 20px;
      margin-top: 10px !important;
    }
  }
}

.plug-detail {
  .xg-ny-title {
    margin-bottom: 24px;
    padding-bottom: 0;
    border-bottom: 0;
  }
  .tip {
    padding: 10px;
    background: #e1e8ef;
    font-size: 12px;
    color: #4f5760;
    text-align: center;
    margin-bottom: 50px;
  }
  .plug-prev,
  .plug-next {
    width: 30px;
    height: 30px;
    border-color: #9ea8b2;
    border-width: 2px;
    border-top-style: solid;
    position: absolute;
    top: 50%;
    cursor: pointer;
    outline: none;
  }
  .plug-prev {
    left: -110px;
    transform: rotate(-45deg) translateY(-50%);
    border-left-style: solid;
  }
  .plug-next {
    right: -110px;
    transform: rotate(45deg) translateY(-50%);
    border-right-style: solid;
  }
  .plug-main {
    position: relative;
    display: flex;
    align-items: flex-start;
  }
  .cover {
    width: 25%;
    flex-shrink: 0;
    border-radius: 45px;
    overflow: hidden;
  }
  .info {
    width: 47%;
    margin-right: 40px;
    margin-left: 50px;
    padding-top: 10px;
    .title {
      font-size: 26px;
      font-weight: bold;
      color: #000;
      margin-bottom: 20px;
    }

    .attribute {
      .avatar {
        display: inline-block;
        width: 50px;
        height: 50px;
        position: relative;
            margin-right: 10px;
        .img-box {
          width: 50px;
          height: 50px;
          border-radius: 100%;
          overflow: hidden;
          img {
            width: 50px;
            height: auto;
          }
        }
        .fansicon {
          position: absolute;
          bottom: 0;
          right: 0;
          width: 23px;
          height: 23px;
          border: #000;
          background-color: #0d6efd;
          text-align: center;
          line-height: 20px;
          color: #ffffff;
          font-weight: 700;
          border-radius: 35px;
        }
      }
      .item {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #818e9d;
        margin-bottom: 20px;
        .text {
          font-size: 14px;
          width: 85px;
          padding-right: 20px;
          text-align: left;
        }
        &.item1 {
          font-size: 14px;
          span {
            color: #ff0000;
          }
          strong {
            font-size: 22px;
            line-height: 1;
          }
        }
        &.item2 {
          span {
            color: #0084ff;
          }
        }
        &.item3 {
          span {
            color: #ff0000;
          }
        }
        &.item4 {
          span {
            color: #4f5760;
          }
        }
      }
    }

    .buy {
      background: #0084ff;
      color: #fff;
      height: 50px;
      line-height: 50px;
      text-align: center;
      border-radius: 2px;
      margin-bottom: 20px;
    }

    .detail {
      text-align: justify;
      position: relative;
      font-size: 14px;
      color: #4f5760;
      line-height: 1.8;
      .xg-more {
        cursor: pointer;
        display: inline-block;
        height: 26px;
        padding-left: 40px;
        color: #0084ff;
        font-size: 14px;
        margin-left: -50px;
        background-image: linear-gradient(
          to left,
          rgb(244, 244, 244) 50%,
          transparent
        );
      }
    }
  }
  .introduce {
    width: 28%;
    flex-shrink: 0;
    padding-left: 40px;
    border-left: 1px solid #c9c9c9;
    .title {
      color: #4f5d6a;
      font-size: 18px;
      font-weight: bold;
      line-height: 1.2;
      margin-bottom: 15px;
    }

    .image {
      position: relative;
      background: #4f5d6a;
      border-radius: 5px;
      padding: 14px;
      width: 100%;
      margin-bottom: 15px;
      cursor: pointer;
      img {
        width: 100%;
      }
      .icon {
        position: absolute;
        font-size: 20px;
        color: #949494;
        right: 24px;
        bottom: 24px;
      }
    }

    .xg-btn-group {
      .xg-btn {
        position: relative;
        color: #4f5d6a;
        font-size: 14px;
        border: 2px solid #c9c9c9;
        border-radius: 6px;
        display: block;
        margin-bottom: 15px;
        text-align: left;
        cursor: pointer;
        outline: none;
        padding-left: 20px;
        padding-right: 20px;
        height: 50px;
        line-height: 50px;
        &::before,
        &::after {
          content: "";
          position: absolute;
          right: 20px;
          top: 50%;
          transform: translateY(-50%);
          display: block;
        }
        &::before {
          width: 14px;
          height: 14px;
          border-top: 2px solid #c9c9c9;
          border-right: 2px solid #c9c9c9;
          margin-top: -2px;
          transform: rotate(45deg) translateY(-50%);
        }
        &::after {
          width: 20px;
          height: 2px;
          background: #c9c9c9;
          transition: all 0.4s;
          margin-right: -6px;
        }
        &:hover {
          color: #fff;
          background: #0084ff;
          border-color: #0084ff;
          &:before {
            border-color: #fff;
          }
          &:after {
            background: #fff;
            width: 40px;
          }
        }
      }
    }
  }

  // 案例下载弹窗
  .plug-open {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
    transition: all 0.4s cubic-bezier(0.18, 0.89, 0.32, 1.28);
    transform: scale(0.8);
    visibility: hidden;
    opacity: 0;

    &.active {
      transform: scale(1);
      opacity: 1;
      visibility: visible;
    }
  }
  .plug-box {
    position: relative;
    text-align: left;
    width: 100%;
    max-width: 460px;
    // height: 380px;
    background: #fff;
    box-shadow: 0px 0px 30px 0px rgba(4, 0, 0, 0.3);
    border-radius: 6px;
    padding: 30px 25px;
    .close {
      font-size: 30px;
      color: #96a1ac;
      position: absolute;
      top: 5px;
      right: 10px;
      cursor: pointer;
    }
    .open-title {
      color: #0084ff;
      line-height: 1.2;
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 15px;
    }
    /deep/ .el-table {
      &::before {
        display: none;
      }
      .cell {
        text-align: left !important;
        color: #909399;
        font-size: 12px;
      }
      th .cell {
        color: #4f5760;
        font-weight: bold;
      }
      .el-icon-download {
        font-size: 20px;
        color: #0084ff;
        text-align: center;
      }
      .el-table__cell {
        padding-top: 10px;
        padding-bottom: 10px;
      }
      th.el-table__cell {
        padding-bottom: 15px;
      }
      .el-table__body-wrapper {
        &::-webkit-scrollbar {
          /*滚动条整体样式*/
          width: 4px; /*高宽分别对应横竖滚动条的尺寸*/
          height: 1px;
        }
        &::-webkit-scrollbar-thumb {
          /*滚动条里面小方块*/
          border-radius: 10px;
          background-color: #b8b8b8;
        }
        &::-webkit-scrollbar-track {
          /*滚动条里面轨道*/
          box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
          background: #f4f4f4;
          border-radius: 10px;
        }
      }
    }

    .head {
      color: #4f5760;
      font-size: 12px;
      line-height: 1.2;
      display: flex;
      align-items: center;
      padding-bottom: 20px;
      font-weight: bold;
      & > div {
        flex-grow: 1;
      }
      .icon {
        flex-shrink: 0;
        width: 40px;
      }
    }
  }
  // 使用介绍 弹框
  .introduce-open {
    padding-left: 20px;
    padding-right: 20px;
    z-index: 999;
    position: fixed;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    left: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.75);
    .close {
      position: absolute;
      top: 22px;
      right: 22px;
      font-size: 24px;
      color: #929da8;
      z-index: 3;
      cursor: pointer;
    }
    /deep/ .introduce-box {
      position: relative;
      background: #242b33;
      padding: 90px 76px;
      padding-bottom: 166px;
      max-width: 1220px;
      width: 100%;
      .swiper-button-prev,
      .swiper-button-next {
        background-image: none;
        border-width: 1px;
        border-top-style: solid;
        border-color: #96a1ac;
        position: absolute;
        top: 50%;
        width: 30px;
        height: 30px;
      }
      .swiper-button-prev {
        border-left-style: solid;
        transform: rotate(-45deg) translateY(-50%);
        left: 35px;
      }
      .swiper-button-next {
        border-right-style: solid;
        transform: rotate(45deg) translateY(-50%);
        right: 35px;
      }
      .swiper-pagination {
        font-size: 0;
        bottom: 30px;
        position: absolute;
        left: 0;
        width: 100%;
        z-index: 99;
        display: flex;
        align-items: center;
        justify-content: center;
        .swiper-pagination-bullet {
          width: 10px;
          height: 10px;
          border-radius: 100%;
          background: #96a1ac;
          margin-left: 4px;
          margin-right: 4px;
          opacity: 1;
        }
        .swiper-pagination-bullet-active {
          background: #0d6efd;
        }
      }
    }
  }
  //
  .plug-swiper-box {
    position: relative;
    .swiper-button-prev,
    .swiper-button-next {
      background-image: none;
      position: absolute;
      top: 50%;
      cursor: pointer;
      outline: none;
      display: block;
      width: 30px;
      height: 30px;
      border-color: #9ea8b2;
      border-width: 2px;
      border-top-style: solid;
      margin: 0;
    }
    .swiper-button-prev {
      left: -110px;
      transform: rotate(-45deg) translateY(-50%);
      border-left-style: solid;
    }
    .swiper-button-next {
      right: -110px;
      transform: rotate(45deg) translateY(-50%);
      border-right-style: solid;
    }
  }
  // 累计数量
  .download-count {
    text-align: right;
    margin-top: 10px;
    color: #8f9299;
    font-size: 14px;
    strong {
      color: #ff3d3d;
    }
  }
}
@media screen and (max-width: 751px) {
  .plug-detail .xg-ny-title {
    margin-bottom: 20px;
  }
  .plug-detail .tip {
    padding: 10px 15px;
    margin-bottom: 30px;
  }
  .plug-detail .plug-main {
    flex-wrap: wrap;
  }
  .plug-detail .cover {
    width: 80%;
    display: block;
    margin: 0 auto 20px;
  }
  .plug-detail .info {
    width: 100%;
    margin: 0;
    padding: 0;
  }
  .plug-detail .info .title {
    font-size: 18px;
    margin-bottom: 15px;
  }
  .plug-detail .info .attribute .item .text {
    width: 75px;
    padding-right: 10px;
  }
  .plug-detail .info .attribute .item {
    margin-bottom: 10px;
  }
  .plug-detail .info .attribute .item .text {
    font-size: 12px;
  }
  .plug-detail .info .attribute .item.item1 {
    font-size: 12px;
  }
  .plug-detail .info .attribute .item.item1 strong {
    font-size: 18px;
  }
  .plug-detail .info .detail {
    margin-bottom: 20px;
  }
  .plug-detail .info .buy {
    height: 40px;
    line-height: 40px;
    font-size: 12px;
    margin-top: 10px;
  }
  .plug-detail .introduce {
    width: 100%;
    padding-left: 0;
    border-left: 0;
  }
  .plug-detail .introduce .xg-btn-group .xg-btn {
    font-size: 12px;
    padding-left: 10px;
    padding-right: 10px;
    height: 40px;
    line-height: 40px;
    border-width: 1px;
    margin-bottom: 10px;
  }
  .plug-detail .plug-open {
    width: 90%;
    left: 5%;
    margin-left: auto;
    padding: 20px;
  }
  .plug-detail .plug-open .open-title {
    font-size: 16px;
  }
  .plug-detail .plug-open /deep/ .el-table {
    th.el-table__cell {
      padding-bottom: 10px;
    }
  }
  .plug-detail .introduce-open {
    padding: 20px 30px;
    padding-bottom: 40px;
  }
  .plug-detail .introduce-open {
    .close {
      right: 5px;
      top: 5px;
    }
    /deep/ .introduce-box {
      padding: 30px 20px;
      .swiper-pagination {
        bottom: 10px;
        .swiper-pagination-bullet {
          width: 6px;
          height: 6px;
        }
      }
      .swiper-button-prev,
      .swiper-button-next {
        width: 15px;
        height: 15px;
        display: none;
      }
      .swiper-button-prev {
        left: 20px;
      }
      .swiper-button-next {
        right: 20px;
      }
    }
  }
}
</style>

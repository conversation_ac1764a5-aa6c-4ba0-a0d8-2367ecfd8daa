<template>
  <div class="xg-ny xg-news-detail">
    <div class="xg-box">
      <div class="xg-head">
        <div class="title">{{ info.title }}</div>
        <div class="info">
          <!-- 作者 -->
          <div class="author">{{ info.author }}</div>
          <!-- 时间 -->
          <div class="xg-time">{{ info.time }}</div>
          <!-- 分类 -->
          <div class="cate">分类：{{ info.cate ? info.cate.name : "" }}</div>
        </div>

        <div class="tag-box" v-if="info.tag_list_info">
          <el-tag
            @click="go(item)"
            class="tag-item"
            v-for="(item, i) in info.tag_list_info"
            :key="i"
            >{{ item.tag }}</el-tag
          >
        </div>
      </div>
      <!-- 正文 -->
      <div class="xg-body" v-html="info.content"></div>
      <!-- 切换文章 -->
      <div class="xg-btn-group">
        <div
          class="xg-line-1 xg-btn"
          v-if="info.prev"
          @click="jump(info.prev.id)"
        >
          上一篇：{{ info.prev.title }}
        </div>
        <div
          class="xg-line-1 xg-btn"
          v-if="info.next"
          @click="jump(info.next.id)"
        >
          下一篇：{{ info.next.title }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "newsdetail",
  components: {},
  data() {
    return {
      info: {},
    };
  },
  mounted() {
    this.getNewsInfo();
  },
  methods: {
    
    go(item) {
      this.$router.push("/search?tag=" + item.id + "&type=news");
    },
    //  跳转
    jump(id) {
      if (this.$route.params.id != id) {
        this.$router.push({ name: "newsdetail", params: { id: id } });
      }
    },

    getNewsInfo() {
      var id = this.$route.params.id;
      if (id) {
        this.$axios
          .get(this.url + "/api/news/" + id, {
            params: {},
          })
          .then((res) => {
            if (res.data.code === 200) {
              this.info = res.data.data;
            }
          })
          .catch((err) => {});
      }
    },
  },
  watch: {
    $route: {
      handler(route) {
        this.getNewsInfo();
      },
    },
  },
};
</script>

<style scoped lang="scss">
.tag-box {
  text-align: left;
  .tag-item {
    margin: 5px 20px 5px 0;
    cursor: pointer;
  }
}
.newsdetail {
  margin: 80px 20%;
  background: white;
  min-height: 600px;
  padding: 40px 20px;
  .top {
    margin-top: 50px;
  }
  .clearfix {
    content: "";
    display: block;
    overflow: hidden;
    clear: both;
  }
}
.newstext {
  img {
    max-width: 100% !important;
  }
  .mainp {
    text-align: left;
    text-indent: 2em !important;
  }
}
.navbar-expand-lg .navbar-collapse {
  display: flex;
  justify-content: space-between;
}
.navbar {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  width: 100%;
  /*position: relative;*/
  /*top: 0;*/
  z-index: 99;
  position: absolute;
  top: 0;
  left: 0;
}
button {
  outline: none;
}
.bg-info {
  /*background-color: rgba(0,0,0,0) !important;*/
  background-color: rgb(17, 19, 42) !important;
}
.navbar-dark .navbar-brand,
.navbar-dark .navbar-nav .nav-link {
  color: white;
}
.btn-success {
  color: white !important;
  background: none !important;
  border: none !important;
  margin-top: 5px;
}

@media only screen and (max-width: 900px) {
  .newsdetail {
    margin: 40px 5%;
    background: white;
    min-height: 600px;
    padding: 20px 10px;
  }
  .navbar-expand-lg .navbar-collapse {
    display: block;
  }
  .navbar-dark .navbar-toggler {
    color: rgba(0, 0, 0, 0.5);
    border-color: rgba(0, 0, 0, 0.5);
  }
  /deep/.navbar-dark .navbar-toggler-icon {
    background-image: url("../../assets/img/nav.png");
  }
  .newstext {
    margin: 20px;
  }
}

/* ==================== 内页 - 新闻详情 start ==================== */
.xg-news-detail {
  & > .xg-box {
    background-color: #fff;
    box-shadow: 0px 0px 20px 0px rgba(4, 0, 0, 0.2);
    border-radius: 8px;
    padding: 70px 76px;
  }
  .xg-head {
    text-align: left;
    padding-bottom: 30px;
    border-bottom: 1px solid #dbdddf;
    width: 100%;
    margin-bottom: 30px;
    background: url(../../assets/img/ny-bg.png) no-repeat right top;
    background-size: 40px;
  }
  .title {
    font-size: 26px;
    color: #000;
    line-height: 1.6;
  }
  .info {
    display: flex;
    align-items: center;
    color: #88929e;
    font-size: 14px;
  }
  .xg-body {
    font-size: 14px;
    color: #000;
    line-height: 1.8;
  }
  .xg-btn-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 45px;
    border-top: 1px solid #dbdddf;
    padding-top: 45px;
  }
  .xg-btn {
    width: 430px;
    height: 40px;
    line-height: 40px;
    border-radius: 100px;
    border: 1px solid #dbdddf;
    color: #88929e;
    transition: all 0.4s;
    font-size: 13px;
    text-align: left;
    padding-left: 20px;
    padding-right: 20px;
    cursor: pointer;
    &:hover {
      color: #0d6efd;
      border-color: #0d6efd;
    }
  }
}
@media screen and (max-width: 1300px) {
  .xg-news-detail {
    & > .xg-box {
      max-width: 90%;
    }
  }
}
@media screen and (max-width: 751px) {
  .xg-news-detail {
    padding: 30px 20px;
    & > .xg-box {
      max-width: 100%;
      box-shadow: 0px 0px 10px 0px rgba(4, 0, 0, 0.2);
      padding: 30px 25px;
    }
  }
  .xg-news-detail .title {
    font-size: 16px;
    line-height: 1.6;
    font-weight: bold;
    padding-bottom: 15px;
  }
  .xg-news-detail .info {
    font-size: 13px;
  }
  .xg-news-detail .xg-head {
    margin-bottom: 15px;
    padding-bottom: 20px;
  }
  .xg-news-detail .xg-btn-group {
    margin-top: 20px;
    padding-top: 10px;
    flex-wrap: wrap;
  }
  .xg-news-detail .xg-btn-group .xg-btn {
    width: 100%;
    height: 30px;
    line-height: 30px;
    font-size: 13px;
    margin-top: 15px;
  }
}
/* ==================== 内页 - 新闻详情 end ==================== */
</style>

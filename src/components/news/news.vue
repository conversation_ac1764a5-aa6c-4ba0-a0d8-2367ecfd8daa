<template>
  <div class="xg-ny xg-ny-news">
    <div class="xg-box">
      <div class="head">
        <div class="left">新闻</div>
        <!-- 分类 -->
        <div class="right">
          <div
            class="item"
            v-for="(item, index) in cates"
            :key="item.id"
            :class="{ active: item.id == current }"
            @click="tabChange(item.id)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>

      <!-- 对应分类的数据 -->
      <div class="xg-body" v-loading="loading">
        <template v-if="items.length > 0">
          <!-- 置顶新闻新闻 -->
          <div class="left-news">
            <div class="top" @click="details(4)">
              <!-- 文章图片 -->
              <div class="xg-image image">
                <img
                    :src="newest.images"
                    alt=""
                />
              </div>
              <!-- 文章标题 -->
              <div class="title">
                <div class="xg-line-1 text">{{ newest.title }}</div>
                <div class="xg-time">{{ newest.time }}</div>
              </div>
            </div>
            <!--  -->
            <div class="bottom" @click="details(newest.id)">
              <div class="title">
                <div class="xg-line-1 text">
                  {{ newest.title }}
                </div>
                <div class="xg-time">{{ newest.time }}</div>
              </div>
              <div class="xg-line-2 desc">
                {{newest.content}}
              </div>
              <div class="xg-more">详情+</div>
            </div>
          </div>
          <!-- 新闻列表 -->
          <div class="right-news">
            <div
                class="item"
                v-for="(news, index) in items"
                :key="index"
                @click="details(news.id)"
            >
              <div class="xg-time">
                <div class="day">{{ news.day }}</div>
                <div class="year">{{news.year}}</div>
              </div>
              <div class="info">
                <div class="xg-line-1 title">
                  {{news.title}}
                </div>
                <div class="xg-line-2 desc">
                  {{news.content}}
                </div>
              </div>
            </div>
            <!-- 分页 -->
            <el-pagination
                class="xg-pagination"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page.sync="pagination.offset"
                :page-size="pagination.limit"
                layout="prev, pager, next, jumper"
                :total="pagination.total"
            >
            </el-pagination>
          </div>
        </template>
        <template v-else>
          <el-empty description="暂无新闻"></el-empty>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "news",
  components: {},
  data() {
    return {
      pagination: {
        total: 0,
        offset: 0,
        limit: 9,
      },
      loading: false,
      // 选中的选项
      current: 0,
      //新闻列表
      items: [],
      // 新闻分类
      cates: [],
      newest: {}
    };
  },
  mounted() {
    this.getData();
    this.getCates();
  },
  methods: {
    handleSizeChange(val) {
      this.pagination.limit = val;
    },
    // current page news info
    handleCurrentChange(val) {
      let page = val - 1;
      let cateid = this.$route.query.cate_id;
      this.getData(cateid, this.pagination.limit, page);
    },

    details(id) {
      this.$router.push({ name: "newsdetail", params: { id: id } });
    },

    // get news info list
    getData(cate_id='', limit = 9, offset = 0) {
      this.$axios
        .get(this.url + "/api/news", {
          params: {
            limit: limit,
            offset: offset,
            cate_id: cate_id,
          },
        })
        .then((res) => {
          if (res.data.code === 200) {
            this.items = res.data.data.news;
            this.pagination.total = res.data.data.total;
            this.newest = res.data.data.newest;
          } else {
          }
        })
        .catch((err) => {});
    },

    getCates() {
      this.loading = true;
      this.$axios
          .get(this.url + "/api/news/cate", {
            params: {},
          })
          .then((res) => {
            if (res.data.code === 200) {
              this.cates = res.data.data;
              this.activeName = this.cates ? this.cates[0]['id']+'':'0';
            } else {
            }
            this.loading = false;
          })
          .catch((err) => {
            this.loading = false;
          });
    },
    /**
     * 分类选项点击事件
     */
    tabChange(e) {
      let cateid = this.$route.query.cate_id;
      if(cateid != e){
        this.$router.push({name:'news', query:{cate_id:e}})
        this.current = e
      }
    },
  },
  watch: {
    "$route": {
      handler (route) {
        let cateid = this.$route.query.cate_id;
        this.activeName = cateid ? cateid+'' : '0';
        this.getData(cateid)
      }
    }
  }
};
</script>

<style scoped lang="scss">
.newss {
  overflow: hidden;
}
.newssbanner {
  background-color: #42b983;
  .banner {
    img {
      width: 100%;
      height: 500px;
    }
  }
}
.newsslist {
  margin: 20px 20%;
  text-align: left;
}
.news-content-list {
  margin-top: 120px;
  margin-bottom: 100px;
  padding: 0 10px;
  .newslist {
    margin-top: 20px;
    background: white;
    padding: 20px 0;
    padding-bottom: 14px;
    cursor: pointer;
    .p1 {
      font-size: 16px;
    }
  }
  .el-pagination {
    text-align: center;
  }
}
.centerp {
  .p1 {
    font-weight: 600;
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
  }
  .p2 {
    width: 100%;
    font-size: 12px;
    margin: 0;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 这里是超出几行省略 */
    overflow: hidden;
    margin-top: 30px;
  }
}

.new-images {
  height: 100px;
  width: 100%;
}

@media only screen and (max-width: 900px) {
  .newsslist {
    margin: 20px 2%;
    text-align: left;
    .el-card {
      margin-top: 20px;
    }
    .p2 {
      margin-top: 5px;
    }
  }
  .news-content-list {
    margin: 0 20px;
  }
}

.time {
  font-size: 13px;
  color: #999;
  float: left;
  display: block;
  width: 80%;
}

.bottom {
  margin-top: 13px;
  line-height: 12px;
}

.button {
  padding: 0;
  float: right;
  display: block;
  width: 20%;
}

.image {
  width: 100%;
  display: block;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
.navbar-expand-lg .navbar-collapse {
  display: flex;
  justify-content: space-between;
}
.navbar {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  width: 100%;
  /*position: relative;*/
  /*top: 0;*/
  z-index: 99;
  /*position: absolute;*/
  /*top:0;*/
  /*left: 0;*/
}
button {
  outline: none;
}
.bg-info {
  /*background-color: rgba(0,0,0,0)!important;*/
  background-color: rgb(17, 19, 42) !important;
  //-webkit-box-shadow: 0 0 4px 0 rgb(0 0 0 / 1%), 0 4px 4px 0 rgb(0 0 0 / 4%);
  /*-moz-box-shadow: 0 0 4px 0 rgba(0,0,0,.01),0 4px 4px 0 rgba(0,0,0,.04);*/
  //box-shadow: 0 0 4px 0 rgb(0 0 0 / 1%), 0 4px 4px 0 rgb(0 0 0 / 4%);
}
.navbar-dark .navbar-brand,
.navbar-dark .navbar-nav .nav-link {
  color: white;
}
.btn-success {
  color: white !important;
  background: none !important;
  border: none !important;
  margin-top: 5px;
}

@media only screen and (max-width: 900px) {
  .navbar-expand-lg .navbar-collapse {
    display: block;
  }
  .navbar-dark .navbar-toggler {
    color: rgba(0, 0, 0, 0.5);
    border-color: rgba(0, 0, 0, 0.5);
  }
  /deep/.navbar-dark .navbar-toggler-icon {
    background-image: url("../../assets/img/nav.png");
  }
}

/* ==================== 内页 - 新闻中心 start ==================== */
.xg-ny-news {
  .head {
    display: flex;
    align-items: flex-start;
    border-bottom: 1px solid #dae2e9;
    margin-bottom: 50px;
    .left {
      text-align: left;
      font-size: 32px;
      color: #0d6efd;
      line-height: 1.2;
      min-width: 0;
      flex-grow: 1;
    }
    .right {
      display: flex;
      align-items: center;
    }
    .item {
      font-size: 16px;
      color: #818e9d;
      position: relative;
      margin-right: 60px;
      padding-bottom: 36px;
      cursor: pointer;
      &:last-child {
        margin-right: 0;
      }
      &.active,
      &:hover {
        color: #0d6efd;
        font-weight: bold;
        &:after {
          width: 100%;
          opacity: 1;
        }
      }
      &:after {
        content: "";
        display: block;
        width: 0;
        height: 2px;
        background: #0d6efd;
        opacity: 0;
        transition: all 0.4s;
        z-index: 9;
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }
  }
  .xg-body {
    display: flex;
    align-items: flex-start;
    .el-empty{display:block;margin:0 auto;}
    .left-news {
      width: 520px;
      flex-shrink: 0;
      margin-right: 40px;
    }
    .top {
      position: relative;
      cursor: pointer;
      .image {
        width: 100%;
      }
      .title {
        display: flex;
        align-items: center;
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        background: rgba(0, 0, 0, 0.8);
        color: #fff;
        height: 52px;
        line-height: 52px;
        padding-left: 20px;
        padding-right: 20px;
      }
      .text {
        min-width: 0;
        flex-grow: 1;
        font-size: 14px;
        color: #fff;
        line-height: 1.2;
      }
      .xg-time {
        flex-shrink: 0;
        font-size: 14px;
        color: #9c9c9c;
        line-height: 1.2;
        display: inline-block;
      }
    }
    .bottom {
      cursor: pointer;
      .title {
        margin-top: 15px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
      }
      .text {
        font-size: 16px;
        color: #000;
        line-height: 1.2;
        min-width: 0;
        flex-grow: 1;
      }
      .xg-time {
        font-size: 14px;
        color: #7d7d7d;
        line-height: 1.2;
      }
      .desc {
        font-size: 14px;
        line-height: 1.8;
        text-align: justify;
        color: #818e9d;
        margin-bottom: 15px;
      }
      .xg-more {
        color: #000;
        font-size: 14px;
        line-height: 1.2;
        text-align: left;
      }
    }
    .right-news {
      min-width:0;flex-grow:1;
      .item {
        cursor: pointer;
        display: flex;
        align-items: center;
        padding-bottom: 30px;
        border-bottom: 1px solid #dbdddf;
        position: relative;
        margin-bottom: 30px;
        &:last-child {
          margin-bottom: 50px;
        }
        &:hover {
          &:after {
            width: 100%;
          }
          .xg-time {
            background: #0d6efd;
          }
        }
        &:after {
          content: "";
          display: block;
          width: 0;
          height: 2px;
          background: #0d6efd;
          transition: all 0.4s;
          position: absolute;
          bottom: 0;
          left: 0;
        }
      }
      .xg-time {
        flex-shrink: 0;
        width: 80px;
        height: 80px;
        border-radius: 12px;
        overflow: hidden;
        background: #a3acb6;
        display: flex;
        align-items: center;
        flex-flow: column;
        justify-content: center;
        color: #fff;
        transition: all 0.4s;
        margin-right: 20px;
      }
      .day {
        font-size: 42px;
        line-height: 1;
      }
      .year {
        font-size: 12px;
        line-height: 1.2;
      }
      .info {
        min-width: 0;
        flex-grow: 1;
      }
      .title {
        font-size: 16px;
        color: #000;
        line-height: 1.2;
        margin-bottom: 10px;
      }
      .desc {
        font-size: 14px;
        color: #88928e;
        line-height: 1.8;
        height: 3.6em;
        text-align: justify;
      }
    }
  }
}
@media screen and (min-width: 751px) {
  .xg-ny-news {
  }
}
@media screen and (max-width: 751px) {
  .xg-ny-news .xg-box {
    padding-bottom: 0;
  }
  .xg-ny-news .head {
    flex-wrap: wrap;
    margin-bottom: 20px;
    border-bottom: 0;
  }
  .xg-ny-news .head .left {
    width: 100%;
    text-align: center;
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 20px;
  }
  .xg-ny-news .head .right {
    flex-wrap: wrap;
    width: 100%;
  }
  .xg-ny-news .head .item {
    margin-right: 4%;
    width: 48%;
    text-align: center;
    padding-bottom: 10px;
    font-size: 14px;
    border-bottom: 1px solid #ddd;
  }
  .xg-ny-news .head .item:nth-child(2n) {
    margin-right: 0;
  }
  .xg-ny-news .head .item:nth-child(2) ~ .item {
    margin-top: 10px;
  }
  .xg-ny-news .xg-body {
    flex-wrap: wrap;
  }
  .xg-ny-news .xg-body .left-news {
    width: 100%;
    margin-bottom: 20px;
    margin-right: 0;
  }
  .xg-ny-news .xg-body .right-news {
    width: 100%;
  }
  .xg-ny-news .xg-body .top .title {
    height: 40px;
    line-height: 40px;
    padding-left: 10px;
    padding-right: 10px;
  }
  .xg-ny-news .xg-body .top .text {
    font-size: 14px;
  }
  .xg-ny-news .xg-body .top .xg-time {
    font-size: 12px;
  }
  .xg-ny-news .xg-body .bottom .title {
    margin-top: 20px;
    margin-bottom: 10px;
  }
  .xg-ny-news .xg-body .bottom .title .text {
    font-size: 14px;
  }
  .xg-ny-news .xg-body .bottom .title .xg-time {
    font-size: 12px;
  }
  .xg-ny-news .xg-body .bottom .desc {
    font-size: 12px;
    line-height: 1.6;
    height: 3.2em;
    margin-bottom: 10px;
  }
  .xg-ny-news .xg-body .right-news .xg-time {
    width: 60px;
    height: 60px;
    border-radius: 6px;
  }
  .xg-ny-news .xg-body .right-news .day {
    font-size: 26px;
    margin-bottom: 0;
  }
  .xg-ny-news .xg-body .right-news .item {
    align-items: flex-start;
    padding-bottom: 15px;
    margin-bottom: 15px;
  }
  .xg-ny-news .xg-body .right-news .title {
    font-size: 14px;
  }
  .xg-ny-news .xg-body .right-news .desc {
    font-size: 12px;
    line-height: 1.6;
    height: 3.2em;
  }
}
/* ==================== 内页 - 新闻中心 end ==================== */
</style>

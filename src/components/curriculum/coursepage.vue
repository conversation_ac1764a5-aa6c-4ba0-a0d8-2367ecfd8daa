<template>
  <div class="xg-course-details">
    <div class="top">
      <div class="xg-box">
        <!-- 视频框架 -->
        <div class="topvideo clearfix">
          <!-- 视频主体 -->
          <div id="video-el-col-l" class="main-video">
            <div>
              <div id="video" v-if="Object.keys(taskData).length !== 0">
                <ali-player
                  ref="player"
                  :source="taskData.link"
                  :cover="taskData.images"
                  width="100%"
                  height="100%"
                  :preload="true"
                  :useH5Prism="true"
                  class="video"
                  @ready="onReady(taskData)"
                  @play="onPlay(taskData)"
                  @pause="onPause(taskData)"
                  v-show="taskData.link"
                >
                </ali-player>
                <div
                  class="preview-vod-component"
                  style="display: block"
                  v-if="courseInfo.purchase !== 1 && taskData.is_first !== 1"
                >
                  <div class="preview-component-layer" style="display: block">
                    <div
                      class="preview-custom"
                      style="font-size: 30px; color: #e6a23c"
                    >
                      请将课程加入学习课程中观看！
                    </div>
                  </div>
                  <div class="preview-component-tip" style="display: none">
                    <span class="can-preview"></span
                    ><span class="preview-time"></span
                    ><span class="preview-custom-bar"></span>
                    <span class="preview-vod-close"></span>
                  </div>
                </div>
                <div
                  class="preview-vod-component"
                  style="display: block"
                  v-if="courseInfo.purchase === 1 && taskData.lock_status === 1"
                >
                  <div class="preview-component-layer" style="display: block">
                    <div
                      class="preview-custom"
                      style="font-size: 30px; color: #e6a23c"
                    >
                      视频暂不支持在线播放，请联系相关客服人员！
                    </div>
                  </div>
                  <div class="preview-component-tip" style="display: none">
                    <span class="can-preview"></span
                    ><span class="preview-time"></span
                    ><span class="preview-custom-bar"></span>
                    <span class="preview-vod-close"></span>
                  </div>
                </div>
                <div class="contbox">
                  <watermark>{{ taskData.name }}</watermark>
                </div>
              </div>
            </div>
          </div>
          <!-- 视频目录 -->
          <div id="video-el-col-r" class="video-directory title-left">
            <el-collapse v-model="activeNames">
              <el-collapse-item
                v-for="(chapter, index) in curdata"
                :key="index"
                :name="index"
              >
                <template slot="title">
                  <span style="float: left; font-size: 10px"
                    >{{ chapter.name
                    }}<i class="el-icon-lock" v-show="chapter.lock_status == 1"
                  /></span>
                </template>
                <ul>
                  <li
                    v-for="(val, i) in chapter.task"
                    :key="i"
                    :class="{
                      active: val.id == num && val.type === 'task',
                    }"
                    class="clearfix"
                    @click="tab(val, index)"
                  >
                    <span></span
                    ><span
                      >{{ val.name
                      }}<i
                        class="el-icon-lock"
                        v-show="
                          val.lock_status == 1 || chapter.lock_status == 1
                        "
                    /></span>
                  </li>
                </ul>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
        <!-- 视频信息 -->
        <div class="video-info">
          <div class="xg-left">
            <div class="xg-flex xg-top">
              <!-- 视频名称 -->
              <div class="video-name">{{ courseInfo.name }}</div>
              <!-- 学习人数 -->
              <div class="study-count" v-if="courseInfo.num > 0">
                已有<strong>{{ courseInfo.num }}</strong
                >人学习
              </div>
              <!-- 收藏 -->
              <div
                class="collection"
                @click="collect(courseInfo.id)"
                v-if="!courseInfo.collect"
              ></div>
              <!-- 加入学习 -->
              <div
                class="learning"
                @click="getUrl()"
                v-if="!courseInfo.purchase && courseInfo.price > 0"
              >
                加入学习
              </div>
              <!-- 购买本课 -->
              <div
                class="buy"
                @click="freeCourse()"
                v-if="!courseInfo.purchase && courseInfo.price <= 0"
              >
                购买本课
              </div>
            </div>
            <!--  -->
            <div class="xg-bottom">
              <div class="xg-flex text">
                <!-- 作者 -->
                <div class="author">{{ courseInfo.author }}</div>
                <!-- 发布时间 -->
                <div class="author">发布于：{{ courseInfo.created_at }}</div>
                <!-- 播放量 -->
                <div class="count">{{ courseInfo.view_num }}</div>
              </div>
              <!-- 上一个下一个 -->
              <div class="xg-flex goto">
                <span @click="perv" class="perv">
                  <i class="el-icon-arrow-left"></i>
                </span>
                <span @click="next" class="next">
                  <i class="el-icon-arrow-right"></i>
                </span>
              </div>
            </div>

            <div class="tag-box">
              <el-tag
                @click="go(item)"
                class="tag-item"
                v-for="(item, i) in courseInfo.tag_list_info"
                :key="i"
                >{{ item.tag }}</el-tag
              >
            </div>
          </div>
          <!-- 广告图 -->
          <div class="xg-ad" v-if="courseInfo.ad && courseInfo.ad.file">
            <a :href="courseInfo.ad.link">
              <img :src="courseInfo.ad.file" alt="广告图" />
            </a>
          </div>
        </div>
      </div>
    </div>
    <!-- 介绍，附件，评论 -->
    <div class="bottom-head">
      <div class="xg-flex xg-box">
        <div
          class="item"
          :class="{ active: current === 0 }"
          @click="tabChange(0)"
        >
          课程介绍
        </div>
        <div
          class="item"
          :class="{ active: current === 1 }"
          @click="tabChange(1)"
        >
          附件
        </div>
        <div
          class="item"
          :class="{ active: current === 2 }"
          @click="tabChange(2)"
        >
          评论
        </div>
      </div>
    </div>
    <div class="bottom-body">
      <div class="xg-box">
        <div class="swiper-and-recommended">
          <div class="swiper-box">
            <swiper
              class="bottom-swiper"
              ref="boomtSwiper"
              :options="swiperOption2"
              @slideChange="swiperChange"
            >
              <!-- 课程介绍 -->
              <swiper-slide>
                <div
                  class="introduce"
                  v-html="courseInfo.intro"
                  v-if="courseInfo.intro"
                ></div>
                <el-empty
                  description="暂无课程简介"
                  v-if="!courseInfo.intro"
                ></el-empty>
              </swiper-slide>
              <!-- 附件 -->
              <swiper-slide>
                <div class="attachment" v-if="courseInfo.is_annex">
                  <template
                    v-for="(item, index) in curdata"
                    v-if="item.annex.length !== 0"
                  >
                    <div
                      class="attachment-item"
                      v-for="(annex, i) in item.annex"
                    >
                      <div class="text">{{ annex.name }}</div>
                      <a
                        :href="annex.link"
                        target="_blank"
                        v-if="courseInfo.purchase"
                      >
                        <div class="download">
                          <i class="el-icon-download"></i>
                          立即下载
                        </div>
                      </a>
                      <a href="javascript:;" style="color: #72767b" v-else>
                        <div class="download">
                          <i class="el-icon-download"></i>
                          购买后下载
                        </div>
                      </a>
                    </div>
                  </template>
                </div>

                <!-- 无数据时，展示 -->
                <el-empty
                  description="暂无课程附件"
                  v-if="!courseInfo.is_annex"
                ></el-empty>
              </swiper-slide>
              <!-- 评论 -->
              <swiper-slide>
                <div class="comments">
                  <comment></comment>
                </div>
              </swiper-slide>
            </swiper>
          </div>
          <!-- 新闻 -->
          <div class="recommended">
            <!-- 讲师栏目 start -->
            <div class="lecturer" v-if="courseInfo.suggest_user">
              <div class="text1">授课老师</div>

              <div class="bottom">
                <div class="avatar">
                  <router-link
                    tag="a"
                    target="_blank"
                    :to="{
                      name: 'userfans',
                      params: { id: courseInfo.user_id },
                    }"
                  >
                    <div class="img-box">
                      <img :src="courseInfo.suggest_user.avatar" alt="" />
                    </div>
                  </router-link>

                  <i
                    class="el-icon-plus fansicon"
                    v-if="courseInfo.nofans"
                    @click.stop="userGuanzhu()"
                  ></i>
                </div>
                <router-link
                  tag="a"
                  target="_blank"
                  :to="{ name: 'userfans', params: { id: courseInfo.user_id } }"
                >
                  <div class="info">
                    <div class="xg-line-1 name">
                      {{ courseInfo.suggest_user.name }}
                    </div>
                    <div class="xg-line-1 desc">
                      {{ courseInfo.suggest_user.sign }}
                    </div>
                  </div>
                </router-link>
              </div>

              <div
                v-if="
                  !courseInfo.nofans && this.courseInfo.user_id != userinfo.id
                "
              >
                <div class="desc ysfans" @click="userGuanzhu(2)">已关注</div>
              </div>
            </div>

            <!-- 讲师栏目 end -->
            <swiper class="recommended-top" :options="recommendedSwiperOption">
              <swiper-slide v-for="item of swiperList" :key="item.id">
                <router-link
                  tag="div"
                  class="recommended-item"
                  :to="{ name: 'newsdetail', params: { id: item.id } }"
                >
                  <div class="xg-line-2 text1">
                    {{ item.title }}
                  </div>
                  <div class="xg-line-2 text2">
                    {{ item.content }}
                  </div>
                </router-link>
              </swiper-slide>

              <div class="swiper-pagination" slot="pagination"></div>
            </swiper>

            <div class="lecturer" v-if="teachers.length > 0">
              <div class="text teachers-title">推荐老师</div>
              <div
                class="bottom"
                v-for="item in teachers"
                :key="item.id"
                
              >
                <div class="avatar" style="border-radius: 50px">
                  <router-link
                    tag="a"
                    target="_blank"
                    :to="{
                      name: 'userfans',
                      params: { id: item.id },
                    }"
                  >
                    <div  style="border-radius: 50px">
                      <img
                        :src="item.avatar"
                        alt=""
                        style="border-radius: 50px"
                      />
                    </div>
                  </router-link>

                  <i
                    class="el-icon-plus fansicon"
                    v-if="item.nofans"
                    @click.stop="itemUserGuanzhu(item)"
                  ></i>
                </div>

                <router-link
                  tag="a"
                  target="_blank"
                  :to="{ name: 'userfans', params: { id: item.id } }"
                >
                  <div class="info">
                    <div class="xg-line-1 name">
                      {{ item.name }}
                    </div>
                    <div class="xg-line-1 desc">
                      {{ item.sign }}
                    </div>
                  </div>
                </router-link>
              </div>
            </div>
            <div class="recommended-bottom">
              <div class="title">推荐课程</div>
              <div class="list">
                <router-link
                  :to="{ name: 'coursepage', params: { id: val.id } }"
                  class="list-item"
                  v-for="(val, index) in pervNext"
                  :key="index"
                >
                  <div class="xg-image image">
                    <img :src="val.images" :alt="val.name" :title="val.name" />
                  </div>
                  <div class="info">
                    <div class="xg-line-1 text1">
                      {{ val.name }}
                    </div>
                    <div class="xg-line-2 text2">
                      {{ val.explain }}
                    </div>
                  </div>
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Aliplayer from "../../components/curriculum/Aliplayer.vue";
import comment from "./comment";
import watermark from "./watermark";
import { isWeiXin } from "../../utils/common";

export default {
  inject: ["reload"],
  components: {
    "ali-player": Aliplayer,
    comment,
    watermark,
  },
  data() {
    return {
      ceshi: JSON.stringify({
        HD: "//player.alicdn.com/resource/player/qupai.mp4",
        SD: "//player.alicdn.com/video/editor.mp4",
      }),
      teachers: [],
      userinfo: null,
      pagesize: 7,
      tabPosition: "right",
      activeName: "first", //介绍、评论
      num: 1, //当前显示的选项
      courseInfo: {
        collect: 1,
        purchase: 1,
      },
      activeNames: 0,
      curdata: [],
      taskData: {},
      current: 0,

      // swiper 初始化配置
      swiperOption2: {
        spaceBetween: 20, // swiper-solid 间距
        autoHeight: true, //高度随内容变化
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
        watchSlidesProgress: true,
        watchSlidesVisibility: true,
        allowTouchMove: false, // 禁止滑动
        preventClicks: false, //默认true
      },
      // swiper 初始化配置
      recommendedSwiperOption: {
        spaceBetween: 20, // swiper-solid 间距
        autoHeight: true, //高度随内容变化
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
        watchSlidesProgress: true,
        watchSlidesVisibility: true,
        autoplay: {
          delay: 5000,
          disableOnInteraction: false,
        },
        // allowTouchMove: false, // 禁止滑动
        preventClicks: false, //默认true
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
        },
      },

      //三张轮播，使用变量循环
      swiperList: [],

      // 上一课程id
      prevId: 0,
      // 下一课程id
      nextId: 0,
      // 上、下课程
      pervNext: [],
    };
  },
  beforeMount() {
    this.getCourseDetail();
    this.getNews();
    this.gePrevNext();
  },
  mounted() {
    // 页面刷新监听,销毁播放器
    let _this = this;
    window.addEventListener("beforeunload", function (e) {
      if (_this.$refs.player) {
        localStorage.setItem("video_volume", _this.$refs.player.getVolume());
        localStorage.setItem(
          "task_watch_" + _this.taskData.id,
          _this.$refs.player.getCurrentTime()
        );
        _this.$refs.player.dispose();
      }
    });

    if (!this.$refs.player) {
      this.reload();
    }

    if (window.innerWidth <= 900) {
      if (!isWeiXin()) {
        this.$message.warning({
          showClose: true,
          message: "请前往PC端观看视频",
        });
      }
    }
  },
  // 离开页面销毁播放器
  destroyed() {
    if (this.$refs.player) {
      localStorage.setItem("video_volume", this.$refs.player.getVolume());
      localStorage.setItem(
        "task_watch_" + this.taskData.id,
        this.$refs.player.getCurrentTime()
      );
      this.$refs.player.dispose();
    }
  },
  methods: {
    gofans(uid) {
      this.$router.push("/userfans/" + uid);
    },
    itemUserGuanzhu(item){
  this.$axios
        .get(
          this.url +
            "/api/userGuanzhu/" +
            item.id +
            "?type=1"
        )
        .then((res) => {
          console.log(res, "res--------");
          if (res.data.code !== 200) {
            this.$message.error(res.data.msg);
          } else {
            item.nofans = false;
            this.$message.success(res.data.msg);
          }
        })
        .catch((err) => {});
    },
    userGuanzhu(type = 1) {
      this.$axios
        .get(
          this.url +
            "/api/userGuanzhu/" +
            this.courseInfo.user_id +
            "?type=" +
            type
        )
        .then((res) => {
          console.log(res, "res--------");
          if (res.data.code !== 200) {
            this.$message.error(res.data.msg);
          } else {
            this.courseInfo.nofans = !this.courseInfo.nofans;
            this.$message.success(res.data.msg);
          }
        })
        .catch((err) => {});
    },
    go(item) {
      this.$router.push("/search?tag=" + item.id + "&type=course");
    },
    click(e) {
      // console.log(e)
    },
    tab(val, index) {
      let videoId = this.$route.query.task_id;
      if (videoId != val.id) {
        if (this.$refs.player) {
          let currentTime = this.$refs.player.getCurrentTime();
          let duration = this.$refs.player.getDuration();
          this.progress(this.taskData, currentTime, duration);

          localStorage.setItem("task_watch_" + this.taskData.id, currentTime);
          localStorage.setItem("video_volume", this.$refs.player.getVolume());
        }

        let id = val.id;
        if (val.type === "task") {
          this.num = id;
          this.activeNames = index;
          this.$router.push({
            name: "coursepage",
            params: { id: val.course_id },
            query: { task_id: val.id },
          });
        }
      }
      if (this.courseInfo.purchase === 1) {
        setTimeout(() => {
          this.$refs.player.play();
        }, 1000);
      }
    },
    //选项卡
    handleClick(tab, event) {
      // console.log(tab, event);
    },
    //跳转链接
    getUrl() {
      // product_type：1-课程，2-插件，3素材
      this.$router.push({
        path: "/payment",
        query: { product_id: this.courseInfo.id, product_type: 1 },
      });

      // this.$axios
      //     .post(this.url + "/api/create-order", {
      //       type: 1,
      //       id: this.courseInfo.id,
      //     })
      //     .then((res) => {
      //       if (res.data.code === 200) {
      //         this.$router.push({
      //           path: "/payment",
      //           query: { order_no: res.data.data.order_no },
      //         });
      //       } else if (res.data.code === 4001) {
      //         localStorage.removeItem("UserInfo");
      //         localStorage.removeItem("Authorization");
      //         this.$router.push({ name: "login" });
      //       } else {
      //         this.$message.warning({
      //           showClose: true,
      //           message: res.data.msg,
      //         });
      //       }
      //     })
      //     .catch((err) => {});
    },

    //
    freeCourse() {
      this.$axios
        .post(this.url + "/api/free-course", {
          type: 1,
          id: this.courseInfo.id,
        })
        .then((res) => {
          if (res.data.code === 200) {
            this.$nextTick(() => {
              this.courseInfo.purchase = 1;
            });

            this.$message.success({
              showClose: true,
              message: res.data.msg,
            });
          } else if (res.data.code === 4001) {
            localStorage.removeItem("UserInfo");
            localStorage.removeItem("Authorization");
            this.$router.push({ name: "login" });
          } else {
            this.$message.warning({
              showClose: true,
              message: res.data.msg,
            });
          }
        })
        .catch((err) => {});
    },

    createCourse() {
      this.$axios
        .post(this.url + "/api/create-order", {
          type: 1,
          id: this.courseInfo.id,
        })
        .then((res) => {
          if (res.data.code === 200) {
            this.$router.push({
              path: "/payment",
              query: { order_no: res.data.data.order_no },
            });
          } else if (res.data.code === 4001) {
            localStorage.removeItem("UserInfo");
            localStorage.removeItem("Authorization");
            this.$router.push({ name: "login" });
          } else {
            this.$message.warning({
              showClose: true,
              message: res.data.msg,
            });
          }
        })
        .catch((err) => {});
    },

    // handle video
    onReady(taskData) {
      let video_volume = localStorage.getItem("video_volume");
      video_volume = video_volume > 0 ? video_volume : 0.3;
      this.$refs.player.setVolume(video_volume);

      let watch = taskData.watch;
      let ls_watch = localStorage.getItem("task_watch_" + taskData.id);
      if (watch > 0 || ls_watch > 0) {
        this.$refs.player.seek(ls_watch ? ls_watch : watch);
      }
    },
    onPlay(item) {
      if (item.id) {
        let user = localStorage.getItem("UserInfo");
        let key = this.$route.params.id + "-" + item.id + "-" + user.id;
        if (!localStorage.getItem(key)) {
          localStorage.setItem(key, true);
          // 播放次数
          this.viewNum(item.id);
        }
      }
    },
    onPause(item) {
      if (this.$refs.player) {
        let currentTime = this.$refs.player.getCurrentTime();
        let duration = this.$refs.player.getDuration();
        this.progress(item, currentTime, duration);

        localStorage.setItem("task_watch_" + item.id, currentTime);
        localStorage.setItem("video_volume", this.$refs.player.getVolume());
      }
    },
    // 课程信息
    getCourseDetail() {
      var id = this.$route.params.id;
      var task_id = this.$route.query.task_id;

      if (task_id == "" || task_id == null || task_id == undefined) {
        task_id = 0;
      }

      if (id) {
        this.$axios
          .get(this.url + "/api/courses/" + id, {
            params: {
              task_id: task_id,
            },
          })
          .then((res) => {
            if (res.data.code == 200) {
              this.courseInfo = res.data.data.course;
              this.curdata = res.data.data.details;
              this.userinfo = res.data.data.user;
              this.teachers = res.data.data.teachers;

              if (
                res.data.data.task != null &&
                Object.keys(res.data.data.task).length !== 0
              ) {
                // if(window.innerWidth <=900){
                //   res.data.data.task.link = '';
                // }
                this.taskData = res.data.data.task;
                this.$nextTick(() => {
                  this.num = this.taskData.id;
                  this.activeNames = this.taskData.cpt_id;
                });
              }

              if (Object.keys(this.curdata).length === 0) {
                this.$message({
                  showClose: true,
                  message: "课件被外星人劫走了，正在追踪，请耐心等待！",
                });
              }
            } else {
              this.$message.error({
                showClose: true,
                message: res.data.msg,
              });
              this.$router.go(-1);
            }
          })
          .catch((err) => {});
      }
    },
    // 新闻
    getNews() {
      this.$axios
        .get(this.url + "/api/news/", {})
        .then((res) => {
          this.swiperList = res.data.data.news;
        })
        .catch((err) => {});
    },
    gePrevNext() {
      var id = this.$route.params.id;
      if (id) {
        this.$axios
          .get(this.url + "/api/courses/" + id + "/prevnext", {})
          .then((res) => {
            this.pervNext = res.data.data.courses;
            this.prevId = res.data.data.ids.prev_id;
            this.nextId = res.data.data.ids.next_id;
          })
          .catch((err) => {});
      }
    },
    // 播放次数
    viewNum(id) {
      this.$axios
        .post(this.url + "/api/courses/view", {
          course_detail_id: id,
        })
        .then((res) => {})
        .catch((err) => {});
    },

    collect(courseId) {
      if (courseId) {
        this.$axios
          .post(this.url + "/api/courses/collect", {
            course_id: courseId,
          })
          .then((res) => {
            if (res.data.code === 200) {
              this.courseInfo.collect = 1;
              this.$message.success({
                showClose: true,
                message: res.data.msg,
              });
            } else if (res.data.code === 4001) {
              localStorage.removeItem("UserInfo");
              localStorage.removeItem("Authorization");
              this.$router.push({ name: "login" });
            } else {
              this.$message.warning({
                showClose: true,
                message: res.data.msg,
              });
            }
          })
          .catch((err) => {});
      }
    },
    /**
     * 分类选项点击事件
     */
    tabChange(index) {
      this.$refs.boomtSwiper.swiper.slideTo(index);
    },
    /**
     * swiper切换事件
     */
    swiperChange(e) {
      this.current = this.$refs.boomtSwiper.swiper.activeIndex;
    },

    // 进度
    progress(item, currentTime, duration) {
      let courseId = this.courseInfo.id;
      let user = JSON.parse(localStorage.getItem("UserInfo"));
      if (courseId && user && Object.keys(user).length !== 0) {
        this.$axios
          .post(this.url + "/api/courses/progress", {
            course_id: courseId,
            course_detail_id: item.id,
            time: currentTime,
            duration: duration,
          })
          .then((res) => {})
          .catch((err) => {});
      }
    },

    perv() {
      if (this.prevId) {
        this.$router.push({ name: "coursepage", params: { id: this.prevId } });
      } else {
        this.$message({
          showClose: true,
          message: "真奇怪，隐身了？",
        });
      }
    },

    next() {
      if (this.nextId) {
        this.$router.push({ name: "coursepage", params: { id: this.nextId } });
      } else {
        this.$message({
          showClose: true,
          message: "真奇怪，隐身了？",
        });
      }
    },
  },
  watch: {
    $route: {
      handler(route) {
        this.activeName = "first";
        this.taskData = [];
        this.reload();
        this.getCourseDetail();
        this.gePrevNext();
      },
    },
  },
};
</script>

<style scoped lang="scss">
#video {
  position: relative;
  height: 760px;
}
.top {
  background: #2a323b;
  padding-top: 46px;
  padding-bottom: 15px;
  padding-left: 40px;
  padding-right: 40px;
}
.xg-box {
  max-width: 1670px;
}

// 视频框架
.topvideo {
  display: flex;
  align-items: stretch;
}
// 视频主体
.main-video {
  width: 82%;
  flex-shrink: 0;
}
// 视频目录
.video-directory {
  width: 18%;
  background: #242b33;
  padding: 24px;
  position: relative;
  height: 760px;
  overflow-y: auto;
  &::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 4px; /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }
  &::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    background-color: #000;
  }
  &::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #ccc;
    border-radius: 10px;
  }
  /deep/ .el-collapse {
    border-top: 0;
    border-bottom: 0;
    .el-collapse-item__header,
    .el-collapse-item__wrap {
      background: none;
    }
    .el-collapse-item__wrap {
      border-bottom: 0;
    }
    .el-collapse-item__header {
      font-size: 15px;
      line-height: 1.2;
      color: #f9f7f8;
      height: auto;
      border-bottom: 0;
      text-align: left;
      span {
        min-width: 0;
        flex-grow: 1;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
    .el-collapse-item__content {
      padding-bottom: 0;
    }
    .el-collapse-item {
      margin-bottom: 15px;
    }
    .el-collapse-item__arrow {
      color: #77828f;
    }
  }
  ul {
    text-align: left;
    overflow: hidden;
    padding-top: 10px;
    padding-left: 15px;
    li {
      font-size: 13px;
      color: #77828f;
      line-height: 30px;
      //white-space: nowrap;
      //overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
      &:hover,
      &.active {
        color: #00be06;
      }
      &.active {
        &:before {
          content: "";
          display: inline-block;
          vertical-align: middle;
          background: url(../../assets/img/play.png) no-repeat center;
          background-size: 100%;
          width: 15px;
          height: 15px;
          margin-right: 10px;
        }
      }
    }
  }

  .page {
    position: absolute;
    left: 42%;
  }
}

//
.bottom-head {
  padding-left: 40px;
  padding-right: 40px;
  background: #f4f4f4;
  .item {
    color: #000;
    line-height: 1.2;
    min-width: 120px;
    text-align: center;
    padding-bottom: 23px;
    padding-top: 23px;
    position: relative;
    font-size: 16px;
    cursor: pointer;
    &.active,
    &:hover {
      color: #0d6efd;
      &:after {
        width: 100%;
      }
    }
    &::after {
      content: "";
      display: block;
      width: 0;
      height: 2px;
      background: #0d6efd;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      transition: all 0.4s;
    }
  }
}
//
.bottom-body {
  background: #fff;
  padding-left: 40px;
  padding-right: 40px;
}
.swiper-and-recommended {
  display: flex;
  align-items: flex-start;
}
.swiper-box {
  // width: 82%;
  padding: 60px;
  padding-left: 20px;
  min-width: 0;
  flex-grow: 1;
}
.bottom-swiper {
  margin-left: 0;
  margin-right: auto;
}
// 讲师栏目
.lecturer {
  margin-bottom: 20px;
  text-align: left;
  padding: 8px 16px 16px;
  background-color: #f9f9f9;
  .teachers-title {
    font-size: 20px;
    color: #333;
    line-height: 24px;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebebeb;
    font-weight: 700;
  }
  .text1 {
    font-size: 16px;
    color: #333;
    line-height: 24px;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebebeb;
  }
  .bottom {
    margin-top: 16px;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  .avatar {
    flex-shrink: 0;
    width: 64px;
    height: 64px;
    margin-right: 12px;
    position: relative;
    .img-box {
      width: 64px;
      height: 64px;
      border-radius: 100%;
      overflow: hidden;
    }
  }
  .fansicon {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 23px;
    height: 23px;
    border: #000;
    background-color: #0d6efd;
    text-align: center;
    line-height: 20px;
    color: #ffffff;
    font-weight: 700;
    border-radius: 35px;
  }
  .info {
    min-width: 0;
    flex-grow: 1;
  }
  .name {
    font-size: 14px;
    color: #333;
    line-height: 20px;
  }
  .desc {
    margin-top: 12px;
    font-size: 12px;
    line-height: 1.2;
    color: #999;
  }

  .ysfans {
    margin-left: 5px;
    box-sizing: border-box;
    background-color: #dddddd;
    color: #222222 !important;
    display: inline-block;
    padding: 5px 10px;
    font-size: 12px;
    border-radius: 5px;
  }
}
//
.recommended {
  flex-shrink: 0;
  // width: 18%;
  width: 280px;
  padding-left: 20px;
  padding-top: 40px;
}
.recommended-top {
  margin-bottom: 20px;

  /deep/ .swiper-pagination {
    bottom: 20px;
    font-size: 0;
    .swiper-pagination-bullet {
      width: 10px;
      height: 10px;
      border-radius: 100%;
      background: #ffff;
    }
  }
  .recommended-item {
    display: block;
    background: #0d6efd;
    padding: 35px 30px 40px;
    .text1 {
      font-size: 16px;
      color: #fff;
      line-height: 1.8;
      text-align: justify;
      margin-bottom: 10px;
    }
    .text2 {
      font-size: 12px;
      color: #b0d0ff;
      line-height: 1.8;
      text-align: justify;
    }
  }
}
.recommended-bottom {
  .title {
    color: #000;
    line-height: 1.2;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
  }
  .list {
  }
  .list-item {
    margin-bottom: 10px;
  }
  text-align: left;
  .image {
    width: 100%;
  }
  .info {
  }
  .text1 {
    padding-top: 10px;
    padding-bottom: 10px;
    color: #000;
    font-size: 14px;
    line-height: 1.2;
  }
  .text2 {
    font-size: 14px;
    color: #8a8a8b;
    line-height: 1.6;
    text-align: justify;
  }
}

//评论
.comments {
  .comments-left {
    background: white;
    min-height: 600px;
    padding-bottom: 50px;
    .leftlist {
      padding: 0;
      margin: 10px;
      text-align: left;
      li {
        padding: 14px 20px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        p {
          margin: 0;
        }
        a {
          margin: 0;
          padding: 0;
          width: 20px;
          height: 20px;
          color: #e6a23c;
        }
      }
      li:hover {
        background: rgba(230, 162, 60, 0.1);
      }
    }
  }
}

// 附件
.attachment {
  padding-left: 150px;
  padding-right: 170px;
  .attachment-item {
    display: flex;
    align-items: center;
    padding-top: 25px;
    padding-bottom: 25px;
    border-bottom: 1px solid #dedede;
  }
  .text {
    font-size: 14px;
    color: #909399;
    min-width: 0;
    flex-grow: 1;
  }
  .download {
    font-size: 14px;
    color: #4a5764;
    line-height: 1.2;
    cursor: pointer;
    i {
      margin-right: 5px;
      font-size: 24px;
      color: #0d6efd;
    }
  }
}

//滚动
.contbox {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  // 设置容器最大宽度为200
  width: 350px;
  font-size: 14px;
  text-align: left;
  color: #fff;
  position: absolute;
  top: 10px;
  left: 20px;
}

//导航
.navbar-expand-lg .navbar-collapse {
  display: flex;
  justify-content: space-between;
}
.navbar {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  width: 100%;
  /*position: relative;*/
  /*top: 0;*/
  z-index: 99;
  position: absolute;
  top: 0;
  left: 0;
}
button {
  outline: none;
}
.bg-info {
  /*background-color: rgba(0,0,0,0) !important;*/
  background-color: rgb(17, 19, 42) !important;
}
.navbar-dark .navbar-brand,
.navbar-dark .navbar-nav .nav-link {
  color: white;
}
.btn-success {
  color: white !important;
  background: none !important;
  border: none !important;
  margin-top: 5px;
}

@media only screen and (max-width: 900px) {
  .navbar-expand-lg .navbar-collapse {
    display: block;
  }
  .navbar-dark .navbar-toggler {
    color: rgba(0, 0, 0, 0.5);
    border-color: rgba(0, 0, 0, 0.5);
  }
  /deep/.navbar-dark .navbar-toggler-icon {
    background-image: url("../../assets/img/nav.png");
  }
}

//小轮播
.swiper-container {
  text-align: left;
  img {
    width: 100%;
  }
  .title {
    font-size: 15px;
    font-weight: 600;
    color: #333333;
  }
  .content {
    font-size: 14px;
    color: #666;
  }
}
.introduce {
  font-size: 14px;
  color: #555555;

  p {
    line-height: 26px;
    margin-top: 20px;
    text-align: left;
  }
}
.new-right {
  margin-top: 20px;
}
//卡片
.time {
  font-size: 13px;
  color: #999;
}

.bottom {
  margin-top: 13px;
  line-height: 12px;
}

.button {
  padding: 0;
  float: right;
}

.image {
  width: 100%;
  display: block;
}

.el-carousel__item h3 {
  color: #475669;
  font-size: 18px;
  opacity: 0.75;
  line-height: 300px;
  margin: 0;
}

.el-carousel__item:nth-child(2n) {
  background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
  background-color: #d3dce6;
}

.active {
  color: #e6a23c;
}

.video-info {
  display: flex;
  align-items: center;
  position: relative;
  margin-top: 14px;
  .video-name {
    font-size: 30px;
    color: #fff;
    line-height: 1.2;
  }
  .right {
    position: absolute;
    right: 40px;
    top: 5px;
    display: flex;
    button {
      padding: 6px 30px;
      background: #e6a23c;
      outline: none;
      border: none;
      color: white;
      margin-right: 0;
    }
    span {
      i {
        font-size: 30px;
        cursor: pointer;
      }
      margin-left: 120px;
    }
  }
  .xg-top {
    margin-bottom: 17px;
  }
  .xg-left {
    width: 82%;
  }
  // 视频名称
  .video-name {
    font-size: 30px;
    color: #fff;
    line-height: 1.2;
    flex-grow: 1;
    min-width: 0;
    text-align: left;
  }
  // 学习人数
  .study-count {
    font-size: 14px;
    color: #77828f;
    margin-right: 20px;
    strong {
      color: #00be06;
    }
  }
  // 收藏
  .collection {
    width: 40px;
    height: 40px;
    display: block;
    background: url("../../assets/img/collection.png") no-repeat center 0;
    background-size: 100% auto;
    cursor: pointer;
    flex-shrink: 0;
    &:hover {
      background-position-y: -41px;
    }
  }
  // 加入学习
  .learning {
    &:before {
      background-image: url("../../assets/img/play02.png");
    }
  }
  // 购买本课
  .buy {
    &:before {
      background-image: url("../../assets/img/learning.png");
    }
  }
  .learning,
  .buy {
    width: 142px;
    border-radius: 100px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #77828f;
    font-size: 14px;
    color: #fff;
    margin-left: 22px;
    flex-shrink: 0;
    cursor: pointer;
    &:hover {
      background: #0d6efd;
    }
    &::before {
      content: "";
      display: block;
      background-repeat: no-repeat;
      background-size: 100%;
      margin-right: 6px;
      flex-shrink: 0;
      width: 20px;
      height: 20px;
    }
  }
  //
  .xg-bottom {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    line-height: 1.2;
    color: #828e9b;
    // 播放量
    .count {
      &::before {
        content: "";
        display: inline-block;
        vertical-align: middle;
        background: url("../../assets/img/play03.png");
      }
    }
    // 上一个下一个
    .goto {
      font-size: 24px;
      span:hover {
        color: #0d6efd;
        cursor: pointer;
      }
      .perv {
        padding-right: 20px;
      }
    }
  }
  // 广告图
  .xg-ad {
    width: 18%;
    padding-left: 20px;
    img {
      width: 100%;
    }
  }
}
.tag-box {
  text-align: left;
  .tag-item {
    margin: 5px 20px 5px 0;
    cursor: pointer;
  }
}

@media screen and (max-width: 1460px) {
  .recommended-top .recommended-item {
    padding: 20px 15px 30px;
  }
  .swiper-box {
    padding: 40px;
  }
  .attachment {
    padding-left: 0;
    padding-right: 0;
  }
  .recommended-top .recommended-item .text1 {
    font-size: 13px;
    line-height: 1.4;
  }
  .recommended-top .recommended-item .text2 {
    line-height: 1.4;
  }
  .recommended-top /deep/ .swiper-pagination {
    bottom: 10px;
  }
  .recommended-bottom .title {
    margin-bottom: 15px;
  }
  .video-info .video-name {
    font-size: 24px;
  }
}
@media screen and (max-width: 751px) {
  #video {
    height: 300px;
  }
  .top {
    padding: 0;
  }
  .topvideo {
    flex-wrap: wrap;
  }
  .bottom-body {
    padding-left: 0;
    padding-right: 0;
  }
  .main-video {
    width: 100%;
  }
  .video-directory {
    width: 100%;
    padding: 15px;
    height: auto;
  }
  .video-info {
    flex-wrap: wrap;
  }
  .video-info .xg-left {
    width: 100%;
  }
  .video-info .xg-top {
    flex-wrap: wrap;
  }
  .video-info .video-name {
    width: 100%;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 20px;
  }
  .video-info .collection {
    width: 30px;
    height: 30px;
  }
  .video-info .learning,
  .video-info .buy {
    width: auto;
    height: auto;
    line-height: inherit;
    padding: 6px 12px;
    font-size: 12px;
    margin-left: 15px;
    &::before {
      width: 10px;
      height: 10px;
    }
  }
  .video-info .xg-ad {
    display: none;
  }
  .video-info .xg-bottom {
    flex-wrap: wrap;
  }
  .video-info .xg-bottom .text {
    flex-wrap: wrap;
    margin-bottom: 10px;
  }
  .video-info .xg-bottom .goto {
    width: 100%;
    justify-content: space-between;
  }
  .bottom-head {
    padding-left: 0px;
    padding-right: 0px;
  }
  .bottom-head > .xg-box {
    padding: 0;
    max-width: 100%;
    flex-wrap: wrap;
    display: flex;
  }
  .bottom-head .item {
    width: 33.333%;
    padding-top: 15px;
    padding-bottom: 15px;
    font-size: 14px;
  }
  .xg-box {
    max-width: 100%;
  }
  .swiper-and-recommended {
    flex-wrap: wrap;
  }
  .swiper-box {
    width: 100%;
    padding: 0;
  }
  .recommended {
    padding: 0;
    width: 100%;
    order: -2;
    .recommended-top,
    .recommended-bottom {
      display: none;
    }
  }
  .attachment .attachment-item {
    padding-top: 10px;
    padding-bottom: 10px;
  }
  .attachment .download,
  .attachment .text {
    font-size: 12px;
  }
  .attachment .download i {
    font-size: 20px;
  }
  .contbox {
    width: 100%;
  }
  .preview-vod-component .preview-component-layer .preview-custom {
    font-size: 14px !important;
  }
}
</style>

<template>
  <div class="xg-ny xg-ny-course" v-loading="loading">
    <div class="xg-box">
      <!-- 内页 标题 -->
      <div class="xg-ny-title" v-if="parentName && parentName != '全部'">
        <span style="color: #72767b">课程 </span> / {{ this.parentName }}
      </div>
      <div class="xg-ny-title" v-else>课程</div>
      <!-- 分类选项  -->
      <el-tabs v-model="activeName" @tab-click="tabChange" class="xg-tabs">
        <el-tab-pane
          :label="item.name"
          :name="item.id + ''"
          v-for="(item, index) in cates"
          :key="item.id"
        ></el-tab-pane>
      </el-tabs>

      <el-tabs
        v-if="
          cates &&
          cates[childindex] &&
          cates[childindex].children_category.length > 0
        "
        v-model="childactiveName"
        @tab-click="tabChange2"
        class="xg-tabs"
      >
        <el-tab-pane :label="'全部'" :name="'0'"></el-tab-pane>
        <el-tab-pane
          :label="item.name"
          :name="item.id + ''"
          v-for="(item, index) in cates[childindex].children_category"
          :key="item.id"
        ></el-tab-pane>
      </el-tabs>
      <el-tabs
        v-if="cate3 && cate3.length > 0"
        v-model="childactiveName3"
        @tab-click="tabChange3"
        class="xg-tabs"
      >
        <el-tab-pane :label="'全部'" :name="'0'"></el-tab-pane>
        <el-tab-pane
          :label="item.name"
          :name="item.id + ''"
          v-for="(item, index) in cate3"
          :key="item.id"
        ></el-tab-pane>
      </el-tabs>
      <el-tabs
        v-if="cate4 && cate4.length > 0"
        v-model="childactiveName4"
        @tab-click="tabChange4"
        class="xg-tabs"
      >
        <el-tab-pane :label="'全部'" :name="'0'"></el-tab-pane>
        <el-tab-pane
          :label="item.name"
          :name="item.id + ''"
          v-for="(item, index) in cate4"
          :key="item.id"
        ></el-tab-pane>
      </el-tabs>
      <br />
      <el-tabs v-model="listorder" @tab-click="tabChangeOrder" class="xg-tabs">
        <el-tab-pane :label="'最新'" :name="'0'"></el-tab-pane>
        <el-tab-pane label="最热" :name="'1'"></el-tab-pane>
      </el-tabs>

      <br />
      <br />
      <!-- 对应分类的数据 -->
      <div class="xg-row-4 itembox">
        <router-link
          class="xg-col item"
          tag="a"
          target="_blank"
          :to="{ name: 'coursepage', params: { id: course.id } }"
          v-for="(course, index) in items"
          :key="index"
        >
          <div class="image">
            <!-- <img
              :src="course.images"
              alt=""
            /> -->
            <div
              class="bg-image"
              :style="`background-image:url(${course.images})`"
            ></div>
            <div class="info">
              {{ course.name }}
            </div>
          </div>
          <div class="xg-line-2 name">
            {{ course.name }}
          </div>
          <!-- 类型（免费，收费） -->
          <div class="bottom">
            <div
              class="type"
              style="color: #0c63e4"
              v-if="course.purchase == 1"
            >
              已加入
            </div>
            <div class="type type1" v-else-if="course.price > 0">
              <span v-if="course.has_discount && course.discount_price">
                {{ course.discount_price }} 光子
                <span style="text-decoration: line-through; color: #999; margin-left: 5px; font-size: 12px;">
                  {{ course.price }} 光子
                </span>
              </span>
              <span v-else>
                {{ course.price }} 光子
              </span>
            </div>
            <div class="type" v-else>免费</div>
            <div class="last-price" v-if="course.market_price != course.price">
              {{ course.market_price }} 光子
            </div>
          </div>
        </router-link>
      </div>
      <!-- 分页 -->
      <el-pagination
        class="xg-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="offset"
        :page-size="limit"
        layout="prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: "course",
  components: {},
  data() {
    return {
      listorder: '0',
      limit: 12,
      offset: 0,
      total: 0,
      loading: false,
      items: [],
      cates: [],
      cate2: [],
      cate3: [],
      cate4: [],
      // 选项卡位置
      activeName: "0",
      parentName: "",
      childactiveName: "0",
      childindex: 0,
      childactiveName3: "0",
      childindex2: 0,
      childindex3: 0,
      childactiveName4: "0",
      childindex4: 0,
      cateIndexArr: [],
    };
  },
  created() {
    this.parentName = this.$route.query.parent_name;
    this.getCates();
    this.setTagIndex();
    this.getData();
  },
  mounted() {},
  methods: {
    handleSizeChange(val) {
      this.limit = val;
    },
    handleCurrentChange(val) {
      let page = val - 1;
      this.getData(this.limit, page);
    },

    getData(limit = 12, offset = 0) {
      let cate_id = this.$route.query.id;
      this.loading = true;
      this.$axios
        .get(this.url + "/api/courses", {
          params: {
            sort: this.listorder,
            limit: limit,
            offset: offset,
            cate_id: cate_id,
            level: this.$route.query.level,
          },
        })
        .then((res) => {
          if (res.data.code === 200) {
            this.items = res.data.data.courses;
            this.total = res.data.data.total;
          } else {
          }
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
        });
    },

    getCates() {
      this.loading = true;
      let cateid = this.$route.query.id;
      let parentid = this.$route.query.parent_id;
      this.$axios
        .get(this.url + "/api/courses/cate", {
          params: {
            // parent_id: parentid
          },
        })
        .then((res) => {
          if (res.data.code === 200) {
            this.cates = res.data.data;
            this.setTagIndex();
            var cateindex = 0;
          } else {
          }
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
        });
    },

    handleClick(tab, event) {},

    setTagIndex() {
      let cateid = this.$route.query.id;
      let parentid = this.$route.query.parent_id;
      this.parentName = this.$route.query.parent_name;
      var cateindex = 0;
      var childcateindex = 0;
      var cateindex3 = 0;
      this.childactiveName = "0";
      this.$nextTick(function () {
        if (this.cates && this.cates.length > 0) {
          var cateStr = this.$route.query.cateStr;
          if (cateStr) {
            var cateArr = cateStr.split(",");
            console.log(cateArr, "arr----");
          } else {
            return;
          }
          if (cateArr[0] && cateArr[0] != 0) {
            let currCate = this.cates.find((item, index) => {
              cateindex = index;
              console.log(
                item.id,
                item.name,
                cateArr[0],
                index,
                "currCate-----"
              );
              return item.id == cateArr[0];
            });
            console.log(currCate, "currCate-----");
            this.activeName = currCate.id + "";
          } else {
            this.activeName = "0";
          }
          this.childindex = cateindex;
          if (cateArr[1] && cateArr[1] != 0) {
            let currCate2 = this.cates[cateindex].children_category.find(
              (item, index) => {
                childcateindex = index;
                return item.id == cateArr[1];
              }
            );
            this.childactiveName = currCate2.id + "";
            console.log(currCate2, "currCate2-----------");
            this.cate3 = [...currCate2.children_category];
            console.log(currCate2, "currCate2-======");
          } else {
            this.childactiveName = "0";
            this.cate3 = [];
          }
          var childcateindex3 = 0;
          if (cateArr[2] && cateArr[2] != 0) {
            let currCate3 = this.cate3.find((item, index) => {
              childcateindex3 = index;
              return item.id == cateArr[2];
            });
            this.childcateindex3 = childcateindex3;
            this.childactiveName3 = currCate3.id + "";
            this.cate4 = [...currCate3.children_category];
            console.log(currCate3, "currCate3-======");
          } else {
            this.childactiveName3 = "0";
            this.cate4 = [];
          }

          if (cateArr[3] && cateArr[3] != 0) {
            let currCate4 = this.cate4.find((item, index) => {
              return item.id == cateArr[3];
            });
            this.childactiveName4 = currCate4.id + "";
          } else {
            this.childactiveName4 = "0";
          }
        }
      });
    },
    tabChangeOrder(e) {
      this.listorder = e.name;
      this.getData();
    },
    tabChange2(e) {
      this.childindex3 = 0;
      this.childindex4 = 0;
      this.childactiveName3 = "0";
      this.childactiveName4 = "0";
      let cateid = this.$route.query.id;
      var that = this;
      var index2 = 0;
      var currCate = null;
      if (cateid != e.name) {
        if (e.name == 0) {
          that.childindex2 = 0;
        } else {
          currCate = this.cates[this.childindex].children_category.find(
            (item, index) => {
              index2 = index;
              return item.id == e.name;
            }
          );
          that.childindex2 = index2;
        }
        console.log("index----", this.childindex2);
        this.cateIndexArr = [this.cates[this.childindex].id, e.name];
        this.cate3 =
          this.cates[this.childindex].children_category[
            this.childindex2
          ].children_category;
        console.log(this.cate3, "cate333-----");
        this.$router.push({
          name: "course",
          query: {
            id: !currCate ? this.cates[this.childindex].id : e.name,
            level: currCate ? currCate.level : "2",
            parent_name: this.cates[this.childindex].name,
            parent_id: this.cates[this.childindex].id,
            tab: 1,
            cateStr: this.cateIndexArr.join(","),
          },
        });
      }
    },

    tabChangechild(e) {
      let cateid = this.$route.query.id;
      // this.setTagIndex();
      if (cateid != e.name) {
        let currCate = this.cates[this.childindex].children_category.find(
          (item) => {
            return item.id == e.name;
          }
        );
        console.log(currCate, this.cates, this.childindex, e.name);
        this.$router.push({
          name: "course",
          query: {
            id: !currCate ? this.cates[this.childindex].id : e.name,
            level: currCate ? currCate.level : "2",
            parent_name: this.cates[this.childindex].name,
            parent_id: this.cates[this.childindex].id,
            tab: 1,
          },
        });
      }
    },

    tabChange3(e) {
      this.childindex4 = 0;
      this.childactiveName4 = "0";
      let cateid = this.$route.query.id;
      var childinde3 = 0;
      if (cateid != e.name) {
        let currCate = this.cate3.find((item, index) => {
          childinde3 = index;
          return item.id == e.name;
        });
        this.childindex3 = childinde3;
        var cateStr = this.$route.query.cateStr;
        var cateArr = [];
        if (cateStr) {
          cateArr = cateStr.split(",");
        }
        if (cateArr.length == 2) {
          cateArr.push(e.name);
        } else if (cateArr.length >= 3) {
          cateArr[2] = e.name;
        }
        this.cateIndexArr = [cateArr[0], cateArr[1], cateArr[2]];
        this.$router.push({
          name: "course",
          query: {
            id: !currCate
              ? this.cates[this.childindex].children_category[this.childindex2]
                  .id
              : e.name,
            level: currCate ? 4 : "3",
            parent_name:
              this.cates[this.childindex].children_category[this.childindex2]
                .name,
            parent_id:
              this.cates[this.childindex].children_category[this.childindex2]
                .id,
            tab: 1,
            cateStr: this.cateIndexArr.join(","),
          },
        });
      }
    },
    tabChange4(e) {
      let cateid = this.$route.query.id;
      if (cateid != e.name) {
        let currCate = this.cate4.find((item) => {
          return item.id == e.name;
        });
        var cateStr = this.$route.query.cateStr;
        var cateArr = [];
        if (cateStr) {
          cateArr = cateStr.split(",");
        }
        if (cateArr.length == 3) {
          cateArr.push(e.name);
        } else if (cateArr.length >= 4) {
          cateArr[3] = e.name;
        }
        this.cateIndexArr = [cateArr[0], cateArr[1], cateArr[2], cateArr[3]];
        console.log(this.cateIndexArr, "arr=============");
        this.$router.push({
          name: "course",
          query: {
            id: !currCate ? this.cate3[this.childindex3].id : e.name,
            level: currCate ? 5 : "4",
            parent_name: this.cate3[this.childindex3].name,
            parent_id: this.cate3[this.childindex3].id,
            tab: 1,
            cateStr: this.cateIndexArr.join(","),
          },
        });
      }
    },
    /**
     * 分类选项点击事件
     */
    tabChange(e) {
      this.childindex = e.index;
      let cateid = this.$route.query.id;
      this.childactiveName = "0";
      // this.setTagIndex();
      console.log(e.index, this.activeName, "e--------");
      if (cateid != e.name) {
        let currCate = this.cates.find((item) => {
          return item.id == e.name;
        });
        this.cateIndexArr = [e.name];
        console.log(this.cateIndexArr);
        this.$router.push({
          name: "course",
          query: {
            id: e.name,
            level: currCate.level,
            parent_name: currCate.parentName,
            // parent_id: this.$route.query.parent_id,
            tab: 1,
            cateStr: this.cateIndexArr.join(","),
          },
        });
      }
    },

    /**
     * swiper切换事件
     */
    swiperChange(e) {
      this.activeName = this.$refs.tabSwiper.swiper.activeIndex + "";
    },
  },
  watch: {
    $route: {
      handler(route) {
        this.setTagIndex();
        if (this.$route.query.tab != 1) {
          this.getCates();
        }

        this.getData();
      },
    },
  },
};
</script>


<style scoped lang="scss">
@import "./index.scss";
.xg-ny-course {
  .item {
    .bg-image {
      height: 180px;
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center;
    }
    .bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .last-price {
      font-size: 14px;
      color: #a9b2bb;
      text-decoration: line-through;
    }
  }
}
>>> .xg-tabs {
  .el-tabs__header {
    margin-bottom: 0 !important;
    .el-tabs__nav-wrap {
      margin-left: 0 !important;
      padding-left: 0 !important;
    }
  }
  .el-tabs__item {
    padding: 5px !important;
    background-color: rgba(255, 255, 255, 0) !important;
    &.is-active {
      color: #0d6efd !important;
    }
  }
}
</style>


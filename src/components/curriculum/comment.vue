<template>
  <div>
    <!-- 还未登录的样式 -->
      <div class="noinput" v-if="!user">
        <div class="text">登录后可评论</div>
        <div class="xg-btn" @click="goLogin()">登录</div>
      </div>
    <!-- 登录后的样式 -->
    <div @click="inputFocus" class="my-reply" v-else>
      <!--      <el-col :lg="2" :xs="4">-->
      <!--        <el-avatar class="header-img" :size="40" :src="user.avatar"></el-avatar>-->
      <!--      </el-col>-->
      <div
        tabindex="0"
        contenteditable="true"
        id="replyInput"
        spellcheck="false"
        placeholder="输入评论..."
        class="reply-input"
        @input="onDivInput($event)"
      ></div>
      <div class="reply-btn-box">
        <el-button class="reply-btn" size="medium" @click="sendComment" type="primary">发表</el-button>
      </div>
    </div>
    <div v-for="(item, i) in comments" :key="i" class="author-title">
      <div class="reply-father">
        <el-avatar class="header-img" :src="item.user.avatar"></el-avatar>
        <div class="author-info">
          <div class="author-name">
            {{ item.user.name || '匿名' }}
          </div>
          <div class="talk-box">
            {{ item.content }}<span class="reply-btn" @click="readyReply(i)">回复</span>
          </div>
          <div class="author-time">{{ item.created_at }}</div>
        </div>
      </div>
      <!-- 回复框 -->
      <div class="replay-card" v-if="label == i">
        <el-input placeholder="我来说几句" v-model="replyText"></el-input>
        <div class="xg-btn" @click="sendReply(item.id, i)">确定</div>
      </div>
      <!-- 回复对话 -->
      <div class="reply-childern">
        <div class="childern-item" v-for="(reply, index) in item.reply" :key="index">
          <el-avatar class="header-img" :src="reply.user.avatar"></el-avatar>
          <div class="author-info">
            <div class="info-top">
              <div class="talk-box">
                <span>{{ reply.user.name || '匿名' }} 回复 {{ item.user.name || '匿名' }}：</span>{{ reply.content }}
              </div>
            </div>
            <div class="author-time">{{ reply.created_at }}</div>
          </div>
        </div>
      </div>
    </div>
    <!--    <div style="text-align: center;margin-top: 30px"><p v-if="loading">加载中...</p></div>-->
  </div>
</template>


<script>
import Vue from "vue";

const clickoutside = {
  // 初始化指令
  bind(el, binding, vnode) {
    function documentHandler(e) {
      // 这里判断点击的元素是否是本身，是本身，则返回
      if (el.contains(e.target)) {
        return false;
      }
      // 判断指令中是否绑定了函数
      if (binding.expression) {
        // 如果绑定了函数 则调用那个函数，此处binding.value就是handleClose方法
        binding.value(e);
      }
    }
    // 给当前元素绑定个私有变量，方便在unbind中可以解除事件监听
    el.vueClickOutside = documentHandler;
    document.addEventListener("click", documentHandler);
  },
  update() {},
  unbind(el, binding) {
    // 解除事件监听
    document.removeEventListener("click", el.vueClickOutside);
    delete el.vueClickOutside;
  },
};
export default {
  name: "ArticleComment",
  data() {
    return {
      index: "0",
      replyComment: "",

      user: {},

      loading: false,

      comments: [],
      replyText:'',
      label:'-1',
    };
  },
  directives: { clickoutside },
  beforeMount() {
    this.getComment();
    this.user = JSON.parse(localStorage.getItem("UserInfo"));
  },
  methods: {
    /**
     * 点赞
     */
    likeClick(item) {},
    inputFocus() {
      var replyInput = document.getElementById("replyInput");
      replyInput.style.padding = "8px 8px";
      replyInput.style.border = "2px solid blue";
      replyInput.focus();
    },
    _inputShow(i) {
      return this.comments[i].inputShow;
    },
    // comment
    sendComment() {
      let AtKey =
        new Date().getFullYear() +
        "-" +
        new Date().getMonth() +
        "-" +
        new Date().getDate();
      let count = Number(localStorage.getItem(AtKey));
      let val = count + 1;
      if (count >= 10) {
        this.$message({
          showClose: true,
          type: "warning",
          message: "评论太过频繁",
        });
        return;
      }

      var course_id = this.$route.params.id;
      let user = JSON.parse(localStorage.getItem("UserInfo"));
      if (user && course_id) {
        if (!this.replyComment) {
          this.$message({
            showClose: true,
            type: "warning",
            message: "评论不能为空",
          });
        } else {
          let input = document.getElementById("replyInput");
          let timeNow = new Date().getTime();
          let time = this.dateStr(timeNow);
          this.$axios
            .post(this.url + "/api/comments", {
              content: this.replyComment,
              target_id: course_id,
            })
            .then((res) => {
              if (res.data.code === 200) {
                localStorage.setItem(AtKey, val);

                this.comments.unshift({
                  user: {
                    name: user.name,
                    avatar: user.avatar,
                  },
                  content: this.replyComment,
                  created_at: time,
                });
                this.replyComment = "";
                input.innerHTML = "";
                this.$message.success({
                  showClose: true,
                  message: "评论成功",
                });
              } else if (res.data.code === 4001) {
                localStorage.removeItem("UserInfo");
                localStorage.removeItem("Authorization");
                this.$router.push({ name: "login" });
              } else {
                this.$message.warning({
                  showClose: true,
                  message: res.data.msg,
                });
              }
            })
            .catch((err) => {
              this.comments = [];
            });
        }
      } else {
        localStorage.removeItem("UserInfo");
        localStorage.removeItem("Authorization");
        this.$router.push({ name: "login" });
      }
    },

    // 开启回复框
    readyReply(i){
      this.label = this.label == i ? '-1' : i;
    },

    //reply
    sendReply(comment_id, i) {
      let AtKey =
          new Date().getFullYear() +
          "-" +
          new Date().getMonth() +
          "-" +
          new Date().getDate();
      let count = Number(localStorage.getItem(AtKey));
      let val = count + 1;
      if (count >= 20) {
        this.$message({
          showClose: true,
          type: "warning",
          message: "回复太过频繁",
        });
        return;
      }

      let user = JSON.parse(localStorage.getItem("UserInfo"));
      if (user) {
        if (!this.replyText) {
          this.$message({
            showClose: true,
            type: "warning",
            message: "回复不能为空",
          });
        } else {
          let timeNow = new Date().getTime();
          let time = this.dateStr(timeNow);
          this.$axios.post(this.url + "/api/comments/reply", {
                content: this.replyText,
                comment_id: comment_id,
              })
              .then((res) => {
                if (res.data.code === 200) {
                  localStorage.setItem(AtKey, val);
                  let comment = this.comments[i];
                  comment['reply'].unshift({
                    user: {
                      name: user.name,
                      avatar: user.avatar,
                    },
                    content: this.replyText,
                    created_at: time,
                  });
                  this.comments[i] = comment
                  this.replyText = "";
                  // this.$message.success({
                  //   showClose: true,
                  //   message: "回复成功",
                  // });
                } else if (res.data.code === 4001) {
                  localStorage.removeItem("UserInfo");
                  localStorage.removeItem("Authorization");
                  this.$router.push({ name: "login" });
                } else {
                  this.$message.warning({
                    showClose: true,
                    message: res.data.msg,
                  });
                }
              })
              .catch((err) => {
                this.comments = [];
              });
        }
      } else {
        localStorage.removeItem("UserInfo");
        localStorage.removeItem("Authorization");
        this.$router.push({ name: "login" });
      }
    },
    dateStr(date) {
      //获取js 时间戳
      var time = new Date().getTime();
      //去掉 js 时间戳后三位，与php 时间戳保持一致
      time = parseInt((time - date) / 1000);
      //存储转换值
      var s;
      if (time < 60 * 10) {
        //十分钟内
        return "刚刚";
      } else if (time < 60 * 60 && time >= 60 * 10) {
        //超过十分钟少于1小时
        s = Math.floor(time / 60);
        return s + "分钟前";
      } else if (time < 60 * 60 * 24 && time >= 60 * 60) {
        //超过1小时少于24小时
        s = Math.floor(time / 60 / 60);
        return s + "小时前";
      } else if (time < 60 * 60 * 24 * 30 && time >= 60 * 60 * 24) {
        //超过1天少于30天内
        s = Math.floor(time / 60 / 60 / 24);
        return s + "天前";
      } else {
        //超过30天ddd
        var date = new Date(parseInt(date));
        return (
          date.getFullYear() +
          "/" +
          (date.getMonth() + 1) +
          "/" +
          date.getDate()
        );
      }
    },
    onDivInput: function (e) {
      this.replyComment = e.target.innerHTML;
    },
    goLogin(){
      localStorage.removeItem("UserInfo");
      localStorage.removeItem("Authorization");
      this.$router.push({ name: "login" });
    },

    //获取评论
    getComment() {
      var id = this.$route.params.id;
      if (id) {
        this.$axios
          .get(this.url + "/api/comments", {
            params: {
              target_id: id,
            },
          })
          .then((res) => {
            if (res.data.code === 200) {
              this.comments = res.data.data.comments;
            }
          })
          .catch((err) => {
            this.comments = [];
          });
      }
    },
  },
};
</script>


<style scoped lang="scss">
.active {
  color: #f00;
}
.my-reply {
  margin-bottom: 50px;
  .header-img {
    display: inline-block;
    vertical-align: top;
  }
  .reply-input {
    min-height: 120px;
    padding: 10px;
    color: #999;
    background-color: #fff;
    border-radius: 5px;
    font-size: 14px;
    border: 1px solid #efefef !important;
    outline: none;
    &:focus,
    &:focus-visible {
      border: 1px solid #efefef !important;
    }
    &:empty:before {
      content: attr(placeholder);
    }
    &:focus:before {
      content: none;
    }
  }
  .reply-btn-box {
    text-align: right;
    padding-top: 20px;
    .reply-btn {
      background-color: #4886fd !important;
      border: 0 !important;
      position: relative;
    }
  }
}
.my-comment-reply {
  margin-left: 50px;
  .reply-input {
    width: auto;
  }
}
.author-title {
  padding-bottom: 25px;
  border-bottom: 1px solid #efefef;
  margin-bottom: 25px;
  .reply-father {
    width: 100%;
    display: flex;
    align-items: flex-start;

    .author-info {
      .author-name {
        font-size: 14px;
        color: #959ea7;
        line-height: 1.8;
      }
      .talk-box {
        font-size: 15px;
        color: #000;
        line-height: 1.8;
        padding-bottom: 5px;
      }
      .author-time {
        font-size: 14px;
        color: #959ea7;
        line-height: 1.2;
      }
    }
  }
  //
  .reply-btn{
    color:#959ea7;padding-left:25px;cursor:pointer;
    &:hover{color:#4886fd;}
  }
  //
  .replay-card{
    margin-left:71px;margin-top:20px;
    .xg-btn{background:#4886fd;color:#fff;border-radius:2px;width:100px;height:40px;line-height:40px;text-align:center;cursor: pointer;}
    /deep/ .el-input{
      margin-bottom:10px;
      input{height:40px;line-height:40px;padding-left:15px;padding-right:15px;border-radius:4px;padding-top:0;padding-bottom:0;}
    }
  }
  //
  .reply-childern {
    padding-left: 70px;
    .childern-item {
      display: flex;
      align-items: flex-start;
      margin-top: 30px;
    }
    .header-img {
      width: 44px;
      height: 44px;
      background: #c0c4cc;
      border-radius: 100%;
      overflow: hidden;
      margin-right: 10px;
      flex-shrink: 0;
    }
    .author-info {
    }
    .info-top {
      display: flex;
      align-items: flex-start;
      font-size: 14px;
      color: #959ea7;
      line-height: 1.2;
    }
    .talk-box {
      span {
        color: #959ea7;
      }
      color: #000;
      line-height: 1.5;
    }
    .author-time {
      color: #959ea7;
      font-size: 14px;
      line-height: 1.2;
      padding-top: 10px;
    }
  }

  .header-img {
    flex-shrink: 0;
    width: 56px;
    height: 56px;
    border-radius: 100%;
    margin-right: 15px;
  }
  .icon-btn {
    width: 30%;
    padding: 0 !important;
    float: right;
    @media screen and (max-width: 1200px) {
      width: 20%;
      padding: 7px;
    }
    span {
      cursor: pointer;
    }
    .iconfont {
      margin: 0 5px;
    }
  }
}
// 未登录的样式
.noinput {
  margin-bottom: 50px;
  width: 100%;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #efefef;
  border-radius: 5px;
  .text {
    color: #4a5764;
    font-size: 14px;
    line-height: 1.2;
    margin-right: 14px;
  }
  .xg-btn {
    width: 100px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    color: #fff;
    font-size: 14px;
    background: #4886fd;
    border-radius: 2px;
    display: block;
  }
}
@media screen and (max-width: 750px) {

  .my-reply,
  .noinput{margin-bottom:20px;}
  .author-title{padding-bottom:20px;margin-bottom:20px;}
  .author-title .header-img{width:40px;height:40px;}
  .author-title .reply-father .author-info .talk-box{font-size:13px;}
  .author-title .reply-childern{padding-left:30px;}
  .author-title .reply-childern .childern-item{margin-top:20px;}
  .author-title .reply-childern .header-img{width:30px;height:30px;}
  .author-title .reply-childern .info-top{font-size:12px;}
  .author-title .reply-childern .author-time{padding-top:5px;font-size:12px;}
  .author-title .replay-card{margin-top:15px;margin-left:50px;}
  .author-title .replay-card .xg-btn{width:68px;height:34px;line-height:34px;font-size:12px;}
}
</style>

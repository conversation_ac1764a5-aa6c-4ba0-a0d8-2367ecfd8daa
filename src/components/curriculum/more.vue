<template>
  <div>
    <!-- 内容区域 -->
    <div style="overflow: hidden;">
      <el-col :span="24"  class="content" v-for="(item,index) in contentlist" :key="index">
        <router-link :to="{name:'coursepage', params:{id:item.id}}">

        <el-row :gutter="40" v-show="index%2===0">
            <el-col :span="8" :xs="24">
              <el-card class="box-card">
                <div slot="header" class="clearfix topone">
                  <span style="float: left;width: 60%">{{ item.name }}</span>
                  <el-button style="float: right; padding: 3px 0;font-size: 15px" type="text" v-if="item.price > 0">
                    <span style="color: #f00;">{{item.price}}</span>
                  </el-button>
                  <el-button style="float: right; padding: 3px 0;font-size: 15px" type="text" v-else>
                    <span style="color: green;">免费</span>
                  </el-button>
                  <el-button style="float: right; padding: 5px 0;font-size: 8px;" type="text" v-if="item.market_price > 0">
                    <s>{{item.market_price}}</s>
                  </el-button>
                </div>
                <div class="text item">
                  <p>{{ item.explain }}</p>
                </div>
                <div class="botnfooter">
                  <time>{{ item.created_at }}</time>
                  <span style="float: right; padding: 3px 0">
                    <svg t="1633617101433" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2111" width="16" height="16"><path d="M512 42.666667a256 256 0 0 1 256 256h128a42.666667 42.666667 0 0 1 42.666667 42.666666l-42.666667 597.333334a42.666667 42.666667 0 0 1-42.666667 42.666666H170.666667a42.666667 42.666667 0 0 1-42.666667-42.666666L85.333333 341.333333a42.666667 42.666667 0 0 1 42.666667-42.666666h128a256 256 0 0 1 256-256z m170.666667 256a170.666667 170.666667 0 1 0-341.333334 0h341.333334zM170.666667 384l42.666666 512h597.333334l42.666666-512H170.666667z m213.333333 298.666667v85.333333a42.666667 42.666667 0 0 1-85.333333 0v-85.333333a42.666667 42.666667 0 0 1 85.333333 0z m170.666667-42.666667v128a42.666667 42.666667 0 0 1-85.333334 0v-128a42.666667 42.666667 0 0 1 85.333334 0z m170.666666-85.333333v213.333333a42.666667 42.666667 0 0 1-85.333333 0v-213.333333a42.666667 42.666667 0 0 1 85.333333 0z" p-id="2112" fill="#999999"></path></svg>
                    {{ item.sales_num }}
                  </span>
                </div>
              </el-card>
            </el-col>
              <el-col class="rightimg" :span="16" :xs="24">
                <img :src="item.images" width="100%"/>
              </el-col>
          </el-row>
        </router-link>
        <router-link :to="{name:'coursepage', params:{id:item.id}}">

          <el-row :gutter="40" v-show="index%2===1">
              <el-col class="rightimg" :span="16" :xs="24">
                <img :src="item.images" width="100%"/>
              </el-col>
            <el-col :span="8" :xs="24">
              <el-card class="box-card">
                <div slot="header" class="clearfix topone">
                  <span>{{ item.name }}</span>
                  <span style="float: right; padding: 3px 0;color: #f00" v-if="item.price > 0">{{item.price}}</span>
                  <span style="float: right; padding: 3px 0;color: #f00" v-if="item.price > 0">{{item.price}}</span>
                  <span style="float: right; padding: 3px 0;color: green" v-else>免费</span>
                </div>
                <div class="text item">
                  <p>{{ item.explain }}</p>
                </div>
                <div class="botnfooter">
                  <time>{{ item.created_at }}</time>
                  <span style="float: right; padding: 3px 0">
                      <svg t="1633617101433" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2111" width="16" height="16"><path d="M512 42.666667a256 256 0 0 1 256 256h128a42.666667 42.666667 0 0 1 42.666667 42.666666l-42.666667 597.333334a42.666667 42.666667 0 0 1-42.666667 42.666666H170.666667a42.666667 42.666667 0 0 1-42.666667-42.666666L85.333333 341.333333a42.666667 42.666667 0 0 1 42.666667-42.666666h128a256 256 0 0 1 256-256z m170.666667 256a170.666667 170.666667 0 1 0-341.333334 0h341.333334zM170.666667 384l42.666666 512h597.333334l42.666666-512H170.666667z m213.333333 298.666667v85.333333a42.666667 42.666667 0 0 1-85.333333 0v-85.333333a42.666667 42.666667 0 0 1 85.333333 0z m170.666667-42.666667v128a42.666667 42.666667 0 0 1-85.333334 0v-128a42.666667 42.666667 0 0 1 85.333334 0z m170.666666-85.333333v213.333333a42.666667 42.666667 0 0 1-85.333333 0v-213.333333a42.666667 42.666667 0 0 1 85.333333 0z" p-id="2112" fill="#999999"></path></svg>
                      {{ item.sales_num }}
                    </span>
                </div>
              </el-card>
            </el-col>
        </el-row>
        </router-link>
      </el-col>
    </div>
    <div style="text-align: center;margin-top: 30px"><p v-if="loading">加载中...</p></div>


    <!-- 导航区域 -->
    <div class="anniu" @click="showToggle" v-show="iftab">
      <svg t="1633937877062" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5850" width="50" height="50"><path d="M26.91072 983.36768H1000.6528V22.15936H26.91072v961.20832z m237.40416-480.6656c0-12.77952 5.3248-24.90368 14.7456-33.5872l304.9472-282.05056a47.02208 47.02208 0 0 1 65.82272 2.048c17.6128 18.47296 16.67072 47.59552-2.048 64.96256l-268.73856 248.6272 268.57472 248.6272c18.75968 17.36704 19.6608 46.44864 2.048 64.96256-17.57184 18.51392-47.02208 19.41504-65.78176 2.048L278.9376 536.24832a45.8752 45.8752 0 0 1-14.62272-33.5872z" p-id="5851" fill="#24416b"></path></svg>
    </div>
    <div class="navs" v-show="navshow">
      <div class="navs-top"></div>
      <ul>
        <li class="navone" v-for="(val,index) in levelInfo" :key="index">
          <span></span>{{val.name}}
          <ul id="navs">
            <li :class="{active:active==index}" v-for="(item,index) in contentlist" @click="scrollTo(index)" v-if="val.level===item.level">
              <div class="navtext">{{index+1}}、{{item.name}}</div>
            </li>
          </ul>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  props: {},
  data() {
    return {
      active: 0, // 当前激活的导航索引
      navshow: false,
      iftab: false,
      levelInfo:[],
      contentlist:[],

      limit: 20,
      offset: 0,
      total: 0,
      loading: false,
    }
  },
  beforeMount(){
    this.getLevel();
    this.getInfo(this.limit, this.offset)
  },
  mounted() {
    // 监听滚动事件
    window.addEventListener('scroll', this.onScroll, false)
    //监听navs
    window.addEventListener("scroll", this.scrollToTop);

    this.infoLoad();
  },
  destroy() {
    // 必须移除监听器，不然当该vue组件被销毁了，监听器还在就会出错
    window.removeEventListener('scroll', this.onScroll)
  },
  //离开该页面需要移除这个监听的事件，不然会报错;
  destroyed() {
    window.removeEventListener("scroll", this.scrollToTop);
  },
  methods: {
    // 滚动监听器
    onScroll() {
      // 获取所有锚点元素
      const navContents = document.querySelectorAll('.content')
      // 所有锚点元素的 offsetTop
      const offsetTopArr = []
      navContents.forEach(item => {
        offsetTopArr.push(item.offsetTop)
      })
      // 获取当前文档流的 scrollTop
      const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
      // 定义当前点亮的导航下标
      let navIndex = 0
      for (let n = 0; n < offsetTopArr.length; n++) {
        // 如果 scrollTop 大于等于第n个元素的 offsetTop 则说明 n-1 的内容已经完全不可见
        // 那么此时导航索引就应该是n了
        if (scrollTop >= offsetTopArr[n]) {
          navIndex = n
        }
      }
      this.active = navIndex
    },
    // 跳转到指定索引的元素
    scrollTo(index) {
      // 获取目标的 offsetTop
      // css选择器是从 1 开始计数，我们是从 0 开始，所以要 +1
      const targetOffsetTop = document.querySelector(`.content:nth-child(${index + 1})`).offsetTop
      // 获取当前 offsetTop
      let scrollTop = document.documentElement.scrollTop || document.body.scrollTop
      // 定义一次跳 50 个像素，数字越大跳得越快，但是会有掉帧得感觉，步子迈大了会扯到蛋
      const STEP = 50
      // 判断是往下滑还是往上滑
      if (scrollTop > targetOffsetTop) {
        // 往上滑
        smoothUp()
      } else {
        // 往下滑
        smoothDown()
      }
      // 定义往下滑函数
      function smoothDown() {
        // 如果当前 scrollTop 小于 targetOffsetTop 说明视口还没滑到指定位置
        if (scrollTop < targetOffsetTop) {
          // 如果和目标相差距离大于等于 STEP 就跳 STEP
          // 否则直接跳到目标点，目标是为了防止跳过了。
          if (targetOffsetTop - scrollTop >= STEP) {
            scrollTop += STEP
          } else {
            scrollTop = targetOffsetTop
          }
          document.body.scrollTop = scrollTop
          document.documentElement.scrollTop = scrollTop
          // 关于 requestAnimationFrame 可以自己查一下，在这种场景下，相比 setInterval 性价比更高
          requestAnimationFrame(smoothDown)
        }
      }
      // 定义往上滑函数
      function smoothUp() {
        if (scrollTop > targetOffsetTop) {
          if (scrollTop - targetOffsetTop >= STEP) {
            scrollTop -= STEP
          } else {
            scrollTop = targetOffsetTop
          }
          document.body.scrollTop = scrollTop
          document.documentElement.scrollTop = scrollTop
          requestAnimationFrame(smoothUp)
        }
      }
    },

    scrollToTop(el) {
      let topBtn = document.getElementById("navs");
      let scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop;
      let browserHeight = window.outerHeight;
      if (scrollTop > browserHeight - 50) {
        // topBtn.style.display = "block";
        let clientWidth =document.documentElement.clientWidth;
        if(clientWidth < 768){
          this.navshow = false
        }else {
          this.navshow = true
        }

        this.iftab = true
      } else {
        // topBtn.style.display = "none";
        this.navshow = false
        this.iftab = false
      }
    },
    showToggle:function() {
      this.navshow = !this.navshow
      // if (this.isShow) {
      //   this.btnText = "隐藏"
      // } else {
      //   this.btnText = "显示"
      // }
    },

    // 滚动到底部加载
    infoLoad() {
      let isLoading = false
      window.onscroll=()=> {
        // 距离底部200px时加载一次
        let bottomOfWindow = document.documentElement.offsetHeight - document.documentElement.scrollTop - window.innerHeight <= 200
        if (bottomOfWindow && isLoading === false) {
          let currOffset = this.offset;
          if ((this.offset + 1) * this.limit < this.total) {
            this.getInfo(this.limit, currOffset + 1)
            isLoading = false;
          } else {
            isLoading = true
          }
        }
      }
    },

    // 获取信息
    getInfo(limit, offset) {
      this.loading = true
      var id = this.$route.query.id;
      let data = {
        limit: limit,
        offset: offset
      };
      if (id) {
        data.cate_id = id
      }
      this.$axios.get(this.url + '/api/courses',{
        params:data
      }).then(res => {
        if (res.data.code === 200) {
          res.data.data.courses.forEach((item, key)=>{
            this.contentlist.push(item)
          });
          this.total = res.data.data.total;
          this.offset = offset;
        }

        this.loading = false
      }).catch(err => {
        this.loading = false
      });
    },
    getLevel() {
      var id = this.$route.query.id;
      let data = {};
      if (id) {
        data = {
          cate_id:id
        }
      }
      this.$axios.get(this.url + '/api/courses/level',{
        params: data
      }).then(res => {
        if (res.data.code === 200) {
          this.levelInfo = res.data.data
        }
      }).catch(err => {});
    }
  },
  watch: {
    "$route": {
      handler(route){
        this.contentlist = [];
        this.getLevel();
        this.getInfo();
      }
    }
  }
}
</script>

<style scoped lang="scss">
/* 内容区的样式 */
.content {
  width: 100%;
  margin-top: 30px;
  background:white;
  padding: 20px;
  .rightimg{
    height: 500px;
    overflow: hidden;
    img{
      width: 100%;
    }
  }
}
.anniu{
  position: fixed;
  top: 0;
  right: 0;
  z-index: 99999999;
  display: none;
}
/* 导航栏的样式 */
.navs {
  width: 200px;
  position: fixed;
  top: 0;
  right: 50px;
  background-color: white;
  cursor: pointer;
  height:45vh;
  overflow-y: auto;
  padding: 0 20px;
  padding-bottom: 20px;
  border: 1px solid #e4ecf3;
  border-top:4px solid #E6A23C;
}
/*.navs-top{*/
  /*width: 100%;*/
  /*height: 4px;*/
  /*background: #11132a;*/
/*}*/
.navs::-webkit-scrollbar {
  width: 0 !important
}
.navs {
  -ms-overflow-style: none;
  overflow: -moz-scrollbars-none;
}

#navs{
  transition: all 0.5s;
}
.navone{
  font-size: 18px!important;
  margin-top: 20px!important;
  color: #333333!important;
  border-bottom: 0!important;
  span{
    width: 4px;
    height: 16px;
    background: #E6A23C;
    display: inline-block;
    margin-right: 5px;
    margin-top: 5px;
  }
}
.navone:hover{
  color: #333333!important;
}
.navs ul{
  padding: 0;
}
.navs li {
  font-size: 16px;
  margin-top: 10px;
  padding: 5px 0;
  color: #666666;
  border-bottom: 1px dashed #E6A23C;
  .navtext{
    line-height: 20px!important;
  }
}
.navs li:hover{
  color: #E6A23C;
}
.mores{
  margin-top: 20px;
  font-size: 14px ;
  color: #E6A23C;
  text-align: right;
}
/* 当导航被点亮后改变颜色 */
.navs .active{
  color: #E6A23C;
  background-color: white;
}
@media only screen and (max-width: 900px) {
  .rightimg{
    height: auto!important;
    overflow: hidden;
    margin-top: 10px;
    img{
      width: 100%;
    }
  }
  .box-card{
    margin-top: 10px;
  }
}
@media only screen and (max-width: 768px) {
  .anniu{
    display: block;
  }
  .navs {
    width: 200px;
    position: fixed;
    top: 0;
    right: 0!important;
  }
}
</style>

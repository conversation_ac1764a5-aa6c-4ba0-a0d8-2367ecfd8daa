<template>
  <div class='prism-player' :id='playerId'>
      <slot></slot>
  </div>
</template>

<script>
import $ from 'jquery'

const aliplayerSdkPath = '//g.alicdn.com/de/prismplayer/2.8.2/aliplayer-min.js'
const jqueryPath = 'https://apps.bdimg.com/libs/jquery/2.1.4/jquery.min.js'

export default {
    name: 'Aliplayer',
    props: {
      // 视频来源
      source: {
            type: String,
            default: ''
        },
        vid: {
            type: String,
            default: ''
        },
        playauth: {
            type: String,
            default: ''
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '320px'
        },
        videoWidth: {
            type: String,
            default: '100%'
        },
        videoHeight: {
            type: String,
            default: '100%'
        },
        preload: {
            type: Boolean,
            default: false
        },
        cover: {
            //视频封面图
         type: String,
            default: ''
        },
        isLive: {
          // true：直播  false：点播
          type: Boolean,
            default: false
        },
        autoplay: {
            type: Boolean,
            default: false
        },
        rePlay: {
            type: Boolean,
            default: false
        },
        useH5Prism: {
            type: Boolean,
            default: false
        },
        useFlashPrism: {
            type: Boolean,
            default: false
        },
        playsinline: {
            type: Boolean,
            default: false
        },
        showBuffer: {
            type: Boolean,
            default: true
        },
        skinRes: {
            type: String,
            default: ''
        },
        skinLayout: {
            type: Array
        },
        controlBarVisibility: {
            type: String,
            default: 'always'
        },
        showBarTime: {
            type: String,
            default: ''
        },
        extraInfo: {
            type: String,
            default: ''
        },
        enableSystemMenu: {
            type: Boolean,
            default: false
        },
        format: {
            type: String,
            default: ''
        },
        mediaType: {
            type: String,
            default: 'video'
        },
        qualitySort: {
            type: String,
            default: 'asc'
        },
        definition: {
            type: String,
            default: ''
        },
        defaultDefinition: {
            type: String,
            default: ''
        },
        x5_type: {
            type: String,
            default: 'h5'
        },
        x5_fullscreen: {
            type: Boolean,
            default: false
        },
        x5_video_position: {
            type: String,
            default: 'center'
        },
        x5_orientation: {
            type: String,
            default: 'portrait'
        },
        // TODO 待测
        x5LandscapeAsFullScreen: {
            type: Boolean,
            default: true
        },
        autoPlayDelay: {
            type: Number,
            default: 0
        },
        autoPlayDelayDisplayText: {
            type: String,
            default: '正在转码，请稍后......'
        },
        language: {
            type: String,
            default: 'zh-cn'
        },
        languageTexts: {
            type: Object,
            default() {
                return {}
            }
        },
        snapshot: {
            type: Boolean,
            default: false
        },
        snapshotWatermark: {
            type: Object,
            default() {
                return {}
            }
        },
        useHlsPluginForSafari: {
            type: Boolean,
            default: false
        },
        enableStashBufferForFlv: {
            type: Boolean,
            default: false
        },
        stashInitialSizeForFlv: {
            type: Number,
            default: 10
        },
        loadDataTimeout: {
            type: Number,
            default: 20
        },
        waitingTimeout: {
            type: Number,
            default: 60
        },
        liveStartTime: {
            type: String,
            default: ''
        },
        liveOverTime: {
            type: String,
            default: ''
        },
        liveTimeShiftUrl: {
            type: String,
            default: ''
        },
        liveShiftSource: {
            type: String,
            default: ''
        },
        recreatePlayer: {
            type: Function,
            default: () => {
            }
        },
        diagnosisButtonVisible: {
            type: Boolean,
            default: true
        },
        disableSeek: {
            type: Boolean,
            default: false
        },
        encryptType: {
            type: Number,
            default: 0
        },
        progressMarkers: {
            type: Array,
            default: () => []
        },
        vodRetry: {
            type: Number,
            default: 3
        },
        liveRetry: {
            type: Number,
            default: 5
        },

        // 下单购买
        // previewBarHtml: {
        //   type: String,
        //   default: `<a @click.prevent class="vip-join">下单购买后</a>观看完整视频`
        // },
        // // 试看时间
        // previewDuration: {
        //   type: Number,
        //   default: 60
        // },
        // previewEndHtml: {
        //   type: String,
        //   // default: `<!--<div>previewEndHtml 插入的元素</div>-->`
        //   default: `#endPreviewTemplate`
        // }
    },
    data() {
        return {
            playerId: 'aliplayer_' + Math.floor((Math.random() * 100000000000000000)+1),
            instance: null,
            mark: 'www.radirhino.com.犀光课程'
        }
    },
    created() {
        if (window.Aliplayer) {
            this.initAliplayer()
        } else {
            this.insertScriptTag()
        }
    },
  mounted() {
      this.initMark();
  },

  methods: {
        // 插入播放器脚本
        insertScriptTag() {
            let playerScriptTag = document.getElementById('playerScriptTag')
            if (playerScriptTag === null) {
                playerScriptTag = document.createElement('script')
                playerScriptTag.src = aliplayerSdkPath
                playerScriptTag.id = 'playerScriptTag'
                let head = document.getElementsByTagName('head')[0]
                head.appendChild(playerScriptTag)
            }
            playerScriptTag.addEventListener('load', () => {
                this.initAliplayer()
            })
        },
        // 初始化水印
        initMark() {
          let user = JSON.parse(localStorage.getItem('UserInfo'));
          if (user) {
            let info = user.name + '.' + user.phone;
            this.mark = this.mark +'<' + info + '>'
          }
        },
        // 初始化播放器
        initAliplayer() {
            if (this.instance === null) {
                this.$nextTick(() => {
                    const saveTime = function (memoryVideo,currentTime) {
                      localStorage.setItem(memoryVideo, currentTime);
                    }

                    const getTime = function (memoryVideo) {
                      //return返回的是自定义起播时间
                      return localStorage.getItem(memoryVideo)
                    }

                  var FdComponent = window.Aliplayer.Component({
                    init:function(...props)
                    {
                    },
                    createEl:function(el)
                    {
                      let jq = document.getElementById('jq')
                      if (jq === null) {
                        jq = document.createElement('script')
                        jq.src = jqueryPath
                        jq.id = 'jq'
                        let head = document.getElementsByTagName('head')[0]
                        head.appendChild(jq)
                      }

                      let html = '';

                      html += '<div class="fd-component" style="display: block; position: absolute; right: 140px; bottom: 0px;z-index: 10" data-label="1">';
                      html += '<a href="javascript:void(0);">';
                      html += '<div style="margin-bottom: 7px;width: 26px;height: 26px;background: url(https://main.radirhino.com/bc.png);background-size: contain;"></div>';
                      html += '</a>';
                      html += '</div>';
                      if(window.innerWidth >900){
                        $(el).append(html);
                      } else {
                        $(el).append('<div class="fd-component"></div>');
                      }

                        document.querySelector(".fd-component").addEventListener('click', function(){
                          if(window.innerWidth >900){
                            let video = document.getElementById('video');
                            let elColL = document.getElementById('video-el-col-l');
                            let elColR = document.getElementById('video-el-col-r');
                            let label = document.querySelector(".fd-component");

                            if (label.getAttribute("data-label") !== 'fc') {
                              label.setAttribute("data-label", 'fc');
                              elColR.style.display = 'none';
                              elColL.style.width = '100%';
                              elColL.style.height = '667px';
                              video.style.height = '667px';
                              document.querySelector("#video div").style.height = '667px';
                            } else {
                              label.setAttribute("data-label", 'fd');

                              elColR.style.display = 'block';
                              elColL.style.width = '82%';
                              elColL.style.height = '760px';
                              video.style.height = '760px';
                              document.querySelector("#video div").style.height = '760px';
                            }
                          } else {
                            document.querySelector(".fd-component").style.display = 'none';
                          }
                        })
                    }
                  });

                    this.instance = window.Aliplayer({
                        id: this.playerId,
                        source: this.source,
                        vid: this.vid,
                        playauth: this.playauth,
                        width: this.width,
                        height: this.height,
                        videoWidth: this.videoWidth,
                        videoHeight: this.videoHeight,
                        preload: this.preload,
                        cover: this.cover,  //视频封面
                        isLive: this.isLive,
                        autoplay: this.autoplay,
                        rePlay: this.rePlay,
                        useH5Prism: this.useH5Prism,
                        useFlashPrism: this.useFlashPrism,
                        playsinline: this.playsinline,
                        showBuffer: this.showBuffer,
                        skinRes: this.skinRes,
                        skinLayout: this.skinLayout,
                        controlBarVisibility: this.controlBarVisibility,
                        showBarTime: this.showBarTime,
                        extraInfo: this.extraInfo,
                        enableSystemMenu: this.enableSystemMenu,
                        format: this.format,
                        mediaType: this.mediaType,
                        qualitySort: this.qualitySort,
                        definition: this.definition,
                        defaultDefinition: this.defaultDefinition,
                        x5_type: this.x5_type,
                        x5_fullscreen: this.x5_fullscreen,
                        x5_video_position: this.x5_video_position,
                        x5_orientation: this.x5_orientation,
                        x5LandscapeAsFullScreen: this.x5LandscapeAsFullScreen,
                        autoPlayDelay: this.autoPlayDelay,
                        autoPlayDelayDisplayText: this.autoPlayDelayDisplayText,
                        language: this.language,
                        languageTexts: this.languageTexts,
                        snapshot: this.snapshot,
                        snapshotWatermark: this.snapshotWatermark,
                        useHlsPluginForSafari: this.useHlsPluginForSafari,
                        enableStashBufferForFlv: this.enableStashBufferForFlv,
                        stashInitialSizeForFlv: this.stashInitialSizeForFlv,
                        loadDataTimeout: this.loadDataTimeout,
                        waitingTimeout: this.waitingTimeout,
                        liveStartTime: this.liveStartTime,
                        liveOverTime: this.liveOverTime,
                        liveTimeShiftUrl: this.liveTimeShiftUrl,
                        liveShiftSource: this.liveShiftSource,
                        recreatePlayer: this.recreatePlayer,
                        diagnosisButtonVisible: this.diagnosisButtonVisible,
                        disableSeek: this.disableSeek,
                        encryptType: this.encryptType,
                        progressMarkers: this.progressMarkers,
                        vodRetry: this.vodRetry,
                        liveRetry: this.liveRetry,
                        components: [
                          {
                            name: 'BulletScreenComponent',
                            type: AliPlayerComponent.BulletScreenComponent,
                            args: [this.mark, {fontSize: '12px', color: '#bfc2c2'}, 'random']
                          },{
                              name: 'RateComponent',
                              type: AliPlayerComponent.RateComponent
                          },{
                              name: 'FdComponent',
                              type: FdComponent,
                              args:['http://example.aliyundoc.com/cover.png']
                          },
                          // {
                          //   name: 'MemoryPlayComponent',
                          //   type: AliPlayerComponent.MemoryPlayComponent,
                          //   /* Set the first parameter to true to enable auto play. The default is false. */
                          //   args:[true,getTime,saveTime]
                          // }
                          // {
                          //   name: 'PreviewVodComponent',
                          //   type: AliPlayerComponent.PreviewVodComponent,
                          //   /**
                          //    * The preview component has these parameters: previewDuration, previewEndHtml, and previewBarHtml.
                          //    * previewDuration: The length of the preview, in seconds. If you want to disable preview, set this parameter to 0. Make sure that it is set to 0 after the audience purchases the video or becomes a VIP.
                          //    * previewEndHtml: The string shown in the middle of the player after the preview is over. The default is null.
                          //    * previewBarHtml: The string shown in the lower-left corner of the player after the preview is over. The default is null.
                          //    * Note 1: The previewEndHtml and previewBarHtml parameters can be set in the same way as the previewEndHtml parameter in the sample code. You can set the type of a script tag to text/x-template, and then set the previewEndHtml parameter to the ID of the script tag. You can also pass strings to both parameters in the same way as the previewBarHtml parameter in the sample code.
                          //    * Note 2: You can also set a DOM string to the previewEndHtml and previewBarHtml parameters. We recommend that you use ES6 template strings.
                          //    */
                          //   args: [this.previewDuration, this.previewEndHtml, this.previewBarHtml]
                          // }
                      ]
                    })
                    // 播放器事件监听
                    this.instance.on('ready', () => {
                        this.$emit('ready', this.instance)
                    })
                    this.instance.on('play', () => {
                        this.$emit('play', this.instance)
                    })
                    this.instance.on('pause', () => {
                        this.$emit('pause', this.instance)
                    })
                    this.instance.on('canplay', () => {
                        this.$emit('canplay', this.instance)
                    })
                    this.instance.on('playing', () => {
                        this.$emit('playing', this.instance)
                    })
                    this.instance.on('ended', () => {
                        this.$emit('ended', this.instance)
                    })
                    this.instance.on('liveStreamStop', () => {
                        this.$emit('liveStreamStop', this.instance)
                    })
                    this.instance.on('onM3u8Retry', () => {
                        this.$emit('onM3u8Retry', this.instance)
                    })
                    this.instance.on('hideBar', () => {
                        this.$emit('hideBar', this.instance)
                    })
                    this.instance.on('showBar', () => {
                        this.$emit('showBar', this.instance)
                    })
                    this.instance.on('waiting', () => {
                        this.$emit('waiting', this.instance)
                    })
                    this.instance.on('timeupdate', () => {
                        this.$emit('timeupdate', this.instance)
                    })
                    this.instance.on('snapshoted', () => {
                        this.$emit('snapshoted', this.instance)
                    })
                    this.instance.on('requestFullScreen', () => {
                        this.$emit('requestFullScreen', this.instance)
                    })
                    this.instance.on('cancelFullScreen', () => {
                        this.$emit('cancelFullScreen', this.instance)
                    })
                    this.instance.on('error', () => {
                        this.$emit('error', this.instance)
                    })
                    this.instance.on('startSeek', () => {
                        this.$emit('startSeek', this.instance)
                    })
                    this.instance.on('completeSeek', () => {
                        this.$emit('completeSeek', this.instance)
                    })
                })
            }
        },
        // 播放器接口
        play() {
            this.instance.play()
        },
        pause() {
            this.instance.pause()
        },
        replay() {
            this.instance.replay()
        },
        seek(time) {
            this.instance.seek(time)
        },
        getCurrentTime() {
            return this.instance.getCurrentTime()
        },
        getDuration() {
            return this.instance.getDuration()
        },
        getVolume() {
            return this.instance.getVolume()
        },
        setVolume(vol) {
            this.instance.setVolume(vol)
        },
        loadByUrl(url, time) {
            this.instance.loadByUrl(url, time)
        },
        replayByVidAndPlayAuth(vid, playauth) {
            this.instance.replayByVidAndPlayAuth(vid, playauth)
        },
        replayByVidAndAuthInfo(vid, accId, accSecret, stsToken, authInfo, domainRegion) {
            this.instance.replayByVidAndAuthInfo(vid, accId, accSecret, stsToken, authInfo, domainRegion)
        },
        setPlayerSize(w, h) {
            this.instance.setPlayerSize(w, h)
        },
        setSpeed(speed) {
            this.instance.setSpeed(speed)
        },
        setSanpshotProperties(width, height, rate) {
            this.instance.setSanpshotProperties(width, height, rate)
        },
        requestFullScreen() {
            this.instance.fullscreenService.requestFullScreen()
        },
        cancelFullScreen() {
            this.instance.fullscreenService.cancelFullScreen()
        },
        getIsFullScreen() {
            return this.instance.fullscreenService.getIsFullScreen()
        },
        getStatus() {
            return this.instance.getStatus()
        },
        setLiveTimeRange(startTime, endTime) {
            this.instance.setLiveTimeRange(startTime, endTime)
        },
        setRotate(deg) {
            this.instance.setRotate(deg)
        },
        getRotate() {
            return this.instance.getRotate()
        },
        setImage(image) {
            this.instance.setImage(image)
        },
        dispose() {
            this.instance.dispose()
        },
        setCover(cover) {
            this.instance.setCover(cover)
        },
        setProgressMarkers(markers) {
            this.instance.setProgressMarkers(markers)
        },
        setPreviewTime(time) {
            this.instance.setPreviewTime(time)
        },
        getPreviewTime() {
            return this.instance.getPreviewTime()
        },
        isPreview() {
            return this.instance.isPreview()
        }
    }
}
</script>

<style scoped>
@import url(//g.alicdn.com/de/prismplayer/2.8.2/skins/default/aliplayer-min.css);

.prism-player {
    overflow: hidden;
}
/deep/.prism-player .prism-cc-btn{
  display: none !important;
}
/deep/.prism-player .prism-setting-btn{
  display: none !important;
}
</style>

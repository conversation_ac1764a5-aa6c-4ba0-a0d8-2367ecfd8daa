<template>
  <!-- 头部 -->
  <div class="navtop">
    <b-navbar toggleable="lg" type="dark" variant="info" id="navbar">
      <img src="../assets/img/logo2.jpg" alt="" class="head-logo">
      <b-navbar-brand href="#" to="/">犀光RadiRhino</b-navbar-brand>
      <b-navbar-toggle target="nav-collapse"></b-navbar-toggle>
      <b-collapse id="nav-collapse" is-nav class="navcont">
        <b-navbar-nav class="navleft">
          <b-nav-item-dropdown text="课程">
            <b-dropdown-item href="#" :to="{name:'course'}">全部</b-dropdown-item>
            <b-dropdown-item href="#" :to="{name:'course', query:{id:cate.id,parent_name:cate.name,parent_id:cate.id,level:cate.level}}" v-for="(cate, key) in courseCates" :key="key">{{ cate.name }}</b-dropdown-item>
          </b-nav-item-dropdown>
          <b-nav-item-dropdown text="插件">
            <b-dropdown-item href="#" :to="{path:'/plug'}">全部</b-dropdown-item>
            <b-dropdown-item href="#" :to="{name:'plugcate', query:{cate_id:cate.id,parent_name:cate.name,parent_id:cate.id,level:cate.level}}" v-for="(cate, key) in plugCates" :key="key">{{ cate.name }}</b-dropdown-item>
          </b-nav-item-dropdown>
          <b-nav-item href="#" to="/news">新闻</b-nav-item>
          <b-nav-item href="#" to="/about">关于</b-nav-item>
        </b-navbar-nav>

        <!-- Right aligned nav items -->
        <b-navbar-nav class="ml-auto navright" v-if="state" right>
          <b-navbar-brand href="#" to="/user" v-if="user.avatar">
            <img :src="user.avatar" :alt="user.name" width="30" height="30">
          </b-navbar-brand>
          <b-navbar-brand href="#" to="/user" v-else>
            {{ user.name }}
          </b-navbar-brand>
          <b-button class="" variant="success" @click="logout">退出</b-button>
        </b-navbar-nav>
        <b-navbar-nav class="ml-auto navright" v-else right>
          <b-button class="" variant="success" to="/login">登录</b-button>
          <b-button class="" variant="success" to="/register">注册</b-button>
        </b-navbar-nav>
      </b-collapse>
    </b-navbar>
  </div>
</template>

<script>
  export default {
    inject: ['reload'],
    name: "headback",
    data(){
      return{
        state:false,
        user: {
          avatar:'https://placekitten.com/g/30/30'
        },

        plugCates:[],
        courseCates:[],
      }
    },
    beforeMount() {
      let user = JSON.parse(localStorage.getItem('UserInfo'))
      if (user) {
        this.state = true;
        this.user = user;
      }

      this.getPlugCate();
      this.getCourseCate();
    },
    methods:{
      logout(){
        this.$axios.post(this.url + '/api/user/logout',{
          params:{}
        }).then(res => {
          localStorage.removeItem('UserInfo');
          localStorage.removeItem('Authorization');
          localStorage.removeItem('IsLogin');
          if (this.$route.name != 'homeindex') {
            this.$router.push('/');
          }
          this.$nextTick(()=>{
            this.state = false;
            this.user = {};
          })
          this.reload();
        }).catch(err => {})
      },

      getPlugCate(){
        this.$axios.get(this.url + '/api/plug/cate',{
          params:{}
        }).then(res => {
          if (res.data.code === 200) {
            this.plugCates = res.data.data
          }
        }).catch(err => {});
      },

      getCourseCate(){
        this.$axios.get(this.url + '/api/courses/cate',{
          params:{}
        }).then(res => {
          if (res.data.code === 200) {
            this.courseCates = res.data.data
          }
        }).catch(err => {});
      },
      enter(){
        console.log('enter');
      },
      doSomething(){
        console.log(111);
      },
      doSomething1(){
        console.log(222);
      }
    },
    watch: {
      "$route": {
        handler(){
          let user = JSON.parse(localStorage.getItem('UserInfo'))
          if (user) {
            this.state = true;
            this.user = user;
          }
        }
      }
    }
  }
</script>

<style scoped lang="scss">
.head-logo{
  width: 40px;
  height: 40px;
  border-radius: 50px;
  margin-right: 10px;
}

.navbar-expand-lg .navbar-collapse{
  display:flex;
  justify-content:space-between;
}
.navbar{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  width: 100%;
  z-index: 99;
  /*position: absolute;*/
  /*top:0;*/
  /*left: 0;*/
}
button{
  outline: none;
}
.bg-info{
  background-color: rgb(17, 19, 42)!important;
}
.navbar-dark .navbar-brand,.navbar-dark .navbar-nav .nav-link{
  color: white;
}
.btn-success{
  color: white!important;
  background: none!important;
  border: none!important;
  margin-top: 5px;
}

@media only screen and (max-width: 900px){
  .navbar-expand-lg .navbar-collapse{
    display:block;
  }
  .navbar-dark .navbar-toggler{
    color: rgba(0,0,0,0.5);
    border-color: rgba(0,0,0,0.5);
  }
  /deep/.navbar-dark .navbar-toggler-icon{
    background-image: url("../assets/img/nav.png");
  }
}

.bottom {
  margin-top: 13px;
  line-height: 12px;
}

.button {
  padding: 0;
  float: right;
}

.image {
  width: 100%;
  display: block;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both
}

.el-carousel__item h3 {
  color: #475669;
  font-size: 18px;
  opacity: 0.75;
  line-height: 300px;
  margin: 0;
}

.el-carousel__item:nth-child(2n) {
  background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n+1) {
  background-color: #d3dce6;
}

.clearfix{
  content: '';
  display: block;
  overflow: hidden;
  clear: both;
}
.active{
  color: #E6A23C;
}

// 菜单
.b-nav-dropdown:hover{
  .dropdown-menu.show{
    display: block;
  }
}
</style>


<template>
  <div class="xg-ny payment">
    <!--  -->
    <div class="xg-box">
      <!-- 步骤条 -->
      <!-- 给 item 添加对应的类显示对应颜色状态 -->
      <!-- 红色 danger -->
      <!-- 蓝色 active -->
      <div class="xg-process">
        <div class="item">
          <div class="first"></div>
          <div class="last"></div>
          <div class="text">选择商品</div>
        </div>
        <div class="item active">
          <div class="first"></div>
          <div class="last"></div>
          <div class="text">确认并支付</div>
        </div>
        <div class="item">
          <div class="first"></div>
          <div class="last"></div>
          <div class="text">支付成功</div>
        </div>
      </div>
      <!--  -->
      <div class="section section01" v-if="orderInfo.product_type == 3 || orderInfo.product_type == 1">
        <div class="head">配送地址</div>
        <div class="list">
          <div class="item" :class="{'active':item.id== choiceId}" v-for="(item,index) in address" :key="index">
            <!-- 姓名 -->
            <div class="name" @click="choice(item.id)">{{ item.name }}</div>
            <!-- 地址 -->
            <div class="address">{{ item.address }} {{ item.phone }}</div>
            <!-- 操作 -->
            <div class="option">
              <div class="edit el-icon-edit" @click="editAddress(item)"></div>
              <div class="del el-icon-delete" @click="delAddress(item)"></div>
            </div>
          </div>
        </div>
        <!-- 添加配送地址 -->
        <div class="add-address">
          <div class="text">添加地址</div>
          <el-form ref="form" :model="form" class="address-form">
            <el-input v-model="form.name" class="name-input" placeholder="输入姓名"></el-input>
            <el-input v-model="form.address" class="address-input" placeholder="详细地址"></el-input>
            <el-input v-model="form.phone" class="tel-input" placeholder="联系电话"></el-input>
            <el-button type="primary" class="xg-submit" @click="onSubmit">保存</el-button>
          </el-form>
        </div>
      </div>
      <!-- 订单列表 -->
      <div class="section section02">
        <div class="head">确认订单信息</div>
        <el-table :data="tableData" class="xg-table">
          <el-table-column label="商品" min-width="180">
            <template slot-scope="scope">
              <div class="product">
                <img :src="scope.row.product_img" alt="">
                <div class="text">{{scope.row.product_name}}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="order_amount" label="单价(光子)">
          </el-table-column>
          <el-table-column prop="num" label="数量">
          </el-table-column>
          <el-table-column prop="postage" label="运费">
          </el-table-column>
          <el-table-column prop="total_amount" label="合计(光子)">
          </el-table-column>
        </el-table>
      </div>
      <!--  -->
      <div class="section section03">
        <div class="head">支付方式</div>
        <div class="body">
          <!-- 支付方式 -->
          <div class="left">
            <div class="pay">
              <div class="wechat" :class="{active:pay_type==1}"><img src="../../assets/img/wechat5.jpg" alt="" @click="pay_type = 1"></div>
              <div class="aliplay" :class="{active:pay_type==2}"><img src="../../assets/img/ailpay.jpg" alt="" @click="pay_type = 2"></div>
            </div>
            <template  v-if="orderInfo.pay_method==0">
              <template v-if="orderInfo.is_integral==1">
                <div class="integral" v-if="orderInfo.usable_integral > 0">
                  <el-checkbox v-model="checked" @change="useIntegral">
                    使用{{ orderInfo.usable_integral }}光子抵扣{{orderInfo.usable_integral_price}}元
                  </el-checkbox>
                </div>
              </template>
              <template v-else>
                <div class="integral">
                  当前商品不支持光子抵扣
                </div>
              </template>
            </template>
          </div>
          <!-- 优惠码 -->
          <div class="discount" v-if="orderInfo.pay_method==0">
            <div class="text">使用优惠码</div>
            <el-input v-model="couponCode" @input="coupon"></el-input>
            <div class="text2" v-if="couponPrice > 0">
              可抵扣 <span>{{ couponPrice }}</span> 元
            </div>
          </div>
        </div>
      </div>
      <!-- 明细 -->
      <div class="section section04">
        <div class="body">
          <div class="list">
            <template v-if="orderInfo.pay_method==0">
              <div class="item item1">订单金额：<span>￥{{ orderInfo.total_amount }}</span></div>
              <div class="item item2" v-if="usableIntegralPrice > 0">光子抵扣：<span>￥-{{ usableIntegralPrice }}</span></div>
              <div class="item item3" v-if="couponPrice > 0">优 惠 码：<span>￥-{{ couponPrice }}</span></div>
              <div class="item item4" v-if="orderInfo.postage > 0">运　　费：<span>￥{{ orderInfo.postage }}</span></div>
              <div class="item item5">共1件商品，应付金额：<span>￥<strong>{{ currentPrice }}</strong></span></div>
              <div class="item item6" v-if="orderInfo.product_type == 3">寄送至：{{ currentAddress }}</div>
            </template>
            <template v-else>
              <div class="item item1">订单金额：<span>￥{{ orderInfo.total_amount }}</span></div>
              <div class="item item2" v-if="orderInfo.integral_deduct_price > 0">光子抵扣：<span>￥-{{ orderInfo.integral_deduct_price }}</span></div>
              <div class="item item3" v-if="orderInfo.deduct_price > 0">优 惠 码：<span>￥-{{ orderInfo.deduct_price }}</span></div>
              <div class="item item4" v-if="orderInfo.postage > 0">运　　费：<span>￥{{ orderInfo.postage }}</span></div>
              <div class="item item5">共1件商品，应付金额：<span>￥<strong>{{ orderInfo.order_amount }}</strong></span></div>
              <div class="item item6" v-if="orderInfo.product_type == 3">寄送至：{{ currentAddress }}</div>
            </template>
          </div>
          <div class="xg-btn-group">
            <div class="xg-btn default" @click="go()">返回商品</div>
<!--            <div class="xg-btn primary" @click="getPayQrImg">立即付款</div>-->
            <div class="xg-btn primary" @click="getPayQrImg" v-if="orderInfo.status === 0">立即付款</div>
            <div class="xg-btn primary" v-else-if="orderInfo.status === 2" style="background-color: #72767b">立即付款</div>
            <div class="xg-btn primary" @click="tips()" v-else>立即付款</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 修改地址弹框 -->
    <div class="dialog-open manual-open" @click="editDialog = false" :class="{ active: editDialog }">
      <div class="dialog-box" @click.stop>
        <div class="close el-icon-close" @click="editDialog = false"></div>
        <div class="open-title">修改地址</div>
        <div class="body">
          <el-form ref="form2" :model="form2" :rules="rules" class="editForm" label-width="80px">
            <el-form-item label="收件人" prop="name">
              <el-input v-model="form2.name"></el-input>
            </el-form-item>
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form2.phone"></el-input>
            </el-form-item>
            <el-form-item label="联系地址" prop="address">
              <el-input v-model="form2.address" class="address" type="textarea"></el-input>
            </el-form-item>
            <el-form-item label="邮政编码">
              <el-input v-model="form2.post_code"></el-input>
            </el-form-item>
            <el-form-item>
              <el-checkbox v-model="form2.status">设为默认地址</el-checkbox>
            </el-form-item>
            <el-form-item class="btn-group">
              <el-button type="primary" @click="editAddressSubmit()">确认</el-button>
              <el-button @click="editDialog = false">取消</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 支付弹窗 -->
    <div class="dialog-open manual-open qr-dialog" @click="payDialog = false" :class="{ active: payDialog }">
      <div class="dialog-box" @click.stop>
        <div class="close el-icon-close" @click="payDialog = false"></div>
        <div class="open-title">微信支付</div>
        <div class="body">
          <div class="qr">
            <img :src="this.payQrcode" alt="">
          </div>
          <div class="text">
            支付金额：￥<strong>{{ currentPrice }}</strong>
          </div>
        </div>
      </div>
    </div>

    <div v-html="payInfo" ref="pay"></div>
  </div>
</template>

<script>
import {isEmpty, retain} from "../../utils/common";

export default {
  name: "payment",
  components: {},
  data() {
    return {
      orderTips: "订单已提交，请在 30 分钟内完成支付！逾期订单将被取消。",

      item: {},
      // dialogVisible: false, //支付弹窗
      fullscreenLoading: false,
      orderInfo: {},
      payQrcode: "",
      payInfo: "",

      couponPrice: 0,
      couponCode: "",
      timer: 0,

      // 支付类型
      pay_type:1,

      // 光子抵扣
      checked: false,
      usableIntegralPrice:0,
      usableIntegral:0,

      // 当前支付价格
      currentPrice:0,
      // 当前寄送地址
      currentAddress:'',

      // 地址
      form: {},
      // 选择地址id
      choiceId:0,
      address:[],

      // 订单信息
      tableData:[],
      form2: {
        status:0
      },
      // 修改地址 弹窗
      editDialog: false,
      // 支付 弹窗
      payDialog: false,
      // 修改地址表单验证
      rules: {
        name: [
          { required: true, message: '请输入收件人', trigger: 'blur' },
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'change' },
          { pattern: /^1[3|5|7|8|9]\d{9}$/, message: '请输入正确的号码格式', trigger: 'change' }
        ],
        address: [
          { required: true, message: '请输入联系地址', trigger: 'blur' }
        ]
      }
    };
  },
  beforeMount() {
    this.getOrder();
    this.getUserAddress();
  },
  mounted() {
    // 支付监听
    this.timer = setInterval(() => {
      this.getOrder();
    }, 3000);
  },
  beforeDestroy() {
    //页面关闭前关闭定时器
    clearInterval(this.timer);
  },
  methods: {
    addClass: function (index) {
      this.current = index;
    },

    getOrder() {
      this.$axios
        .post(this.url + "/api/get-order", {
          order_no: this.$route.query.order_no,
        })
        .then((res) => {
          if (res.data.code === 200) {
            let amount = res.data.data.total_amount - this.couponPrice - this.usableIntegralPrice;
            this.currentPrice = retain(amount);
            this.orderInfo = res.data.data;
            this.tableData = [];
            this.tableData.push(res.data.data);
            if (this.orderInfo.status === 1) {
              clearInterval(this.timer);
              this.orderTips = "订单已支付成功。";
              this.payDialog = false;
              this.$router.push({ name: "payment-result", query: {order_no: this.$route.query.order_no}});
            } else if (this.orderInfo.status === 2) {
              clearInterval(this.timer);
              this.orderTips = "";
              this.$message.warning({
                showClose: true,
                message: "订单已关闭，请重新下单进行购买操作！",
              });
              this.payDialog = false;
            } else {
              this.orderTips = "订单已提交，请在 30 分钟内完成支付！逾期订单将被取消。";
            }
          } else if (res.data.code === 4001) {
            localStorage.removeItem("UserInfo");
            localStorage.removeItem("Authorization");
            this.$router.push({name:"login"})
            clearInterval(this.timer);
          } else {
            clearInterval(this.timer);
          }
        })
        .catch((err) => {
          clearInterval(this.timer);
        });
    },
    getPayQrImg() {
      console.log(this.pay_type);
      if (this.pay_type !== 1 && this.pay_type !== 2) {
        this.$message.warning({
          showClose: true,
          message: "请选择支付方式！",
        });
        return false;
      }
      if (this.orderInfo.product_type == 3 && !this.choiceId) {
        this.$message.warning({
          showClose: true,
          message: "请选择收获地址",
        });
        return false;
      }
      this.fullscreenLoading = true;
      this.$axios.post(this.url + "/api/order-pay", {
          order_no: this.$route.query.order_no,
          coupon_code: this.couponCode,
          deduct_integral: this.usableIntegral,
          pay_method: this.pay_type, // 1-微信,2-支付宝
          address_id: this.choiceId, // 地址id
        })
        .then((res) => {
          this.fullscreenLoading = false;
          if (res.data.code === 200 && res.data.msg == "成功") {
            this.$message.success({
              showClose: true,
              message: res.data.msg,
            });

            // this.$router.go(-1);
            this.$router.push({ name: "payment-result", query: {order_no: this.$route.query.order_no}});
          } else if (res.data.code === 200) {
            if (res.data.msg == "支付码生成成功") {
              this.payQrcode = res.data.data.payCodeImg;
              this.payDialog = true;
            } else {
              this.payInfo = res.data.data;
              this.$nextTick(() => {
                this.$refs.pay.children[0].submit();
              });
            }
          } else if (res.data.code === 4001) {
            localStorage.removeItem("UserInfo");
            localStorage.removeItem("Authorization");
            this.$router.push({name:"login"})
          } else {
            this.$message.warning({
              showClose: true,
              message: res.data.msg,
            });
          }
        })
        .catch((err) => {});
    },
    tips() {
      this.$message.success({
        showClose: true,
        message: "订单已支付成功，无需再次购买！",
      });
    },
    coupon() {
      if (!isEmpty(this.couponCode)) {
        this.$axios
          .post(this.url + "/api/coupon-code", {
            code: this.couponCode,
            product_type:this.orderInfo.product_type,
            product_id:this.orderInfo.product_id,
          })
          .then((res) => {
            if (res.data.code === 200) {
              this.$nextTick(() => {
                this.couponPrice = res.data.data;
                let amount = this.orderInfo.total_amount - this.couponPrice - this.usableIntegralPrice;
                this.currentPrice = retain(amount);
              });
            } else if (res.data.code === 4001) {
              localStorage.removeItem("UserInfo");
              localStorage.removeItem("Authorization");
              this.$router.push({name:"login"})
            } else {
              this.$message.warning({
                showClose: true,
                message: res.data.msg,
              });
            }
          })
          .catch((err) => {})
      } else {
        this.couponPrice = 0;
        this.couponCode = '';
      }
    },
    useIntegral(){
      this.$nextTick(() => {
        if (this.checked) {
          this.usableIntegralPrice = this.orderInfo.usable_integral_price;
          this.usableIntegral = this.orderInfo.usable_integral;
          let amount = this.orderInfo.total_amount - this.couponPrice - this.usableIntegralPrice;
          this.currentPrice = retain(amount);
        } else {
          this.usableIntegralPrice = 0;
          this.usableIntegral = 0;
          let amount = this.orderInfo.total_amount - this.couponPrice;
          this.currentPrice = retain(amount);
        }
      });
    },
    onSubmit() {
      if (isEmpty(this.form.name)) {
        this.$message.warning({
          showClose: true,
          message: "请输入姓名",
        });return;
      }

      if (isEmpty(this.form.address)) {
        this.$message.warning({
          showClose: true,
          message: "请输入地址",
        });return;
      }

      if (isEmpty(this.form.phone)) {
        this.$message.warning({
          showClose: true,
          message: "请输入电话",
        });return;
      }

      this.$axios.post(this.url + '/api/address',{
        phone: this.form.phone,
        address: this.form.address,
        name: this.form.name
      }).then(res => {
        if (res.data.code === 200) {
          this.$message.success({
            showClose: true,
            message: res.data.msg,
          });

          this.$nextTick(() => {
            this.choiceId = res.data.data.id;
            this.address.push(res.data.data);
          });

          this.form.phone = '';
          this.form.address = '';
          this.form.name = '';
        } else {
          alert(res.data.msg);
        }
      }).catch(err => {
      });
    },
    /**
     * 修改地址
     */
    editAddress(item) {
      this.editDialog = true;

      this.$set(this.form2, "phone", item.phone);
      this.$set(this.form2, "address", item.address);
      this.$set(this.form2, "name", item.name);
      this.$set(this.form2, "post_code", item.post_code);
      this.$set(this.form2, "status", item.status ? true : false);

      this.address_id = item.id;
    },
    /**
     * 修改地址
     */
    editAddressSubmit() {
      this.$refs.form2.validate((valid) => {
        if (valid) {
          let data = {
            phone: this.form2.phone,
            address: this.form2.address,
            name: this.form2.name,
            post_code: this.form2.post_code,
            status: this.form2.status ? 1 : 0,
          };

          this.$axios.put(this.url + "/api/address/"+this.address_id, data)
              .then((res) => {
                if (res.data.code === 200) {
                  if (this.form2.status == 1) {
                    this.$nextTick(() => {
                      this.choiceId = this.address_id;
                      this.currentAddress = this.form2.address;
                    });
                  }

                  let newAddress = [];
                  this.address.forEach((item, index) => {
                    if (item.id == this.address_id) {
                      item.phone = this.form2.phone;
                      item.address = this.form2.address;
                      item.name = this.form2.name;
                      item.post_code = this.form2.post_code;
                      item.status = this.form2.status;
                    }
                    newAddress.push(item);
                  });
                  this.$nextTick(() => {
                    this.address = newAddress;
                  });

                  this.$message.success({
                    showClose: true,
                    message: res.data.msg,
                  });
                } else {
                  this.$message.warning({
                    showClose: true,
                    message: res.data.msg,
                  });
                }
              })
              .catch((err) => {});

          this.editDialog = false;
        } else {
          return false;
        }
      });
    },

    // 删除地址
    delAddress(val) {
      this.$axios.delete(this.url + "/api/address/"+val.id+"/delete",{})
          .then((res) => {
            if (res.data.code === 200) {
              let newAddress = [];
              this.address.forEach((item) => {
                if (item.id != val.id) {
                  newAddress.push(item);
                }
              });
              this.$nextTick(() => {
                this.address = newAddress;
                if (val.id == this.choiceId) {
                  this.choiceId = 0;
                  this.currentAddress = '';
                }
              });
            }
          })
          .catch((err) => {});
    },

    getUserAddress() {
      this.choiceId = 0;
      this.$axios.get(this.url + "/api/address", {})
          .then((res) => {
            if (res.data.code === 200) {
              this.address = res.data.data;
              res.data.data.forEach((item, index) => {
                if (item.status == 1) {
                  this.$nextTick(() => {
                    this.choiceId = item.id;
                    this.currentAddress = item.address;
                  });
                }
              });
            }
          })
          .catch((err) => {});

    },
    /**
     * 地址选中
     */
    choice(choiceId) {
      this.choiceId = choiceId;

      this.address.forEach((item, index) => {
        if (item.id == choiceId) {
          this.currentAddress = item.address;
        }
      });
    },

    go(){
      switch (this.orderInfo.product_type) {
        case 1:
          this.$router.push({ name: "coursepage", params: {id: this.orderInfo.product_id}});
          break;
        case 2:
          this.$router.push({ name: 'plug', params: {id:this.orderInfo.product_id}})
          break;
        case 3:
          this.$router.push({ name: 'peripheryDetails', params: {id:this.orderInfo.product_id}})
          break;
      }
    }
  },
  created() {},
};
</script>

<style scoped lang="scss">
@import './index.scss';


.blue {
  border: 2px solid #e6a23c !important;
}
//导航
.navbar-expand-lg .navbar-collapse {
  display: flex;
  justify-content: space-between;
}
.navbar {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  width: 100%;
  /*position: relative;*/
  /*top: 0;*/
  z-index: 99;
  /*position: absolute;*/
  /*top:0;*/
  /*left: 0;*/
}
button {
  outline: none;
}
.bg-info {
  /*background-color: rgba(0,0,0,0) !important;*/
  background-color: rgb(17, 19, 42) !important;
}
.navbar-dark .navbar-brand,
.navbar-dark .navbar-nav .nav-link {
  color: white;
}
.btn-success {
  color: white !important;
  background: none !important;
  border: none !important;
  margin-top: 5px;
}

.payment-content {
  margin: 40px 15%;
  background: white;
  padding: 40px 0;
  .payment-top {
    width: 80%;
    margin: 0 auto;
    h4 {
      text-align: left;
      margin-bottom: 10px;
      line-height: 40px;
    }
    p {
      background: rgba(252, 144, 0, 0.1);
      color: #24416b;
      text-align: left;
      padding: 15px;
      margin: 10px 0;
    }
  }
  .payment-zhong {
    width: 80%;
    margin: 20px auto;
    background: #f9f9f9;
    .left {
      height: 120px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .right {
      padding-left: 30px;
      padding-top: 10px;
      p {
        line-height: 20px;
        text-align: left !important;
      }
    }
  }
  .payment-bottom {
    text-align: left;
    width: 80%;
    margin: 20px auto;
    h6 {
      font-size: 16px;
      span {
        font-size: 12px;
        color: #666666;
        margin-left: 10px;
      }
    }
    .pick {
      margin-top: 20px;
      display: flex;
      .pick-left,
      .pick-right {
        border: 2px solid #eeeeee;
        margin-right: 20px;
        line-height: 40px;
        padding: 5px 20px;
        cursor: pointer;
        img {
          width: 40px;
        }
      }
    }
  }
  .zhifu {
    width: 80%;
    margin: 0 auto;
    text-align: right;
    p {
      span {
        color: red;
        font-size: 20px;
      }
    }
    button {
      padding: 10px 20px;
      background: #e6a23c;
      outline: none;
      border: none;
      .el-button--text {
        color: white;
      }
    }
    .dialog-footer {
      text-align: center !important;
    }
  }
}

@media only screen and (max-width: 900px) {
  .navbar-expand-lg .navbar-collapse {
    display: block;
  }
  .navbar-dark .navbar-toggler {
    color: rgba(0, 0, 0, 0.5);
    border-color: rgba(0, 0, 0, 0.5);
  }
  /deep/.navbar-dark .navbar-toggler-icon {
    background-image: url("../../assets/img/nav.png");
  }
  .payment-content {
    margin: 40px 5%;
  }
}

/* ==================== 改版样式 start ==================== */
.payment {
  padding-top:4px;
  padding-bottom:35px;
  &>.xg-box{background:#fff;padding:30px 46px;}
  .section {
    text-align:left;
    .head{height:26px;line-height:26px;border-left:7px solid #0d6efd;padding-left:10px;font-size:18px;color:#000;font-weight:bold;margin-bottom:24px;}
  }
  .section01 {
    margin-bottom:50px;
    .item{
      display:flex;align-items:center;flex-wrap:wrap;
      margin-bottom:20px;
      &.active{
        .name{border-color:#0d6efd;border-width:2px;}
      }
    }
    .name{width:120px;height:36px;line-height:36px;border:1px solid #dbdddf;text-align:center;margin-right:20px;flex-shrink:0;color:#000;font-size:14px;cursor: pointer;}
    .address{font-size:14px;color:#515151;line-height:1.6;margin-right:15px;}
    .option{font-size:14px;color:#515151;}
    .edit,
    .del{
      cursor: pointer;
      &:hover{color:#0d6efd;}
    }
    .edit{margin-right:10px;}
    .add-address{margin-left:140px;border-top:1px solid #dbdddf;padding-top:25px;
      .text{font-size:14px;color:#000;line-height:1.2;color:#000;margin-bottom:15px;font-weight:bold;}
      .address-form{display:flex;align-items:center;}
      /deep/ .el-input__inner{height:42px;line-height:42px;background:#fff;border:1px solid #bdbdbd;padding:0px 15px;font-size:12px;border-radius:0;}
      /deep/ .el-input{margin-right:10px;}
      .name-input{width:120px;flex-shrink:0;}
      .tel-input{width:140px;flex-shrink:0;}
      .xg-submit{width:90px;height:42px;line-height:42px;border-radius:4px;background:#0d6efd!important;color:#fff;text-align:center;font-size:12px;padding:0;border:0;}
    }
  }
  .section02 {
    margin-bottom:40px;
    .product{
      display:flex;align-items: center;
      img{width:85px;flex-shrink:0;margin-right:20px;}
      .text{font-size:14px;color:#000;line-height:1.6;text-align:left;}
    }
    /deep/ .el-table{
      th{border-bottom:0;}
      th.el-table__cell{background:#fafafa;
        &:first-child>.cell{text-align:left!important;}
      }
      th.el-table__cell>.cell{font-size:14px;color:#000;padding-left:30px;padding-right:30px;}
      .el-table__row td{padding-top:40px;padding-bottom:40px;}
      .el-table__row td:last-child .cell{color:#ff0000;font-size:16px;font-weight:bold;}
    }
  }
  .section03 {
    margin-bottom:40px;
    padding-bottom:40px;
    border-bottom:1px solid #dbdddf;
    .body{display:flex;align-items:flex-end;justify-content:space-between;}
    .left{
      .pay{display:flex;margin-bottom:35px;}
      .wechat,
      .aliplay{width:60px;border:4px;border:2px solid transparent;flex-shrink:0;border-radius: 4px;overflow:hidden;cursor: pointer;;
        &.active,
        &:hover{border-color:#0d6efd;}
      }
      .wechat{margin-right:20px;}
      .integral{
        strong{padding-left:14px;}
        /deep/ .el-checkbox{
          font-size:12px;color:#000;
          &.is-checked{
            .el-checkbox__inner{background:#0d6efd;border-color:#0d6efd;}
            .el-checkbox__label{
              color:#000;
            }
            strong{color:#ff0000;}
          }
        }
      }
    }
    .discount{
      .text{font-size:14px;color:#000;line-height:1.2;margin-bottom:10px;font-weight:bold;}
      /deep/ .el-input{
        input{width:260px;height:40px;line-height:40px;border:1px solid #bdbdbd;border-radius:4px;background:#fafafa;padding:0 15px;color:#000;}
      }
      .text2{font-size:12px;color:#000;margin-top:10px;
        span{color:#ff0000;font-weight:bold;}
      }
    }
  }
  .section04 {
    width:100%;display:flex;justify-content:flex-end;align-items:flex-start;
    .list{
      margin-bottom:30px;
      .item{color:#000;font-size:14px;margin-bottom:10px;
        span{color:#ff0000;font-weight:bold;}
        strong{font-size:24px;}
      }
      .item5{margin-bottom:0;}
      .item6{color:#515151;}
    }
  }
  // 弹窗样式

  .dialog-open{
    position: fixed;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;z-index:999;
    transition: all 0.4s cubic-bezier(0.18, 0.89, 0.32, 1.28);
    transform: scale(0.8);
    visibility: hidden;
    opacity: 0;

    &.active {
      transform: scale(1);
      opacity: 1;
      visibility: visible;
    }
    .dialog-box {
      position:relative;
      text-align: left;
      width:100%;
      max-width: 556px;
      height: 440px;
      background: #fff;
      box-shadow: 0px 0px 30px 0px rgba(4, 0, 0, 0.3);
      border-radius: 6px;
      padding: 30px 25px;
    }
    .close {
      font-size: 30px;
      color: #96a1ac;
      position: absolute;
      top: 15px;
      right: 15px;
      cursor: pointer;
    }
    .open-title {
      color: #0084ff;
      line-height: 1.2;
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 30px;
    }
    .editForm /deep/{
      .el-input__inner{border:1px solid #a3acb6;border-radius:0;height:32px;line-height:32px;}
      .el-form-item--mini.el-form-item,
      .el-form-item--small.el-form-item{margin-bottom:15px;}
      .el-form-item__label{color:#909399;}
      .el-checkbox__input.is-checked .el-checkbox__inner,
      .el-checkbox__input.is-indeterminate .el-checkbox__inner{background-color:#0d6efd;border-color:#0d6efd;}
      .el-checkbox__inner{border-color:#a3acb6;}
      .el-checkbox__label{color:#333;}
      .el-checkbox__input.is-checked+.el-checkbox__label{color:#0d6efd;}
      .address .el-textarea__inner{height:100px!important;line-height:1.8;border-color:#a3acb6;border-radius:0;resize: none;

        // 隐藏滚动条
        &::-webkit-scrollbar {
          display: none;
        }
        // 兼容 Firefox
        scrollbar-width: none;
        // 兼容 IE 10 +
        -ms-overflow-style: none;
      }
      .btn-group{width:100%;
        .el-form-item__content{text-align:right;width:100%;}
        .el-button{width:80px;height:32px;line-height:32px;border-radius:0;padding:0;
          &:focus,
          &:hover{background-color:inherit;border-color:inherit;}
          &.el-button--default{border-color:#bdbdbd;color:#bdbdbd;}
        }
      }
    }
  }
  // 支付弹窗
  .qr-dialog{
    .dialog-box{height:480px;}
    .open-title{color:#000;}
    .body{
      text-align:center;
    }
    .qr{width:270px;margin:0 auto 35px;}
    .text{color:#aea4a4;font-size:18px;font-weight:bold;line-height:1.2;
      strong{color:#ff0000;}
    }
  }
}

@media only screen and (max-width: 1280px) {
  .payment{padding-left:40px;padding-right:40px;}
}
@media only screen and (max-width: 750px) {
  .payment{padding:30px 20px;}
  .payment>.xg-box{padding:20px 15px;}
  .payment .section01 .add-address{margin-left:0;}
  .payment .section .head{height:20px;line-height:20px;font-size:14px;margin-bottom:20px;border-left-width:4px;}
  .payment .section01 .list{display:flex;align-items:flex-start;justify-content:flex-start;flex-wrap:wrap;padding-bottom:20px;}
  .payment .section01 .item{width:48%;margin-right:4%;margin-bottom:0;}
  .payment .section01 .item:nth-child(2n){margin-right:0;}
  .payment .section01 .item:nth-child(2)~.item{margin-top:15px;}
  .payment .section01 .name{margin-right:0;font-size:12px;height:auto;line-height:inherit;padding:6px 10px;width:100%;margin-bottom:15px;}
  .payment .section01 .address{font-size:12px;margin-right:0;text-align:justify;padding-top:0px;}
  .payment .section01 .option{font-size:16px;width:100%;justify-content:center;display:flex;align-items:center;margin-top:10px;}
  .payment .section01 .edit{margin-right:40px;}
  .payment .section01 .add-address .address-form{flex-wrap:wrap;}
  .payment .section01 .add-address /deep/{
    .el-input{width:100%;margin-right:0;margin-bottom:10px;}
    .el-input__inner{height:36px;line-height:36px;font-size:12px;padding:0 10px;}
    .xg-submit{width:100%;height:40px;line-height:40px;font-size:12px;font-size:12px;}
  }
  .payment .section01{margin-bottom:30px;}
  .payment .section02 /deep/{
    .el-table th.el-table__cell>.cell{padding-left:10px;padding-right:10px;}
    .el-table .el-table__row td{padding-top:15px;padding-bottom:15px;}
  }
  .payment .section02{margin-bottom:30px;}
  .payment .section02 .product img{width:40px;margin-right:10px;}
  .payment .section02 .product .text{font-size:12px;}
  .payment .section03{padding-bottom:20px;margin-bottom:20px;}
  .payment .section03 .body{flex-wrap:wrap;}
  .payment .section03 .left{width:100%;display:flex;align-items:center;justify-content:center;flex-wrap:wrap;margin-bottom:40px;margin-top:40px;}
  .payment .section03 .left .pay{margin-bottom:20px;}
  .payment .section03 .discount{width:100%;}
  .payment .section03 .discount /deep/{
    .el-input input{width:100%;}
  }
  .payment .section04{width:100%;display:block;}
  .payment .section04 .list{margin-bottom:15px;width:100%;}
  .payment .section04 .list .item{font-size:12px;}
  .payment .dialog-open{width:90%;left:5%;margin-left:auto;}
  .payment .dialog-open .open-title{font-size:16px;}
  .payment .dialog-open .editForm /deep/ {
    .btn-group .el-form-item__content{display:flex;align-items:center;
      &::before,
      &::after{display:none;}
    }
    .btn-group .el-button{flex-grow:1;}
  }
}

/* ==================== 改版样式 end ==================== */
</style>

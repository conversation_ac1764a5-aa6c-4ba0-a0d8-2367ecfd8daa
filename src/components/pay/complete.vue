<template>
  <!-- 支付结果页面 -->
  <div class="xg-ny xg-complete">
    <div class="xg-box">
      <!-- 步骤条 -->
      <!-- 给 item 添加对应的类显示对应颜色状态 -->
      <!-- 红色 danger -->
      <!-- 蓝色 active -->
      <div class="xg-process">
        <div class="item">
          <div class="first"></div>
          <div class="last"></div>
          <div class="text">选择商品</div>
        </div>
        <div class="item">
          <div class="first"></div>
          <div class="last"></div>
          <div class="text">确认并支付</div>
        </div>
        <div class="item" :class="[state==1?'active':'danger']">
          <div class="first"></div>
          <div class="last"></div>
          <div class="text">支付成功</div>
        </div>
      </div>
      <div class="body">
        <div class="message" :class="[state==1?'success':'danger']">
          <!-- 成功状态 -->
          <div class="icon el-icon-check" v-if="state==1"></div>
          <!-- 失败状态 -->
          <div class="icon el-icon-close" v-else></div>
          <!-- 提示信息 -->
          <div class="info">
            <div class="text1" v-if="state==1">支付成功</div>
            <div class="text1" v-else>支付失败，请重新支付</div>
            <div class="detail">
              <div class="text2" v-if="orderInfo.order_amount>0">
                订单金额：<strong>{{ orderInfo.order_amount }}</strong>
                <span v-if="this.orderType==1">光子</span>
                <span v-else>元</span>
              </div>
              <div :class="[state==1?'text2':'text3']" v-if="state!=1">订单交易失败，请重新下单或进入订单详情重新支付，客服联系方式：453395205</div>
            </div>
            <div class="xg-btn-group" v-if="this.orderType==1">
              <div class="xg-btn default" @click="go()" >进入商品介绍</div>
              <router-link tag="div" class="item" to="/user/order"><div class="xg-btn primary">查看订单列表</div></router-link>
            </div>
            <div class="xg-btn-group" v-else>
              <router-link tag="div" class="item" to="user/integral"><div class="xg-btn primary">进入光子账户</div></router-link>
            </div>
            <!-- 导航 -->
            <div class="itembox" v-if="state==1">
              <router-link tag="div" class="item" to="/">返回首页</router-link>
              <router-link tag="div" class="item" :to="{ name: 'course' }">逛视频</router-link>
              <router-link tag="div" class="item" :to="{ path: '/plug' }">逛插件</router-link>
              <router-link tag="div" class="item" :to="{ path: '/periphery' }">逛素材</router-link>
              <router-link tag="div" class="item" to="/user/info">返回会员中心</router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

  export default {
    name: "complete",
    components:{
    },
    data(){
      return{
        tips: '',
        orderInfo: {},
        state:1,
        orderType: 0,
      }
    },
    computed: {

    },
    beforeMount(){
      this.getOrderInfo();
    },
    methods:{
      getOrderInfo(){
        let orderNo = this.$route.query.order_no;
        let url = '';
        if (orderNo=='' || orderNo == undefined) {
          console.log('订单号错误');
          this.$message.error({
            showClose: true,
            message: '订单号错误!'
          });
          return false;
        }

        if (this.$route.query.order_type == 1) {
          this.orderType = 1;
          url = this.url + '/api/get-order';
        } else {
          url = this.url + '/api/integral/order';
          this.orderType = 0;
        }

        this.$axios.post(url, {
          order_no:orderNo
        }).then(res => {
          if (res.data.code === 200) {
            this.orderInfo = res.data.data;
            if (this.orderInfo.status == 1) {
              this.tips = '支付成功';
            } else {
              this.tips = '支付失败';
            }
          } else if (res.data.code === 4001) {
            localStorage.removeItem('UserInfo');
            localStorage.removeItem('Authorization');
            this.$router.push({name:"login"})
          }  else {
            this.$message.error({
              showClose: true,
              message: res.data.msg
            });
            this.tips = res.data.msg;
          }
        }).catch(err => {});
      },

      go(){
        switch (this.orderInfo.product_type) {
          case 1:
            this.$router.push({ name: "coursepage", params: {id: this.orderInfo.product_id}});
            break;
          case 2:
            this.$router.push({ name: 'plug', params: {id:this.orderInfo.product_id}})
            break;
          case 3:
            this.$router.push({ name: 'peripheryDetails', params: {id:this.orderInfo.product_id}})
            break;
        }
      },
      goIntegral() {
        this.$router.push({ name: "userIntegral"});
      }
    },
    created() {
    },
  }

</script>

<style scoped lang="scss">
@import './index.scss';

  .blue {
    border: 2px solid #E6A23C!important;
  }
  //导航
  .navbar-expand-lg .navbar-collapse{
    display:flex;
    justify-content:space-between;
  }
  .navbar{
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    width: 100%;
    /*position: relative;*/
    /*top: 0;*/
    z-index: 99;
    /*position: absolute;*/
    /*top:0;*/
    /*left: 0;*/
  }
  button{
    outline: none;
  }
  .bg-info{
    /*background-color: rgba(0,0,0,0) !important;*/
    background-color: rgb(17, 19, 42)!important;
  }
  .navbar-dark .navbar-brand,.navbar-dark .navbar-nav .nav-link{
    color: white;
  }
  .btn-success{
    color: white!important;
    background: none!important;
    border: none!important;
    margin-top: 5px;
  }

  .payment-content{
    margin: 40px 15%;
    background: white;
    padding: 50px 0;
    .zhong{
      width: 70%;
      margin: 0 auto;
      .payment-top{
        width: 80%;
        margin: 0 auto;
        h4{
          text-align: left;
          margin-bottom: 10px;
          line-height: 80px;
          font-size: 40px;
        }
        .payment-top-text{
          /*background: rgba(252, 144, 0, 0.1);*/
          color: #24416b;
          text-align: left;
          padding: 15px;
          padding-left: 0;
          p{
            margin: 0;
            span{
              color: red;
              font-size: 20px;
              padding-left: 5px;
            }
          }
        }
        .complete-zhong{
          margin-top: 20px;
          text-align: left;
          border-bottom: 1px solid #eeeeee;
          padding-bottom: 50px;
          button{
            padding:10px 20px;
            background: #E6A23C;
            outline: none;
            border: none;
            color: white;
            margin-right: 20px;
          }
        }
        .complete-bottom{
          text-align: left;
          margin: 50px 0;
        }
      }
    }
  }


  @media only screen and (max-width: 900px){
    .navbar-expand-lg .navbar-collapse{
      display:block;
    }
    .navbar-dark .navbar-toggler{
      color: rgba(0,0,0,0.5);
      border-color: rgba(0,0,0,0.5);
    }
    /deep/.navbar-dark .navbar-toggler-icon{
      background-image: url("../../assets/img/nav.png");
    }
    .payment-content{
      margin: 40px 5%;
    }
  }



/* ==================== 改版样式 start ==================== */
.xg-complete{
  padding-top:4px;padding-bottom:40px;
  .xg-box{background:#fff;padding:30px 46px;}
  .body{display:flex;flex-flow:column;justify-content:center;align-items:center;min-height:600px;}
  .message{
    &.danger{
      .icon{background:#ff0000;}
      .text1{color:#ff0000;}
    }
    &.success{
      .icon{background:#00be06;}
      .text1{color:#00be06;}
    }
    display:flex;align-items:flex-start;
    .icon{width:70px;height:70px;background:#000;margin-right:40px;flex-shrink:0;border-radius:100%;color:#fff;font-size: 50px;display: flex;align-items: center;justify-content: center;font-weight: bold;}
    .info{text-align:left;padding-top:20px;min-width:340px;}
    .text1{font-size:26px;font-weight:bold;color:#000;line-height:1.2;margin-bottom:40px;}
    .detail{margin-bottom:80px;}
    .text2{font-size:14px;color:#818e9d;line-height:1.6;margin-bottom:10px;
      strong{color:#ff0000;}
    }
    .text3{color:#000;line-height:1.6;}
  }
  .itembox{
    width:100%;margin-top:35px;
    display:flex;align-items:center;flex-wrap:wrap;
    .item{color:#818e9d;font-size:13px;line-height:1.2;padding-right:12px;margin-right:12px;position:relative;cursor:pointer;flex-shrink:0;
      &:hover{color:#000;}
      &:last-child{margin-right:0;padding-right:0;}
      &:last-child:after{display:none;}
      &:after{content:"";display:block;width:1px;height:100%;background:#818e9d;position:absolute;right:0;top:50%;transform:translateY(-50%);}
    }
  }
}

@media only screen and (max-width: 1280px) {
  .xg-complete{padding-left:40px;padding-right:40px;}
}
@media only screen and (max-width: 750px) {
  .xg-complete{padding:30px 20px;
    .xg-box{padding:20px 15px;}
  }
  .xg-complete .message .info{min-width:inherit;padding-top:5px;}
  .xg-complete .body{min-height:inherit;padding-top:40px;padding-bottom:20px;}
  .xg-complete .message .icon{width:50px;height:50px;font-size:34px;margin:0 auto 15px;}
  .xg-complete .message .text1{font-size:18px;margin-bottom:40px;text-align:center;}
  .xg-complete .message .detail{margin-bottom:20px;}
  .xg-complete .message .text3{font-size:12px;}
  .xg-complete .itembox{margin-right:0;}
  .xg-complete .itembox .item{font-size:12px;width:32%;margin-right:0;margin-bottom:10px;text-align:center;
    &:nth-child(3n-1){margin-left:2%;margin-right:2%;}
    &:nth-child(3n):after{display:none;}
  }
  .xg-complete .message{flex-wrap:wrap;}
}
/* ==================== 改版样式 end ==================== */

</style>

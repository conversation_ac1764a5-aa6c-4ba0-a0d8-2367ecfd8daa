<template>
  <div class="payment">
    <!--导航-->
    <!-- <headback></headback> -->

    <div class="payment-content">
      <div class="zhong">
        <div class="payment-top">
          <h4>{{ tips }}</h4>
          <div class="payment-top-text" v-show="Object.keys(orderInfo).length !== 0">
            <p>订单编号 : {{ orderInfo.order_no }}</p>
            <p>实付金额 : <span>{{ orderInfo.order_amount }}</span></p>
          </div>
          <div class="complete-zhong">
<!--            <button>查看订单详情</button>-->
            <button @click="goHome">返回首页</button>
          </div>
          <div class="complete-bottom">
            <p>提示：您正在进行犀光相关付费服务，若有疑问，请联系相关客服人员进行处理！</p>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
  import headback from "../headback";

  export default {
    name: "complete",
    components:{
      headback,
    },
    data(){
      return{
        tips: '',
        orderInfo: {},
      }
    },
    computed: {

    },
    mounted(){
      this.getOrderInfo();
    },
    methods:{
      getOrderInfo(){
        let orderNo = this.$route.query.order_no;
        if (orderNo=='' || orderNo == undefined) {
          this.$message.error({
            showClose: true,
            message: '订单号错误!'
          });
          this.tips = '订单号错误!';
          return false;
        }

        this.$axios.post(this.url + '/api/get-order', {
          order_no:orderNo
        }).then(res => {
          if (res.data.code === 200) {
            this.orderInfo = res.data.data;
            if (this.orderInfo.status == 1) {
              this.tips = '支付成功';
            } else {
              this.tips = '支付失败';
            }
          } else if (res.data.code === 4001) {
            localStorage.removeItem('UserInfo');
            localStorage.removeItem('Authorization');
            this.$router.push({name:"login"})
          }  else {
            this.$message.error({
              showClose: true,
              message: res.data.msg
            });
            this.tips = res.data.msg;
          }
        }).catch(err => {});
      },

      goHome() {
        this.$router.push('/');
      }
    },
    created() {
    },
  }

</script>

<style scoped lang="scss">
  .blue {
    border: 2px solid #E6A23C!important;
  }
  //导航
  .navbar-expand-lg .navbar-collapse{
    display:flex;
    justify-content:space-between;
  }
  .navbar{
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    width: 100%;
    /*position: relative;*/
    /*top: 0;*/
    z-index: 99;
    /*position: absolute;*/
    /*top:0;*/
    /*left: 0;*/
  }
  button{
    outline: none;
  }
  .bg-info{
    /*background-color: rgba(0,0,0,0) !important;*/
    background-color: rgb(17, 19, 42)!important;
  }
  .navbar-dark .navbar-brand,.navbar-dark .navbar-nav .nav-link{
    color: white;
  }
  .btn-success{
    color: white!important;
    background: none!important;
    border: none!important;
    margin-top: 5px;
  }

  .payment-content{
    margin: 40px 15%;
    background: white;
    padding: 50px 0;
    .zhong{
      width: 70%;
      margin: 0 auto;
      .payment-top{
        width: 80%;
        margin: 0 auto;
        h4{
          text-align: left;
          margin-bottom: 10px;
          line-height: 80px;
          font-size: 40px;
        }
        .payment-top-text{
          /*background: rgba(252, 144, 0, 0.1);*/
          color: #24416b;
          text-align: left;
          padding: 15px;
          padding-left: 0;
          p{
            margin: 0;
            span{
              color: red;
              font-size: 20px;
              padding-left: 5px;
            }
          }
        }
        .complete-zhong{
          margin-top: 20px;
          text-align: left;
          border-bottom: 1px solid #eeeeee;
          padding-bottom: 50px;
          button{
            padding:10px 20px;
            background: #E6A23C;
            outline: none;
            border: none;
            color: white;
            margin-right: 20px;
          }
        }
        .complete-bottom{
          text-align: left;
          margin: 50px 0;
        }
      }
    }
  }


  @media only screen and (max-width: 900px){
    .navbar-expand-lg .navbar-collapse{
      display:block;
    }
    .navbar-dark .navbar-toggler{
      color: rgba(0,0,0,0.5);
      border-color: rgba(0,0,0,0.5);
    }
    /deep/.navbar-dark .navbar-toggler-icon{
      background-image: url("../../assets/img/nav.png");
    }
    .payment-content{
      margin: 40px 5%;
    }
  }



</style>

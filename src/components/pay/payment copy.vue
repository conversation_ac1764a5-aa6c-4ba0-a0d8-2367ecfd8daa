<template>
  <div class="payment">
    <!--导航-->
    <!-- <headback></headback> -->

    <div class="payment-content">
      <div class="payment-top">
        <h4>支付订单</h4>
        <p>{{ orderTips }}</p>
      </div>
      <div class="payment-zhong">
        <el-row  :gutter="20">
          <el-col :span="6" :xs="24"  class="left">
            <img :src="orderInfo.product_img"/>
          </el-col>
          <el-col :span="18" :xs="24"  class="right">
            <p><span>订单号:</span>{{orderInfo.order_no}}</p>
            <p><span>订单名称:</span>{{orderInfo.product_name}}</p>
            <p><span>订单价格:</span>￥{{orderInfo.total_amount}}</p>
          </el-col>
        </el-row>
      </div>
      <div class="payment-bottom">
        <h6>支付方式<span>选择适合支付方式</span>
          <span @click="couponCodeShow()" href="#"><a href="javascript:void(0);" style="color: #cf9236">[使用优惠码{{couponCode?':'+couponCode:''}}]</a></span>
        </h6>
        <div class="pick">
          <div class="pick-left" v-for="(todo, index) in todos" v-on:click="addClass(todo.id)" v-bind:class="{ blue:todo.id==current}">
            <img :src="todo.img"/>
          </div>
        </div>
      </div>

      <div class="zhifu">
        <p>应付:<span>￥{{orderInfo.order_amount}}</span></p>
        <el-button type="text" @click="getPayQrImg()" v-loading.fullscreen.lock="fullscreenLoading" v-if="orderInfo.status === 0">支付订单</el-button>
        <el-button type="text" v-else-if="orderInfo.status === 2" style="background-color: #72767b">支付订单</el-button>
        <el-button type="text" @click="tips()" v-else>支付成功</el-button>
      </div>

<!--      优惠码-->
      <el-dialog title="优惠码" :visible.sync="dialogFormVisible" :show-close="false">
        <el-form :model="couponForm">
          <el-form-item>
            <el-input v-model="couponForm.code" placeholder="请输入优惠码" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="coupon()">确 定</el-button>
        </div>
      </el-dialog>

      <!--支付弹窗-->
      <el-dialog
        title="扫码支付"
        :visible.sync="dialogVisible"
        width="30%">
        <div style="width: 60%; margin: 0 auto;">
          <img style="width: 100%" :src="payQrcode"/>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
        </span>
      </el-dialog>

    </div>
    <div v-html="payInfo" ref="pay"></div>

  </div>
</template>

<script>
    import headback from "../headback";

    export default {
      name: "payment",
      components:{
        headback,
      },
      data(){
        return{
          orderTips:'订单已提交，请在 30 分钟内完成支付！逾期订单将被取消。',

          item:{},
          current:0,
          todos:[
            {id:1, img: require('../../assets/img/wechatpay.jpg') },
            {id:2, img: require('../../assets/img/alipay.png') },
          ],
          dialogVisible: false, //支付弹窗
          fullscreenLoading: false,
          orderInfo:{},
          payQrcode:"",
          payInfo:"",

          dialogFormVisible:false,
          couponPrice:0,
          couponCode:'',
          couponForm: {
            code: ''
          },


          timer:0
        }
      },
      beforeMount(){
        this.getOrder();
      },
      mounted(){
        // 支付监听
        this.timer = setInterval(() => {
          this.getOrder()
        }, 3000);
      },
      beforeDestroy() {//页面关闭前关闭定时器
        clearInterval(this.timer)
      },
      methods:{
        addClass:function(index){
          this.current=index;
        },

        getOrder() {
          this.$axios.post(this.url + '/api/get-order', {
            order_no:this.$route.query.order_no
          }).then(res => {
            if (res.data.code === 200) {
              let amount = res.data.data.total_amount - this.couponPrice
              res.data.data.order_amount = amount > 0 ? amount : 0;
              this.orderInfo = res.data.data;
              if (this.orderInfo.status === 1) {
                clearInterval(this.timer)
                this.orderTips = '订单已支付成功。'
                this.dialogVisible = false;
              } else if (this.orderInfo.status === 2) {
                clearInterval(this.timer)
                this.orderTips = '订单已关闭，请重新下单进行购买操作！';
                this.dialogVisible = false;
              } else {
                this.orderTips = '订单已提交，请在 30 分钟内完成支付！逾期订单将被取消。'
              }
            } else if (res.data.code === 4001) {
              localStorage.removeItem('UserInfo');
              localStorage.removeItem('Authorization');
              this.$router.push({name:"login"})
              clearInterval(this.timer)
            } else {
              clearInterval(this.timer)
            }
          }).catch(err => {
            clearInterval(this.timer)
          });
        },
        getPayQrImg() {
          if (this.current === 0) {
            this.$message.warning({
              showClose: true,
              message: '请选择支付方式！'
            });
            return false;
          }
            this.fullscreenLoading = true;
            this.$axios.post(this.url + '/api/order-pay', {
              order_no:this.$route.query.order_no,
              coupon_code:this.couponForm.code,
              pay_method:this.current // 1-微信,2-支付宝
            }).then(res => {
              this.fullscreenLoading = false;
              if (res.data.code === 200 && res.data.msg == '成功') {
                this.$message.success({
                  showClose: true,
                  message: res.data.msg
                });

                this.$router.go(-1);
              } else if (res.data.code === 200) {
                if (res.data.msg == '支付码生成成功') {
                  this.payQrcode = res.data.data.payCodeImg;
                  this.dialogVisible = true;
                } else {
                  this.payInfo = res.data.data;
                  this.$nextTick(() => {
                    this.$refs.pay.children[0].submit();
                  });
                }
              } else if (res.data.code === 4001) {
                localStorage.removeItem('UserInfo');
                localStorage.removeItem('Authorization');
                this.$router.push({name:"login"})
              } else {
                this.$message.warning({
                  showClose: true,
                  message: res.data.msg
                });
              }
            }).catch(err => {});
        },
        tips(){
          this.$message.success({
            showClose: true,
            message: '订单已支付成功，无需再次购买！'
          });
        },
        couponCodeShow(){
          this.dialogFormVisible = true;
          this.couponForm.code = '';
        },
        coupon(){
          // this.dialogFormVisible = true
          if (this.couponForm.code!=''&&this.couponForm.code!=undefined&&this.couponForm.code!=null) {
            this.$axios.post(this.url + '/api/coupon-code',{
              code: this.couponForm.code
            }).then(res => {
              if (res.data.code === 200) {
                this.$nextTick(()=> {
                  this.couponPrice = res.data.data
                  this.couponCode = this.couponForm.code
                  let amount = this.orderInfo.total_amount - res.data.data
                  this.orderInfo.order_amount = amount > 0 ? amount : 0
                })
              } else if (res.data.code === 4001) {
                localStorage.removeItem('UserInfo');
                localStorage.removeItem('Authorization');
                this.$router.push({name:"login"})
              } else {
                this.$message.warning({
                  showClose: true,
                  message: res.data.msg
                });
              }
            }).catch(err => {
            }).finally(()=>{
              this.dialogFormVisible = false
            });
          } else {
            this.$message.warning({
              showClose: true,
              message: '请填写有效优惠码'
            });
          }
        },
        // open() {
        //   this.$confirm('<img style="width:100%;" src="https://hbimg.huabanimg.com/4c71e44c64c1770d74f1b96c85ec46d2696fb0582afb1-PYigIN_fw658/format/webp"/>', '扫码支付', {
        //     dangerouslyUseHTMLString: true,
        //     center: true,
        //     confirmButtonText: '',
        //     cancelButtonText: '',
        //   });
        // }
        handleClose(done) {
          this.$confirm('确认关闭？')
            .then(_ => {
              done();
            })
            .catch(_ => {});
        }
      },
      created() {
      },
    }

</script>

<style scoped lang="scss">
  .blue {
    border: 2px solid #E6A23C!important;
  }
  //导航
  .navbar-expand-lg .navbar-collapse{
    display:flex;
    justify-content:space-between;
  }
  .navbar{
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    width: 100%;
    /*position: relative;*/
    /*top: 0;*/
    z-index: 99;
    /*position: absolute;*/
    /*top:0;*/
    /*left: 0;*/
  }
  button{
    outline: none;
  }
  .bg-info{
    /*background-color: rgba(0,0,0,0) !important;*/
    background-color: rgb(17, 19, 42)!important;
  }
  .navbar-dark .navbar-brand,.navbar-dark .navbar-nav .nav-link{
    color: white;
  }
  .btn-success{
    color: white!important;
    background: none!important;
    border: none!important;
    margin-top: 5px;
  }

.payment-content{
  margin: 40px 15%;
  background: white;
  padding: 40px 0;
  .payment-top{
    width: 80%;
    margin: 0 auto;
    h4{
      text-align: left;
      margin-bottom: 10px;
      line-height: 40px;
    }
    p{
      background: rgba(252, 144, 0, 0.1);
      color: #24416b;
      text-align: left;
      padding: 15px;
      margin: 10px 0;
    }
  }
  .payment-zhong{
    width: 80%;
    margin: 20px auto;
    background: #f9f9f9;
    .left{
      height: 120px;
      img{
        width: 100%;
        height: 100%;
      }
    }
    .right{
      padding-left: 30px;
      padding-top: 10px;
      p{
        line-height: 20px;
        text-align: left!important;
      }
    }
  }
  .payment-bottom{
    text-align: left;
    width: 80%;
    margin: 20px auto;
    h6{
      font-size: 16px;
      span{
        font-size: 12px;
        color: #666666;
        margin-left: 10px;
      }
    }
    .pick{
      margin-top: 20px;
      display: flex;
      .pick-left, .pick-right{
        border: 2px solid #eeeeee;
        margin-right: 20px;
        line-height: 40px;
        padding: 5px 20px;
        cursor: pointer;
        img{
          width: 40px;
        }
      }
    }
  }
  .zhifu{
    width: 80%;
    margin: 0 auto;
    text-align: right;
    p{
      span{
        color: red;
        font-size: 20px;
      }
    }
    button{
      padding:10px 20px;
      background: #E6A23C;
      outline: none;
      border: none;
      .el-button--text{
        color: white;
      }
    }
    .dialog-footer{
      text-align: center!important;
    }
  }
}

  @media only screen and (max-width: 900px){
    .navbar-expand-lg .navbar-collapse{
      display:block;
    }
    .navbar-dark .navbar-toggler{
      color: rgba(0,0,0,0.5);
      border-color: rgba(0,0,0,0.5);
    }
    /deep/.navbar-dark .navbar-toggler-icon{
      background-image: url("../../assets/img/nav.png");
    }
    .payment-content{
      margin: 40px 5%;
    }
  }
</style>

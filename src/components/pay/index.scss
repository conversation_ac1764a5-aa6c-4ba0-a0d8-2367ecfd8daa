
/* ==================== 通用 流程样式 end ==================== */
.xg-process {
    display:flex;align-items:center;
    padding-right:20px;margin-bottom:40px;
    .item {
        font-size:14px;
        width:32%;position:relative;font-weight:bold;
        height:40px;line-height:40px;color:#000;
        background:#e6e9ec;margin-left:24px;
        &:first-child{
            .first:after{border-color:#e6e9ec;}
        }
        // 失败样式
        &.danger{
        color:#fff;
        background:#ff0000;
            .first:after{border-bottom-color:#ff0000;border-right-color:#ff0000;}
            .first:before{border-top-color:#ff0000;border-right-color:#ff0000;}
            .last{background:#ff0000;}
        }
        // 激活样式
        &.active{
        color:#fff;
        background:#0d6efd;
            .first:after{border-bottom-color:#0d6efd;border-right-color:#0d6efd;}
            .first:before{border-top-color:#0d6efd;border-right-color:#0d6efd;}
            .last{background:#0d6efd;}
        }
        .first,
        .last{position:absolute;}
        .first{
            left: -20px;
            top: 0;
            width: 40px;
            height: 40px;
            &:before,
            &:after{content:"";display:block;width:0;height:0;border-width:20px;border-style:solid;position:absolute;left:0;top:0;}
            &:before{border-color:#e6e9ec #e6e9ec transparent transparent;}
            &:after{border-color:transparent #e6e9ec #e6e9ec transparent;}
        }
        .last{right:-14px;top:0;width:28px;height:28px;transform:translateY(22%) rotate(45deg);background:#e6e9ec;}
        .text{position:relative;z-index:3;}
    }
}
@media only screen and (max-width: 750px) {
    .xg-process{flex-wrap:wrap;padding-right:0;border-radius:5px;overflow:hidden;margin-bottom:20px;}
    .xg-process .item{margin-left:0;font-size:12px;font-weight:400;}
    .xg-process .item .first,
    .xg-process .item .last{display:none;}
}

/* ==================== 通用 流程样式 end ==================== */
/* ==================== 通用 按钮样式 end ==================== */
.xg-btn-group{
    display:flex;align-items:center;
    .xg-btn{
        cursor: pointer;outline:none;width:160px;height:50px;line-height:50px;text-align:center;font-size:14px;border-radius:4px;border:1px solid #dbdddf;
        &.default{color:#a3acb6;background:#fafafa;margin-right:25px;}
        &.primary{border-color:#0d6efd;background:#0d6efd;color:#fff;}
    }
}
@media only screen and (max-width: 750px) {
  .xg-btn-group .xg-btn{width:48%;height:auto;line-height:inherit;padding:10px;font-size:12px;}
  .xg-btn-group .xg-btn.default{margin-right:4%;}
}
/* ==================== 通用 按钮样式 end ==================== */
a{
  text-decoration: none!important;
}
ul,li{
  list-style: none;
}
.el-tabs__item.is-active{
  color: #0d6efd!important;
}
.el-tabs__item:hover{
  color: #0d6efd!important;
}
.el-tabs__active-bar{
  background-color:  #0d6efd!important;
}
.el-button--primary{
  background-color: #0d6efd!important;
  border-color: #0d6efd!important;
}
.my-reply .reply-info .reply-input{
  border: 2px solid #0d6efd!important;
}
.el-button--text{
  color: #0d6efd!important;
}
.dropdown-toggle::after{
  display: none!important;
}
.headee{
  .btn-success{
    color: white!important;
    background: none!important;
    border: none!important;
    margin-top: 5px;
  }
}

.loginwap , .forget{
  .el-button-group{
    width: 100%;
  }
}
//navtop{
.navtop{
  .navbar-dark .navbar-nav .nav-link{
    color: white!important;
  }
  .dropdown-menu{
    background:#11132a;
    border: none;
    top: 45px;
  }
  .dropdown-item{
    color: white!important;
    font-size: 14px;
  }
  .dropdown-item:hover{
    background:none!important;
  }
}

//导航
.headee{
  .navbar-dark .navbar-nav .nav-link{
    color: white!important;
  }
  .dropdown-menu{
    background:rgba(255,255,255,0.2);
    border: none;
  }
  .dropdown-item{
    color: white!important;
    font-size: 14px;
  }
  .dropdown-item:hover{
    background:none!important;
  }
}
//首页轮播
.rotation{
  .el-carousel__container{
    height: 100%;
    button:not(:disabled), [type=button]:not(:disabled), [type=reset]:not(:disabled), [type=submit]:not(:disabled){
      display: none;
    }
  }
  .el-carousel__indicators--outside{
    display: none!important;
  }
  .el-carousel__indicators--horizontal{
    display: none!important;
  }
}
//重置密码安全问题
.reset{
  .el-input__inner{
    width: 100%;
    border: none;
    border-bottom: 1px solid rgba(0,0,0,0.16);
    outline: none;
    color: rgba(0,0,0,0.88)!important;
    font-size: 12px;
    border-radius: 0;
  }
  .el-input__inner::placeholder{
    color: rgba(0,0,0,0.66)!important;
  }
}

.security{
  .el-card__body{
    padding: 20px 10px!important;
  }
}
.communityActivity{
  .el-button{
    color: white!important;
    //border: 1px solid #0d6efd!important;
  }
}


.v-modal{opacity: 0!important;}
.el-progress-bar__inner{
  background-color: #0d6efd!important;
}
.el-button--warning{
  color: white!important;
}
.el-pagination.is-background .el-pager li:not(.disabled).active{
  background-color: #0d6efd!important;
}
.el-pagination{
  margin-top: 30px;
}
.el-table{
  // color: #0d6efd!important;
  // cursor: pointer;
}
.el-menu-item.is-active{
  color: #E6A23C !important;
}

.xl-chrome-ext-bar{
  display: none!important;
}

// .el-dialog__body {
//   padding: 10px 20px!important;
// }
.banner-home{
  .el-carousel__mask {
    background-color: transparent!important;
  }
}

.newss{
  .el-card{
    border: 0!important;
    background-color: transparent!important;
  }
}

//视频详情页
.coursepage{
  .title-left{
    .el-collapse{
      padding: 10px;
      //background: white;
    }
    .el-collapse-item__header{
      background: #e7e7e7;
      font-size: 16px;
      padding: 10px;
      border-bottom: 1px solid #dbdbdb;
    }
    .el-collapse-item__wrap{
      background: #e7e7e7;
      padding: 10px;
      padding-top: 0;
    }
    .el-collapse-item__content{
      padding-bottom: 0;
    }
  }
  .comments{
    .el-collapse-item__header{
      background: #f5f5f5;
      padding: 10px;
    }
    .el-collapse-item__content{
      padding-bottom: 0;
    }
    .el-tabs__nav{
      margin-left: 10px;
    }
    .el-tabs{
      margin-left: 20px;
    }
  }

  .topvideo{
    .title-left{
      ul{
        padding: 0;
        li{
          line-height: 20px!important;
          overflow: visible!important;
          word-break:break-all!important;
          white-space: normal!important;
          height:auto!important;
          margin-top: 10px;
          display: block!important;
        }
        li:hover{
          color: #E6A23C;
        }
      }
    }
  }
}

// 注册弹窗
// .el-dialog {
//   max-height: 600px;
//   display: flex;
//   flex-direction: column;
// }
// .el-dialog__body {
//   overflow: auto;
// }

//新闻详情页
.newstext{
  .mainp{
    text-align: left;
    img{
      max-width: 90%;
    }
  }
}
// 课程简介
.comments-left{
  .introduce{
    .mainp{
      text-align: left;
      img{
        max-width: 90%;
      }
    }
  }
}
// 关于我们
.aboutus{
  .mainp{
    text-align: left;
    img{
      max-width: 90%;
    }
  }
}

//购买页
.payment{
  // .el-button--text{
  //   color: white!important;
  // }
  // .el-dialog__footer{
  //   text-align: center;
  // }
  // .el-dialog{
  //   width: 20%!important;
  // }
}

//插件页
.el-button--warning{
  background: none!important;
  border: none!important;
}
.el-button--warning:focus, .el-button--warning:hover {
  background: none!important;
  border: none;
}
a::after{
  border: none!important;
}

// @media only screen and (max-width: 900px){
//   .el-dialog{
//     width: 90%!important;
//     margin-top:20vh!important;
//   }
//   .dropdown-menu{
//     text-align: center!important;
//     background: #121434 !important;
//   }
//   //购买页
//   .payment{
//     .el-dialog{
//       width: 90%!important;
//     }
//   }
// }

/* ==================== 通用样式 start ==================== */
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

*:before,
*:after {
  vertical-align: middle;
  box-sizing: border-box;
}
img {
  vertical-align: middle;
  max-width: 100%;
}

input:focus,
select:focus,
textarea:focus {
    outline: -webkit-focus-ring-color auto 0;
}

button,
input {
    border: 0;
    outline: none;
    background: none;
}

/* 图片统一动画(图片放大） */
.xg-image {
  overflow: hidden;
  &:hover {
      img {
          transform: scale(1.1);
      }
  }
  img {
      transition: all .4s;
      width: 100%;
  }
}
/* ==================== 通用样式 end ==================== */
/* ==================== 文本行数限制 start ==================== */
.xg-line-1 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.xg-line-2 {
  -webkit-line-clamp: 2;
}
.xg-line-3 {
  -webkit-line-clamp: 3;
}
.xg-line-4 {
  -webkit-line-clamp: 4;
}
.xg-line-5 {
  -webkit-line-clamp: 5;
}
.xg-line-6 {
  -webkit-line-clamp: 6;
}
.xg-line-2,
.xg-line-3,
.xg-line-4,
.xg-line-5,
.xg-line-6 {
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box; // 弹性伸缩盒
  -webkit-box-orient: vertical; // 设置伸缩盒子元素排列方式
}
/* ==================== 文本行数限制 end ==================== */
/* ==================== flex通用布局 start ==================== */
.xg-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.xg-flex-wrap {
  flex-wrap: wrap;
}
.xg-flex-nowrap {
  flex-wrap: nowrap;
}
.xg-flex-col {
  flex-direction: column;
}
.xg-grow-1 {
  flex-grow: 1;
}
.xg-col-center {
  align-items: center;
}
.xg-col-top {
  align-items: flex-start;
}
.xg-col-bottom {
  align-items: flex-end;
}
.xg-row-center {
  justify-content: center;
}
.xg-row-left {
  justify-content: flex-start;
}
.xg-row-right {
  justify-content: flex-end;
}
.xg-row-between {
  justify-content: space-between;
}
.xg-row-around {
  justify-content: space-around;
}
/* ==================== flex通用布局 end ==================== */
/* ==================== 通用布局 start ==================== */
@media screen and (min-width: 751px) {
  .xg-row-2,
  .xg-row-3,
  .xg-row-4,
  .xg-row-5,
  .xg-row-6 {
      display: flex;
      align-items: stretch;
      justify-content: flex-start;
      flex-wrap: wrap;
  }

  // 一行两列
  .xg-row-2>.xg-col {
      width: 48%;

      &:nth-child(2n) {
          margin-left: 4%;
      }

      &:nth-child(2)~.xg-col {
          margin-top: 4%;
      }
  }

  // 一行三列
  .xg-row-3>.xg-col {
      width: 32%;

      &:nth-child(3n-1) {
          margin-left: 2%;
          margin-right: 2%;
      }

      &:nth-child(3)~.xg-col {
          margin-top: 2%;
      }
  }

  // 一行四列
  .xg-row-4>.xg-col {
      width: 23.5%;
      margin-right: 2%;

      &:nth-child(4n) {
          margin-right: 0%;
      }

      &:nth-child(4)~.xg-col {
          margin-top: 2%;
      }
  }

  // 一行五列
  .xg-row-5>.xg-col {
      width: 18.4%;
      margin-right: 2%;

      &:nth-child(5n) {
          margin-right: 0%;
      }

      &:nth-child(5)~.xg-col {
          margin-top: 2%;
      }
  }

  // 一行六列
  .xg-row-6>.xg-col {
      width: 15%;
      margin-right: 2%;

      &:nth-child(6n) {
          margin-right: 0%;
      }

      &:nth-child(6)~.xg-col {
          margin-top: 2%;
      }
  }
}

@media screen and (max-width: 751px) {
  .xg-row-2,
  .xg-row-3,
  .xg-row-4,
  .xg-row-5,
  .xg-row-6 {
      display: flex;
      align-items: stretch;
      justify-content: flex-start;
      flex-wrap: wrap;
  }
  // 一行一列
  .xg-row-2 .xg-col {
      width: 100%;

      &:nth-child(1)~.xg-col {
          margin-top: 2%;
      }
  }
  // 一行两列
  .xg-row-3>.xg-col,
  .xg-row-4>.xg-col,
  .xg-row-5>.xg-col,
  .xg-row-6>.xg-col {
      width: 48%;

      &:nth-child(2n) {
          margin-left: 4%;
      }

      &:nth-child(2)~.xg-col {
          margin-top: 4%;
      }
  }
}
/* ==================== 通用布局 end ==================== */
/* 容器大小 */
.xg-box {
  max-width: 1220px;
  margin: 0 auto;
  color: #333;
  .title{text-align:left;}
  p {
      margin-bottom: 0;
  }
  
  ul {
    margin-top: 0;
    margin-bottom: 0;
    margin-left: 0;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
}
/* ==================== 页面自定义样式 start ==================== */
@media screen and (min-width: 751px) {
  .pc-none,
  .mob-nav,
  .pc-none {
      display: none !important;
  }
}

@media screen and (max-width: 1680px) {
  .xg-box {
  }
}
@media screen and (max-width: 751px) {
  body {
      margin-top: 60px!important;
  }
  .m-none,
  .pc-nav {
      display: none !important;
  }
  .xg-box {
      padding: 30px 20px;
  }
}
/* ==================== 页面自定义样式 end ==================== */
/* ==================== 内页 - 通用分页 start ==================== */
// 分页样式
.xg-pagination {
  .el-input__inner,
  .el-pager li,
  .btn-prev,
  .btn-next {
    width: 40px!important;
    height: 40px!important;
    line-height: 40px!important;
    border-radius: 2px;
    font-size: 15px;
    color: #a7afb9;
    background: #fff;
    font-weight: 400;
  }
  .el-pager li {
    margin-left: 9px;
    margin-right: 9px;
    &.active {
      background: #0d6efd !important;
      color: #fff;
    }
  }
  .el-pagination__jump {
    font-size: 15px;
    color: #4a5764;
  }
}

@media screen and (max-width: 751px) {
  .xg-pagination {
    margin-top:30px;
    .el-input__inner,
    .el-pager li,
    .btn-prev,
    .btn-next {
      min-width:24px!important;
      width: 24px!important;
      height: 30px!important;
      line-height: 30px!important;
      font-size: 12px;
    }
    .el-pager li {
      margin-left: 2px;
      margin-right: 2px;
    }
    .el-pagination__jump{display:none!important;}
  }
}
/* ==================== 内页 - 通用分页 end ==================== */
/* ==================== 内页通用容器 start ==================== */
.xg-ny {
  background: #f4f4f4;
}

@media only screen and (max-width: 1680px) {
  .xg-ny{padding-left:40px;padding-right:40px;}
}
@media screen and (min-width: 751px) {
  .xg-ny {
    padding-top: 50px;
    padding-bottom: 80px;
  }
}
@media screen and (max-width: 751px) {
  .xg-ny{padding-left:0;padding-right:0;}
}
/* ==================== 内页通用容器 end ==================== */
/* ==================== 内页标题 start ==================== */
.xg-ny-title {
  text-align: left;
  font-size: 32px;
  color: #0d6efd;
  line-height: 1.2;
  padding-bottom: 30px;
  border-bottom: 2px solid #e6e6e6;
  margin-bottom: 35px;
  width:100%;
}

@media screen and (max-width: 751px) {
  .xg-ny-title {
    padding-bottom: 15px;
    margin-bottom: 15px;
    font-size: 22px;
    font-weight: bold;
  }
}
/* ==================== 内页标题 end ==================== */
/* ==================== 内页 - 通用标签页样式 start ==================== */
.xg-tabs{
  // 选项卡样式
  .el-tabs__header {
    margin-bottom: 30px;
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }
  .el-tabs__active-bar {
    display: none;
  }
  .el-tabs__item {
    margin-right: 20px;
    height: auto;
    line-height: inherit;
    padding: 20px !important;
    font-size: 14px;
    color: #000;
    border-radius: 4px;
    text-align: center;
    &:last-child {
      margin-right: 0;
    }
    &.is-active {
      background: #0d6efd;
      color: #fff !important;
    }
  }
  .el-tabs__nav-prev,
  .el-tabs__nav-next {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 23px;
    height: 23px;
    line-height: 23px;
    border-radius: 100%;
    background-color: #fff;
    bottom: auto;
    top: 50%;
    transform: translateY(-50%);
    box-shadow: -3px 4px 7px 0px rgba(0, 0, 0, 0.08);
    &:hover {
      background-color: #0d6efd;
      &:after {
        border-left-color: #fff;
      }
    }
    &:after {
      content: "";
      display: block;
      width: 0;
      height: 0;
      border-top: 4px solid transparent;
      border-left: 4px solid #0d6efd;
      border-bottom: 4px solid transparent;
    }
    & > i {
      display: none;
    }
  }
  .el-tabs__nav-prev {
    &:hover {
      &:after {
        border-right-color: #fff;
      }
    }
    &:after {
      border-left: 0;
      border-right: 4px solid #0d6efd;
    }
  }
  .el-tabs__nav-wrap {
    padding-left: 40px;
    padding-right: 40px;
  }
}
@media screen and (min-width: 751px) {
  .xg-tabs .el-tabs__nav-wrap {
    padding-left: 40px!important;
    padding-right: 40px!important;
  }
}
@media screen and (max-width: 751px) {
  .xg-tabs .el-tabs__header {
    margin-bottom: 20px;
  }
  .xg-tabs .el-tabs__item {
    padding: 10px 15px !important;
    margin-right: 5px;
  }
  .xg-tabs .el-tabs__nav-wrap {
    padding-left: 30px!important;
    padding-right: 30px!important;
  }
}
/* ==================== 内页 - 通用标签页样式 end ==================== */
// 去除input 自动填充的背景色
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus, 
input:-webkit-autofill:active {
  border:0;
  transition-delay: 999999999s;
  transition: color  999999999s ease-out, background-color  999999999s ease-out;
  -webkit-transition-delay:  999999999s;
  -webkit-transition: color  999999999s ease-out, background-color  999999999s ease-out;
  // -webkit-text-fill-color: #fff;
}
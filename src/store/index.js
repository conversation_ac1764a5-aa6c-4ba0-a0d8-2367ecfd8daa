import Vue from 'vue';
import Vuex from 'vuex';
Vue.use(Vuex);

const store = new Vuex.Store({
  state: {
    // 存储token
    Authorization: localStorage.getItem('Authorization') ? localStorage.getItem('Authorization') : '',
    UserInfo: localStorage.getItem('UserInfo') ? localStorage.getItem('UserInfo') : '',
  },

  mutations: {
    // 修改token，并将token存入localStorage
    changeLogin (state, user) {
      state.Authorization = user.Authorization;
      state.UserInfo = user.UserInfo;
      localStorage.setItem('Authorization', user.Authorization);
      localStorage.setItem('UserInfo', JSON.stringify(user.UserInfo));
    },
    localClear (key) {
      // console.log(key);
      localStorage.removeItem(key);
    }
  }
});

export default store;

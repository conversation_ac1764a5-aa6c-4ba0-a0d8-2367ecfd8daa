import Vue from 'vue'
import Router from 'vue-router'
// user
import userIndex from "../components/user/index";// 用户
import userCourse from "../components/user/course";// 课程
import userInfo from "../components/user/info";// 基础信息
import userCertification from "../components/user/certification";  //实名认证
import userSecurity from "../components/user/security";  //安全设置
import userSocialite from "../components/user/socialite";// 第三方登陆
import userOrder from "../components/user/order"; //订单管理
import userOrderDetail from "../components/user/orderDetail"; //订单详情
import verifyResult from "../components/user/emailverify"; //订单管理

// home
import homeindex from "../components/home";  //首页
// course
import course from "../components/curriculum/course"; //课程管理页
import payment from "../components/pay/payment"//购买页面
import complete from "../components/pay/complete"//购买成功/失败页面
import coursepage from "../components/curriculum/coursepage";
// plug
import plug from '../components/plug/plug' //插件
import plugcate from '../components/plug/plugcate'
// news
import news from "../components/news/news";  //新闻首页
import newsdetail from "../components/news/newsdetail"; //新闻详情页
// aboutus
import about from "../components/about";  //关于
// ai
import ai from "../components/ai/ai";  //ai
import aiGallery from "../components/ai/gallery";  //ai作品
import aiPlug from "../components/ai/plug";  //ai插件
import aiChat from "../components/ai/chat";  //ai问答
import aiRender from "../components/ai/render";  //ai渲染
import aiNews from "../components/ai/news";  //ai资讯
import aiNewsDetail from "../components/ai/newsdetail";  //ai资讯
import aiVip from "../components/ai/vip";  //ai资讯


// login
import login from "../components/auth/login"  //登录
import register from "../components/auth/register"; //注册
import verification from "../components/auth/verification"; //手机号验证登录
import forget from "../components/auth/forget"; //忘记密码
import reset from "../components/auth/reset"; //忘记密码
// 周边
import periphery from "../components/periphery"; //周边分类页
import peripheryDetails from "../components/periphery/details"; //周边详情页
// 搜索结果
import search from "../components/search"


// 光子账户
import userIntegral from "../components/user/integral"
// 消息回复
import message from "../components/user/message"
// 收货地址
import address from "../components/user/address"


//投稿管理
import contribute from "../components/user/contribute"
//粉丝信息

import userfans from "../components/fans/userfans"
import fansIndex from "../components/fans/index"

import userfanslist from "../components/fans/userfanslist"
import attentionlist from "../components/fans/attentionlist"






Vue.use(Router)

const router = new Router({
  // mode: 'history',
  // base: '/',
  routes: [
    {
      path: '/',
      name: 'homeindex',
      meta: {
        requireAuth: false,
      },
      component: homeindex,
    },
    {
      path: '/index',
      name: 'index',
      meta: {
        requireAuth: false,
      },
      component: homeindex,
    },



    // login
    {
      path: '/login',
      name: 'login',
      meta: {
        requireAuth: false,
        isAuthRoute: true
      },
      component: login,
    }, {
      path: '/register',
      name: 'register',
      meta: {
        requireAuth: false,
        isAuthRoute: true
      },
      component: register
    }, {
      path: '/verification',
      name: 'verification',
      meta: {
        requireAuth: false,
        isAuthRoute: true
      },
      component: verification
    }, {
      path: '/forget',
      name: 'forget',
      meta: { requireAuth: false },
      component: forget
    }, {
      path: '/reset-password',
      name: 'reset-password',
      meta: { requireAuth: false },
      component: reset
    },
    {
      path: '/plug/:id',
      name: 'plug',
      component: plug,
      meta: { requireAuth: false },
      childern: []
    },
    {
      path: '/plug',
      name: 'plugcate',
      component: plugcate,
    },
    // news
    {
      path: '/news',
      name: 'news',
      meta: { requireAuth: false },
      component: news,
      children: []
    }, {
      path: '/news/:id',
      name: 'newsdetail',
      meta: { requireAuth: false },
      component: newsdetail
    },
    // email-verify
    {
      path: '/email-verify',
      name: 'email-verify',
      meta: { requireAuth: false },
      component: verifyResult
    },
    //aboutus
    {
      path: '/help',
      name: 'help',
      component: about
    },
    //ai
    {
      path: '/ai',
      name: 'ai',
      component: ai,
      meta: { requireAuth: false , noHeaderFooter : true},
      children: [
        {
          path: '/ai/gallery',
          name: 'aiGallery',
          meta: { requireAuth: false , noHeaderFooter : true},
          component: aiGallery
        },
        {
          path: '/ai/plug',
          name: 'aiPlug',
          meta: { requireAuth: false , noHeaderFooter : true},
          component: aiPlug
        },
        {
          path: '/ai/chat',
          name: 'aiChat',
          meta: { requireAuth: false , noHeaderFooter : true},
          component: aiChat
        },
        {
          path: '/ai/render',
          name: 'aiRender',
          meta: { requireAuth: false, noHeaderFooter : true },
          component: aiRender
        },
        {
          path: '/ai/news',
          name: 'aiNews',
          meta: { requireAuth: false, noHeaderFooter : true },
          component: aiNews
        },
        {
          path: '/ai/news/:id',
          name: 'aiNewsDetail',
          meta: { requireAuth: false, noHeaderFooter : true },
          component: aiNewsDetail
        },
        {
          path: '/ai/vip',
          name: 'aiVip',
          meta: { requireAuth: false, noHeaderFooter : true },
          component: aiVip
        }
      ]
    },
    // user
    {
      path: '/user',
      name: 'user',
      meta: { requireAuth: true },
      component: userIndex,
      redirect: '/user/info',
      children: [
        {
          path: '/user/info',
          name: 'userInfo',
          meta: { requireAuth: true },
          component: userInfo
        },
        //投稿管理
        {
          path: '/user/contribute',
          name: 'contribute',
          meta: { requireAuth: true },
          component: contribute
        },
        {
          path: '/user/order',
          name: 'userOrder',
          meta: { requireAuth: true },
          component: userOrder
        }, {
          path: '/user/order/:id',
          name: 'userOrderDetail',
          meta: { requireAuth: true },
          component: userOrderDetail
        }, {
          path: '/user/course',
          name: 'userCourse',
          meta: { requireAuth: true },
          component: userCourse
        }, {
          path: '/user/certification',
          name: 'userCertification',
          meta: { requireAuth: true },
          component: userCertification
        }, {
          path: '/user/security',
          name: 'userSecurity',
          meta: { requireAuth: true },
          component: userSecurity
        }, {
          path: '/user/socialite',
          name: 'userSocialite',
          meta: { requireAuth: true },
          component: userSocialite
        }, {
          path: '/user/integral',
          name: 'userIntegral',
          meta: { requireAuth: true },
          component: userIntegral
        }, {
          path: '/user/message',
          name: 'message',
          meta: { requireAuth: true },
          component: message
        }, {
          path: '/user/address',
          name: 'address',
          meta: { requireAuth: true },
          component: address
        }
      ],
    }, {
      path: '/course',
      name: 'course',
      meta: { requireAuth: false },
      component: course
    }, {
      path: '/coursepage/:id',
      name: 'coursepage',
      meta: { requireAuth: false },
      component: coursepage
    }, {
      path: '/payment',
      name: 'payment',
      meta: { requireAuth: true },
      component: payment
    }, {
      path: '/payment-result',
      name: 'payment-result',
      component: complete
    },
    // 周边
    {
      path: '/periphery',
      name: 'periphery',
      meta: { requireAuth: false },
      component: periphery
    },
    {
      path: '/periphery/:id',
      name: 'peripheryDetails',
      meta: { requireAuth: false },
      component: peripheryDetails
    },
    // 搜索结果
    {
      path: '/search',
      name: 'search',
      meta: { requireAuth: false },
      component: search
    },
    // 

    {
      path: '/userfans',
      name: 'userfans',
      meta: { requireAuth: true },
      component: fansIndex,
      children: [
        {
          path: '/userfans/:id',
          name: 'userfans',
          meta: { requireAuth: false },
          component: userfans
        },

        {
          path: '/userfanslist/:id',
          name: 'userfanslist',
          meta: {
            requireAuth: false,
          },
          component: userfanslist,
        },

        {
          path: '/attentionlist/:id',
          name: 'attentionlist',
          meta: {
            requireAuth: false,
          },
          component: attentionlist,
        }
      ]
    }
  ],
  scrollBehavior(to, from, savedPosition) {
    return { x: 0, y: 0 }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  if (to.matched.some(record => record.meta.requireAuth)) {
    let token = localStorage.getItem('Authorization');
    if (token === null || token === '') {
      next('/login');
    } else {
      next();
    }
  } else {
    if (to.matched.some(record => record.meta.isAuthRoute)) {
      let token = localStorage.getItem('Authorization');
      if (token === null || token === '') {
        next();
      } else {
        next('/user/info');
      }
    } else {
      next();
    }
  }
});

export default router

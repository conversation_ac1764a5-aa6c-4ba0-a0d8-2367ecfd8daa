<template>
  <div id="aiapp" v-if="$route.meta.noHeaderFooter">
    <keep-alive>
      <router-view />
    </keep-alive>
  </div>
  <div id="app" v-else>
    <!-- 头部导航 -->
    <xgHeader></xgHeader>

    <keep-alive>
      <div id="v-content" v-bind:style="{ minHeight: Height + 'px' }">
        <router-view />
      </div>
    </keep-alive>

    <foot></foot>
  </div>
</template>
<script>
// 引入 头部 模块
import xgHeader from "./components/header.vue";
import foot from "./components/foot";
import login from "./components/auth/login";
export default {
  name: "App",
  components: {
    foot,
    login,
    xgHeader,
  },
  provide() {
    return {
      reload: this.reload,
    };
  },
  data() {
    return {
      Height: 0,
      isRouterAlive: true,
    };
  },
  mounted() {
    //动态设置内容高度 让footer始终居底   header+footer的高度是100
    this.Height = document.documentElement.clientHeight - 290;
    //监听浏览器窗口变化
    window.onresize = () => {
      this.Height = document.documentElement.clientHeight - 290;
    };
  },
  methods: {
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(function () {
        this.isRouterAlive = true;
      });
    },
  },
};
</script>

<style lang="scss">
#app {
  list-style: none;
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  /*background-color: #f5f5f5;*/
  background: rgba(219, 222, 227, 1);
}
ul,
li,
ol {
  list-style: none;
}
.zindex {
  z-index: 99999999999999999;
}

@media screen and (min-width: 751px) {
  #app{
    margin-top:84px;
  }
}
</style>

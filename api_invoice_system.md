# API端开票系统文档

## 概述

API端开票系统为用户提供完整的发票管理功能，包括发票信息管理、开票申请、充值记录查询等功能。

## 认证方式

所有接口都需要用户登录认证，在请求头中携带：
```
Authorization: Bearer {api_token}
```

## 接口列表

### 1. 发票信息管理

#### 1.1 获取发票信息列表

**接口地址：** `GET /api/invoice/info/list`

**请求参数：** 无

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "user_id": 123,
            "invoice_type": 1,
            "invoice_type_name": "电子普通发票",
            "invoice_content": "技术咨询服务",
            "header_type": 2,
            "header_type_name": "单位",
            "header_name": "北京科技有限公司",
            "tax_number": "91110000********9X",
            "registered_address": "北京市朝阳区xxx路xxx号",
            "registered_phone": "010-********",
            "bank_name": "中国银行北京分行",
            "bank_account": "********90********9",
            "is_default": 1,
            "created_at": "2024-01-01 10:00:00"
        }
    ]
}
```

#### 1.2 创建发票信息

**接口地址：** `POST /api/invoice/info/create`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| invoice_type | integer | 是 | 发票类型：1-电子普通发票，2-电子专票 | 1 |
| header_type | integer | 是 | 抬头类型：1-个人，2-单位 | 2 |
| header_name | string | 是 | 抬头名称 | 北京科技有限公司 |
| tax_number | string | 否* | 单位税号 | 91110000********9X |
| registered_address | string | 否* | 注册地址 | 北京市朝阳区xxx路xxx号 |
| registered_phone | string | 否* | 注册电话 | 010-******** |
| bank_name | string | 否* | 开户银行 | 中国银行北京分行 |
| bank_account | string | 否* | 银行账号 | ********90********9 |
| is_default | boolean | 否 | 是否设为默认 | true |

**注意：** 标记*的字段在以下情况为必填：
- 发票类型为电子专票(2)时
- 抬头类型为单位(2)时

**请求示例：**
```json
{
    "invoice_type": 1,
    "header_type": 2,
    "header_name": "北京科技有限公司",
    "tax_number": "91110000********9X",
    "registered_address": "北京市朝阳区xxx路xxx号",
    "registered_phone": "010-********",
    "bank_name": "中国银行北京分行",
    "bank_account": "********90********9",
    "is_default": true
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "发票信息创建成功",
    "data": {
        "id": 1,
        "user_id": 123,
        "invoice_type": 1,
        "header_type": 2,
        "header_name": "北京科技有限公司",
        "is_default": 1,
        "created_at": "2024-01-01 10:00:00"
    }
}
```

#### 1.3 更新发票信息

**接口地址：** `PUT /api/invoice/info/{id}`

**请求参数：** 同创建发票信息

**响应示例：**
```json
{
    "code": 200,
    "message": "发票信息更新成功",
    "data": {
        "id": 1,
        "user_id": 123,
        "invoice_type": 1,
        "header_type": 2,
        "header_name": "北京科技有限公司",
        "updated_at": "2024-01-01 11:00:00"
    }
}
```

#### 1.4 删除发票信息

**接口地址：** `DELETE /api/invoice/info/{id}`

**请求参数：** 无

**响应示例：**
```json
{
    "code": 200,
    "message": "发票信息删除成功",
    "data": null
}
```

### 2. 开票申请管理

#### 2.1 申请开票

**接口地址：** `POST /api/invoice/apply`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| recharge_order_id | integer | 是 | 充值订单ID | 123 |
| invoice_info_id | integer | 是 | 发票信息ID | 1 |

**请求示例：**
```json
{
    "recharge_order_id": 123,
    "invoice_info_id": 1
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "开票申请提交成功",
    "data": {
        "id": 1,
        "user_id": 123,
        "recharge_order_id": 123,
        "invoice_info_id": 1,
        "application_no": "INV20241201********9",
        "amount": "100.00",
        "status": 1,
        "status_name": "待开票",
        "created_at": "2024-01-01 10:00:00"
    }
}
```

#### 2.2 获取发票申请列表

**接口地址：** `GET /api/invoice/applications`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| limit | integer | 否 | 每页数量 | 20 |

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 1,
                "application_no": "INV20241201********9",
                "amount": "100.00",
                "status": 2,
                "status_name": "已开票",
                "invoice_file_url": "/storage/invoices/2024/01/01/invoice.pdf",
                "invoice_time": "2024-01-01 15:00:00",
                "created_at": "2024-01-01 10:00:00",
                "recharge_order": {
                    "id": 123,
                    "order_no": "RC20241201********9",
                    "order_amount": "100.00",
                    "pay_time": "2024-01-01 09:00:00"
                },
                "invoice_info": {
                    "id": 1,
                    "header_name": "北京科技有限公司",
                    "invoice_type_name": "电子普通发票"
                }
            }
        ],
        "total": 10
    }
}
```

#### 2.3 修改发票申请

**接口地址：** `PUT /api/invoice/applications/{id}`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| invoice_info_id | integer | 是 | 新的发票信息ID | 2 |

**注意：** 只有状态为"待开票"的申请才能修改

**响应示例：**
```json
{
    "code": 200,
    "message": "发票申请修改成功",
    "data": {
        "id": 1,
        "invoice_info_id": 2,
        "updated_at": "2024-01-01 11:00:00"
    }
}
```

#### 2.4 申请重开发票

**接口地址：** `POST /api/invoice/applications/{id}/reissue`

**请求参数：** 无

**注意：** 只有状态为"已开票"的申请才能重开

**响应示例：**
```json
{
    "code": 200,
    "message": "重开发票申请提交成功",
    "data": {
        "id": 1,
        "status": 3,
        "status_name": "已重开",
        "updated_at": "2024-01-01 16:00:00"
    }
}
```

### 3. 充值记录查询

#### 3.1 获取充值记录（用于开票）

**接口地址：** `GET /api/invoice/recharge-records`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| start_time | string | 否 | 开始时间 | 2024-01-01 00:00:00 |
| end_time | string | 否 | 结束时间 | 2024-12-31 23:59:59 |
| limit | integer | 否 | 每页数量 | 20 |

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 123,
                "order_no": "RC20241201********9",
                "num": 100,
                "order_amount": "100.00",
                "pay_time": "2024-01-01 09:00:00",
                "remark": "管理员充值",
                "invoice_application": {
                    "id": 1,
                    "status": 2,
                    "status_name": "已开票",
                    "application_no": "INV20241201********9"
                }
            },
            {
                "id": 124,
                "order_no": "RC20241201123456790",
                "num": 50,
                "order_amount": "50.00",
                "pay_time": "2024-01-02 09:00:00",
                "remark": "管理员充值",
                "invoice_application": null
            }
        ],
        "total": 20
    }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 参数错误 |
| 401 | 未授权（需要登录） |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 业务规则

### 发票类型规则
1. **电子普通发票**：可选择个人或单位抬头
2. **电子专票**：只能选择单位抬头，需要完整单位信息

### 必填字段规则
1. **个人抬头**：只需填写抬头名称（真实姓名）
2. **单位抬头**：需要填写税号、地址、电话、银行信息
3. **电子专票**：强制要求完整单位信息

### 开票流程
1. 用户创建发票信息
2. 选择充值记录申请开票
3. 提交申请后状态变为"待开票"
4. 管理员上传发票文件后状态变为"已开票"
5. 用户可申请重开发票

### 状态说明
- **待开票(1)**：申请已提交，等待管理员处理
- **已开票(2)**：管理员已上传发票文件
- **已重开(3)**：用户申请重开，等待管理员重新处理

## 注意事项

1. 每个充值订单只能申请一次开票
2. 只有已支付的充值订单才能申请开票
3. 发票信息被使用后不能删除
4. 只有待开票状态的申请才能修改
5. 只有已开票状态的申请才能重开
6. 设置默认发票信息会自动取消其他默认设置

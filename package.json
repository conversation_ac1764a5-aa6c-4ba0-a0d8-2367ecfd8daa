{"name": "my_light", "version": "1.0.0", "description": "A Vue.js project", "author": "lenovo <<EMAIL>>", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "unit": "jest --config test/unit/jest.conf.js --coverage", "e2e": "node test/e2e/runner.js", "test": "npm run unit && npm run e2e", "build": "node build/build.js"}, "dependencies": {"ali-oss": "^6.20.0", "animate.css": "^4.1.1", "axios": "^0.21.4", "bootstrap": "^5.1.1", "bootstrap-vue": "^2.21.2", "element-ui": "^2.15.6", "video.js": "^7.15.4", "vue": "^2.6.14", "vue-aliplayer": "^1.0.0", "vue-aliplayer-v2": "^1.3.0", "vue-awesome-swiper": "^3.1.3", "vue-clipboard2": "^0.3.3", "vue-i18n": "^9.10.2", "vue-property-decorator": "^9.1.2", "vue-quill-editor": "^3.0.6", "vue-router": "^3.0.1", "vue-video-player": "^5.0.2", "vuex": "^3.6.2"}, "devDependencies": {"autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-jest": "^21.0.2", "babel-loader": "^7.1.1", "babel-plugin-dynamic-import-node": "^1.2.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^2.0.1", "chromedriver": "^2.46.0", "copy-webpack-plugin": "^4.0.1", "cross-spawn": "^5.0.1", "css-loader": "^0.28.0", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "jest": "^22.0.4", "jest-serializer-vue": "^0.3.0", "jquery": "^3.6.0", "less": "^4.1.1", "less-loader": "^10 v .0.1", "nightwatch": "^0.9.12", "node-notifier": "^5.1.2", "node-sass": "^4.14.1", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "rimraf": "^2.6.0", "sass-loader": "^7.3.1", "selenium-server": "^3.0.1", "semver": "^5.3.0", "shelljs": "^0.7.6", "style-loader": "^3.2.1", "swiper": "^3.4.2", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-jest": "^1.0.2", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0", "wowjs": "^1.1.3"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}